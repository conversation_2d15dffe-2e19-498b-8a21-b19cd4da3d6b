{"id": "xwallet-pro-env", "name": "xWalletPro Environment", "values": [{"key": "baseUrl", "value": "https://api.xwalletpro.com", "enabled": true}, {"key": "authorization", "value": "", "enabled": true}, {"key": "securityToken", "value": "", "enabled": true}, {"key": "keyId", "value": "", "enabled": true}, {"key": "authToken", "value": "", "enabled": true}, {"key": "timestamp", "value": "", "enabled": true}, {"key": "random", "value": "", "enabled": true}, {"key": "signature", "value": "", "enabled": true}, {"key": "mobileAreaCode", "value": "+263", "enabled": true}, {"key": "mobileNo", "value": "", "enabled": true}, {"key": "password", "value": "", "enabled": true}, {"key": "bioPwd", "value": "", "enabled": true}, {"key": "registrationId", "value": "", "enabled": true}, {"key": "gesturePwd", "value": "", "enabled": true}, {"key": "systemVersion", "value": "17.0", "enabled": true}], "_postman_variable_scope": "environment"}