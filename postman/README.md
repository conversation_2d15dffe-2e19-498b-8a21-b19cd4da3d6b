# xWalletPro Postman Collection

This Postman collection provides comprehensive API testing for the xWalletPro iOS application, strictly following the Wallet-Personal.html API documentation.

## Features

- **Compact JSON Envelope**: All requests use the required envelope structure with timestamp, random, reqSource, signMethod, platform, language, etc.
- **Automatic Signature Generation**: Pre-request scripts automatically generate MD5(uppercase(SHA256(JSON))) signatures in uppercase format
- **Environment Variables**: Comprehensive environment setup for easy configuration
- **Token Management**: Automatic extraction and storage of authentication tokens

## Setup Instructions

### 1. Import Collection and Environment

1. Import `collections/xWalletPro.postman_collection.json` into Postman
2. Import `environments/xWalletPro.postman_environment.json` into Postman
3. Select the "xWalletPro Environment" in Postman

### 2. Configure Environment Variables

Update the following variables in your environment:

#### Required Variables
- `baseUrl`: API base URL (default: https://api.xwalletpro.com)
- `mobileAreaCode`: Your mobile area code (default: +263)
- `mobileNo`: Your mobile number
- `password`: Your encrypted password
- `registrationId`: Aurora registration ID

#### Optional Variables
- `systemVersion`: iOS system version (default: 17.0)
- `bioPwd`: Biometric password (if using fingerprint login)
- `gesturePwd`: Gesture password (if using gesture login)

#### Auto-Generated Variables (Do Not Modify)
- `keyId`: Encryption key ID (auto-populated from Get Public Key)
- `authToken`: Authentication token (auto-populated from Login)
- `authorization`: Bearer token (auto-populated from Login)
- `timestamp`: Request timestamp (auto-generated)
- `random`: Random UUID (auto-generated)
- `signature`: Request signature (auto-generated)

## Usage Workflow

### 1. Get Public Key
Run the "Get Public Key" request first to obtain the encryption key ID:
- Automatically generates signature for empty request body
- Saves `keyId` to environment variables for subsequent requests

### 2. Login
Run the "Login" request to authenticate:
- Uses the `keyId` from step 1
- Automatically generates signature with full request envelope
- Saves authentication token to environment variables
- Requires: `mobileAreaCode`, `mobileNo`, `password`, `registrationId`

### 3. Use Authenticated Endpoints
All other endpoints will use the saved authentication token automatically.

## Request Structure

All requests follow the compact JSON envelope format as specified in the API documentation:

```json
{
  "timestamp": "1640995200000",
  "random": "uuid-string",
  "reqSource": "iOS",
  "signMethod": "MD5",
  "keyId": "encryption-key-id",
  "platform": "iOS",
  "appVersion": "1.0.0",
  "brand": "Apple",
  "series": "iPhone",
  "systemVersion": "17.0",
  "language": "en",
  // ... endpoint-specific parameters
  "signature": "MD5_HASH_UPPERCASE"
}
```

## Signature Generation

The signature is automatically generated using the following algorithm:
1. Create JSON request body (without signature field)
2. Generate SHA256 hash of the JSON string
3. Convert SHA256 hash to uppercase
4. Generate MD5 hash of the uppercase SHA256
5. Convert MD5 hash to uppercase

This matches the API specification: `MD5(uppercase(SHA256(JSON)))` in uppercase.

## Authentication

The collection supports multiple authentication methods:
- **Password Login** (`passwordType: "1"`): Standard password authentication
- **Fingerprint Login** (`passwordType: "2"`): Biometric authentication
- **Gesture Login** (`passwordType: "3"`): Gesture pattern authentication

## Error Handling

All requests include proper error handling and will log relevant information to the Postman console for debugging.

## Security Notes

- Never commit actual credentials to version control
- Use environment variables for all sensitive data
- The signature generation ensures request integrity
- All sensitive data should be properly encrypted before transmission

## Troubleshooting

### Common Issues

1. **Missing keyId**: Ensure you run "Get Public Key" first
2. **Invalid signature**: Check that all required fields are present in the request body
3. **Authentication failed**: Verify your credentials in environment variables
4. **Missing token**: Ensure "Login" request completed successfully

### Debug Information

Check the Postman console for:
- Generated signatures
- Saved tokens and key IDs
- Request/response details
- Error messages

## API Documentation Reference

This collection strictly follows the specifications in `Wallet-Personal.html`. Refer to that document for:
- Complete endpoint documentation
- Request/response schemas
- Error codes and messages
- Business logic requirements
