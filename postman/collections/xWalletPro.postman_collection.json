{"info": {"name": "xWalletPro", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "<PERSON><PERSON>", "item": [{"name": "Get Public Key", "event": [{"listen": "pre-request", "script": {"exec": ["// Generate signature for Get Public Key request", "const timestamp = new Date().getTime().toString();", "const random = pm.variables.replaceIn('{{$randomUUID}}');", "", "// Create empty request body for public key endpoint", "const requestBody = {};", "", "// Convert to JSON string", "const jsonString = JSON.stringify(requestBody);", "", "// Generate SHA256 hash", "const sha256Hash = CryptoJS.SHA256(jsonString).toString(CryptoJS.enc.Hex);", "", "// Convert to uppercase and generate MD5", "const signature = CryptoJS.MD5(sha256Hash.toUpperCase()).toString(CryptoJS.enc.Hex).toUpperCase();", "", "// Set variables for use in request", "pm.environment.set('timestamp', timestamp);", "pm.environment.set('random', random);", "pm.environment.set('signature', signature);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// Save the keyId from response for use in subsequent requests", "if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    if (responseJson.success && responseJson.data && responseJson.data.keyId) {", "        pm.environment.set('keyId', responseJson.data.keyId);", "        console.log('KeyId saved:', responseJson.data.keyId);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/encrypt/public/key", "host": ["{{baseUrl}}"], "path": ["encrypt", "public", "key"]}}}, {"name": "<PERSON><PERSON>", "event": [{"listen": "pre-request", "script": {"exec": ["// Generate signature according to API docs: MD5(uppercase(SHA256(JSON))) in uppercase", "const timestamp = new Date().getTime().toString();", "const random = pm.variables.replaceIn('{{$randomUUID}}');", "", "// Create the request body without signature", "const requestBody = {", "    timestamp: timestamp,", "    random: random,", "    reqSource: 'iOS',", "    signMethod: 'MD5',", "    keyId: pm.environment.get('keyId') || '',", "    platform: 'iOS',", "    appVersion: '1.0.0',", "    brand: 'Apple',", "    series: 'iPhone',", "    systemVersion: pm.environment.get('systemVersion') || '17.0',", "    language: 'en',", "    mobileAreaCode: pm.environment.get('mobileAreaCode') || '+263',", "    mobileNo: pm.environment.get('mobileNo') || '',", "    passwordType: '1',", "    password: pm.environment.get('password') || '',", "    bioPwd: pm.environment.get('bioPwd') || '',", "    registrationId: pm.environment.get('registrationId') || '',", "    gesturePwd: pm.environment.get('gesturePwd') || ''", "};", "", "// Convert to JSON string", "const jsonString = JSON.stringify(requestBody);", "", "// Generate SHA256 hash", "const sha256Hash = CryptoJS.SHA256(jsonString).toString(CryptoJS.enc.Hex);", "", "// Convert to uppercase and generate MD5", "const signature = CryptoJS.MD5(sha256Hash.toUpperCase()).toString(CryptoJS.enc.Hex).toUpperCase();", "", "// Set variables for use in request", "pm.environment.set('timestamp', timestamp);", "pm.environment.set('random', random);", "pm.environment.set('signature', signature);"], "type": "text/javascript"}}, {"listen": "test", "script": {"exec": ["// Save authentication token from login response", "if (pm.response.code === 200) {", "    const responseJson = pm.response.json();", "    if (responseJson.success && responseJson.data && responseJson.data.token) {", "        pm.environment.set('authToken', responseJson.data.token);", "        pm.environment.set('authorization', 'Bearer ' + responseJson.data.token);", "        console.log('Auth token saved:', responseJson.data.token);", "    }", "}"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "Authorization", "value": "{{authorization}}", "disabled": false}], "body": {"mode": "raw", "raw": "{\n  \"timestamp\": \"{{timestamp}}\",\n  \"random\": \"{{random}}\",\n  \"reqSource\": \"iOS\",\n  \"signMethod\": \"MD5\",\n  \"keyId\": \"{{keyId}}\",\n  \"platform\": \"iOS\",\n  \"appVersion\": \"1.0.0\",\n  \"brand\": \"Apple\",\n  \"series\": \"iPhone\",\n  \"systemVersion\": \"{{systemVersion}}\",\n  \"language\": \"en\",\n  \"mobileAreaCode\": \"{{mobileAreaCode}}\",\n  \"mobileNo\": \"{{mobileNo}}\",\n  \"passwordType\": \"1\",\n  \"password\": \"{{password}}\",\n  \"bioPwd\": \"{{bioPwd}}\",\n  \"registrationId\": \"{{registrationId}}\",\n  \"gesturePwd\": \"{{gesturePwd}}\",\n  \"signature\": \"{{signature}}\"\n}"}, "url": {"raw": "{{baseUrl}}/user/login", "host": ["{{baseUrl}}"], "path": ["user", "login"]}}}, {"name": "Auth Check", "event": [{"listen": "pre-request", "script": {"exec": ["// Generate signature for Auth Check request", "const timestamp = new Date().getTime().toString();", "const random = pm.variables.replaceIn('{{$randomUUID}}');", "", "// Create the request body with envelope structure", "const requestBody = {", "    timestamp: timestamp,", "    random: random,", "    reqSource: 'iOS',", "    signMethod: 'MD5',", "    keyId: pm.environment.get('keyId') || '',", "    platform: 'iOS',", "    appVersion: '1.0.0',", "    brand: 'Apple',", "    series: 'iPhone',", "    systemVersion: pm.environment.get('systemVersion') || '17.0',", "    language: 'en'", "};", "", "// Convert to JSON string", "const jsonString = JSON.stringify(requestBody);", "", "// Generate SHA256 hash", "const sha256Hash = CryptoJS.SHA256(jsonString).toString(CryptoJS.enc.Hex);", "", "// Convert to uppercase and generate MD5", "const signature = CryptoJS.MD5(sha256Hash.toUpperCase()).toString(CryptoJS.enc.Hex).toUpperCase();", "", "// Set variables for use in request", "pm.environment.set('timestamp', timestamp);", "pm.environment.set('random', random);", "pm.environment.set('signature', signature);"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{\n  \"timestamp\": \"{{timestamp}}\",\n  \"random\": \"{{random}}\",\n  \"reqSource\": \"iOS\",\n  \"signMethod\": \"MD5\",\n  \"keyId\": \"{{keyId}}\",\n  \"platform\": \"iOS\",\n  \"appVersion\": \"1.0.0\",\n  \"brand\": \"Apple\",\n  \"series\": \"iPhone\",\n  \"systemVersion\": \"{{systemVersion}}\",\n  \"language\": \"en\",\n  \"signature\": \"{{signature}}\"\n}"}, "url": {"raw": "{{baseUrl}}/user/auth/check", "host": ["{{baseUrl}}"], "path": ["user", "auth", "check"]}}}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/logout", "host": ["{{baseUrl}}"], "path": ["logout"]}}}]}, {"name": "User", "item": [{"name": "Get User Info", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/user/getUserInfo", "host": ["{{baseUrl}}"], "path": ["user", "getUserInfo"]}}}, {"name": "Modify User Info", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/user/modifyUserInfo", "host": ["{{baseUrl}}"], "path": ["user", "modifyUserInfo"]}}}, {"name": "Login Password Setup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/user/login/password/setup", "host": ["{{baseUrl}}"], "path": ["user", "login", "password", "setup"]}}}, {"name": "Login Password Change", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/user/login/password/change", "host": ["{{baseUrl}}"], "path": ["user", "login", "password", "change"]}}}, {"name": "PIN Validate", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/user/pin/validate", "host": ["{{baseUrl}}"], "path": ["user", "pin", "validate"]}}}, {"name": "Bio Lock Status Modify", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/user/modifyBioLockStatus", "host": ["{{baseUrl}}"], "path": ["user", "modifyBioLockStatus"]}}}]}, {"name": "SMS", "item": [{"name": "Get SMS Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/code/sms/get", "host": ["{{baseUrl}}"], "path": ["code", "sms", "get"]}}}, {"name": "Validate SMS Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/code/sms/validate", "host": ["{{baseUrl}}"], "path": ["code", "sms", "validate"]}}}]}, {"name": "Accounts", "item": [{"name": "Balance", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/subjectInf/balance", "host": ["{{baseUrl}}"], "path": ["subjectInf", "balance"]}}}, {"name": "Activate Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/subjectInf/activeAccount", "host": ["{{baseUrl}}"], "path": ["subjectInf", "activeAccount"]}}}]}, {"name": "Transfer", "item": [{"name": "Latest 5 Transfer Users", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/perTransfer/latest5TransferUser", "host": ["{{baseUrl}}"], "path": ["perTransfer", "latest5TransferUser"]}}}, {"name": "Get By Mobile", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/user/getByMobile", "host": ["{{baseUrl}}"], "path": ["user", "getByMobile"]}}}, {"name": "Check Transfer To PerAcc", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/perTransfer/transferToPerAccCheck", "host": ["{{baseUrl}}"], "path": ["perTransfer", "transferToPerAccCheck"]}}}, {"name": "Transfer To PerAcc", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/perTransfer/transferToPerAcc", "host": ["{{baseUrl}}"], "path": ["perTransfer", "transferToPerAcc"]}}}, {"name": "Transfer Records By Mobile", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/perTransfer/transferRecordsByMobile", "host": ["{{baseUrl}}"], "path": ["perTransfer", "transferRecordsByMobile"]}}}]}, {"name": "Contact", "item": [{"name": "List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/contact/list", "host": ["{{baseUrl}}"], "path": ["contact", "list"]}}}, {"name": "Delete", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/contact/delete", "host": ["{{baseUrl}}"], "path": ["contact", "delete"]}}}, {"name": "Add", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/contact/add", "host": ["{{baseUrl}}"], "path": ["contact", "add"]}}}, {"name": "Is Contact Change", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/contact/isContactChange", "host": ["{{baseUrl}}"], "path": ["contact", "isContactChange"]}}}]}, {"name": "QRCode", "item": [{"name": "Get", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/qrCode/get", "host": ["{{baseUrl}}"], "path": ["qrCode", "get"]}}}, {"name": "Analyze", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/qrCode/analyze", "host": ["{{baseUrl}}"], "path": ["qrCode", "analyze"]}}}, {"name": "Pay Code Get", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/qrCode/pay/get", "host": ["{{baseUrl}}"], "path": ["qrCode", "pay", "get"]}}}, {"name": "Pay Code Check", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/qrCode/pay/check", "host": ["{{baseUrl}}"], "path": ["qrCode", "pay", "check"]}}}, {"name": "QR Analyze (All Types)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/qrCode/qrAnalyze", "host": ["{{baseUrl}}"], "path": ["qrCode", "qrAnalyze"]}}}, {"name": "Available Currencies", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/subjectInf/availableCurrencies", "host": ["{{baseUrl}}"], "path": ["subjectInf", "availableCurrencies"]}}}]}, {"name": "Bills & Orders", "item": [{"name": "Historical Bills", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/orderRecord/list", "host": ["{{baseUrl}}"], "path": ["orderRecord", "list"]}}}, {"name": "Order Detail", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/orderRecord/detail", "host": ["{{baseUrl}}"], "path": ["orderRecord", "detail"]}}}, {"name": "Wallet Enquiry Pay", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/subjectInf/walletEnquiryPay", "host": ["{{baseUrl}}"], "path": ["subjectInf", "walletEnquiryPay"]}}}, {"name": "Check Record Pay (pre)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/orderRecord/queryOrderRecordPayCheck", "host": ["{{baseUrl}}"], "path": ["orderRecord", "queryOrderRecordPayCheck"]}}}, {"name": "Record Pay", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/orderRecord/queryOrderRecordPay", "host": ["{{baseUrl}}"], "path": ["orderRecord", "queryOrderRecordPay"]}}}]}, {"name": "Card & TopUp", "item": [{"name": "CST Card Count", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/perTopUp/bankNumQuery", "host": ["{{baseUrl}}"], "path": ["perTopUp", "bankNumQuery"]}}}, {"name": "CST Card List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/cstCardRecord/list", "host": ["{{baseUrl}}"], "path": ["cstCardRecord", "list"]}}}, {"name": "CST Card Add", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/cstCardRecord/add", "host": ["{{baseUrl}}"], "path": ["cstCardRecord", "add"]}}}, {"name": "CST Card Delete", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/cstCardRecord/del", "host": ["{{baseUrl}}"], "path": ["cstCardRecord", "del"]}}}, {"name": "TopUp Check", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/perTopUp/check", "host": ["{{baseUrl}}"], "path": ["perTopUp", "check"]}}}, {"name": "TopUp Account", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/perTopUp/topUpAcc", "host": ["{{baseUrl}}"], "path": ["perTopUp", "topUpAcc"]}}}]}, {"name": "Split Bill", "item": [{"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/splitBill/createOrder", "host": ["{{baseUrl}}"], "path": ["splitBill", "createOrder"]}}}, {"name": "Pay Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/splitBill/payOrder", "host": ["{{baseUrl}}"], "path": ["splitBill", "payOrder"]}}}, {"name": "Pending Rec List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/splitBill/recList", "host": ["{{baseUrl}}"], "path": ["splitBill", "recList"]}}}, {"name": "Pending Pay List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/splitBill/payList", "host": ["{{baseUrl}}"], "path": ["splitBill", "payList"]}}}, {"name": "Pending Re<PERSON>ail", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/splitBill/recDetail", "host": ["{{baseUrl}}"], "path": ["splitBill", "recDetail"]}}}, {"name": "Pending Pay Detail", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/splitBill/payDetail", "host": ["{{baseUrl}}"], "path": ["splitBill", "payDetail"]}}}, {"name": "Close", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/splitBill/close", "host": ["{{baseUrl}}"], "path": ["splitBill", "close"]}}}]}, {"name": "Payment & Checkout", "item": [{"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/pay/createOrder", "host": ["{{baseUrl}}"], "path": ["pay", "createOrder"]}}}, {"name": "Pay Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/pay/payOrder", "host": ["{{baseUrl}}"], "path": ["pay", "payOrder"]}}}, {"name": "Merchant Code Check", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/pay/merchantCodeCheck", "host": ["{{baseUrl}}"], "path": ["pay", "merchantCodeCheck"]}}}, {"name": "Create Mer Code Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/pay/createMerCodeOrder", "host": ["{{baseUrl}}"], "path": ["pay", "createMerCodeOrder"]}}}, {"name": "Checkout Order Get", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/pay/order/get", "host": ["{{baseUrl}}"], "path": ["pay", "order", "get"]}}}, {"name": "Checkout Payment Types", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/checkOutCounter/payment/type/get", "host": ["{{baseUrl}}"], "path": ["checkOutCounter", "payment", "type", "get"]}}}, {"name": "Marketing Info Query", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/api/marketing/marketingInfoQuery", "host": ["{{baseUrl}}"], "path": ["api", "marketing", "marketingInfoQuery"]}}}, {"name": "Marketing Info Change", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/api/marketing/marketingInfoChange", "host": ["{{baseUrl}}"], "path": ["api", "marketing", "marketingInfoChange"]}}}]}, {"name": "Bank Cards", "item": [{"name": "Withdraw Card List (deprecated)", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/cstCardRecord/cardList", "host": ["{{baseUrl}}"], "path": ["cstCardRecord", "cardList"]}}}, {"name": "Bank List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/cstBank/bankList", "host": ["{{baseUrl}}"], "path": ["cstBank", "bankList"]}}}, {"name": "Bank Accounts", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/cstBank/cstBankListQuery", "host": ["{{baseUrl}}"], "path": ["cstBank", "cstBankListQuery"]}}}, {"name": "Deduction Sort List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/user/deduction/sort/list", "host": ["{{baseUrl}}"], "path": ["user", "deduction", "sort", "list"]}}}, {"name": "Deduction Sort Config", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/user/deduction/sort/config", "host": ["{{baseUrl}}"], "path": ["user", "deduction", "sort", "config"]}}}]}, {"name": "<PERSON><PERSON><PERSON>", "item": [{"name": "Last Withdrawal Card", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/withdrawal/lastWithdrawalCard", "host": ["{{baseUrl}}"], "path": ["withdrawal", "lastWithdrawalCard"]}}}, {"name": "Create Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/withdrawal/createOrder", "host": ["{{baseUrl}}"], "path": ["withdrawal", "createOrder"]}}}, {"name": "Check", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/withdrawal/check", "host": ["{{baseUrl}}"], "path": ["withdrawal", "check"]}}}]}, {"name": "Fee", "item": [{"name": "Calculate", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/fee/calculate", "host": ["{{baseUrl}}"], "path": ["fee", "calculate"]}}}]}, {"name": "Notifications", "item": [{"name": "Check New", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/user/notice/checkNew", "host": ["{{baseUrl}}"], "path": ["user", "notice", "checkNew"]}}}, {"name": "List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/user/notice/list", "host": ["{{baseUrl}}"], "path": ["user", "notice", "list"]}}}]}, {"name": "Invite Friends", "item": [{"name": "Check Activity", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/inviteActivity/checkActivity", "host": ["{{baseUrl}}"], "path": ["inviteActivity", "checkActivity"]}}}, {"name": "Get Invite Link", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/inviteFriends/get", "host": ["{{baseUrl}}"], "path": ["inviteFriends", "get"]}}}, {"name": "Register by Invite", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/inviteFriends/register", "host": ["{{baseUrl}}"], "path": ["inviteFriends", "register"]}}}, {"name": "Invite: <PERSON> Login Password", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/inviteFriends/login/password/setup", "host": ["{{baseUrl}}"], "path": ["inviteFriends", "login", "password", "setup"]}}}]}, {"name": "<PERSON><PERSON>", "item": [{"name": "Device List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/user/device/list", "host": ["{{baseUrl}}"], "path": ["user", "device", "list"]}}}, {"name": "Send Auth Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/device/authCode/send", "host": ["{{baseUrl}}"], "path": ["user", "device", "authCode", "send"]}}}, {"name": "Validate Auth Code", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/device/authCode/validate", "host": ["{{baseUrl}}"], "path": ["user", "device", "authCode", "validate"]}}}, {"name": "Auth Code List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/user/device/authCode/list", "host": ["{{baseUrl}}"], "path": ["user", "device", "authCode", "list"]}}}, {"name": "Change Master Device", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/user/device/changeMasterDevice", "host": ["{{baseUrl}}"], "path": ["user", "device", "changeMasterDevice"]}}}]}, {"name": "Biller & Airtime/Bundle", "item": [{"name": "ZESA Meter No Query", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/perZesa/meterNoQuery", "host": ["{{baseUrl}}"], "path": ["perZesa", "meter<PERSON><PERSON><PERSON><PERSON>y"]}}}, {"name": "ZESA Amount Limit", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/user/system/amountLimitByZesa", "host": ["{{baseUrl}}"], "path": ["user", "system", "amountLimitByZesa"]}}}, {"name": "ZESA Token Purchase", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/perZesa/tokenPurchase", "host": ["{{baseUrl}}"], "path": ["perZesa", "tokenPurchase"]}}}, {"name": "ZESA Biller Order List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/perZesa/zesaBillerOrderList", "host": ["{{baseUrl}}"], "path": ["perZesa", "zesaBillerOrderList"]}}}, {"name": "ZESA Resend Token", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/perZesa/resendToken", "host": ["{{baseUrl}}"], "path": ["perZesa", "resendToken"]}}}, {"name": "Biller Code Check", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/perBillPayment/billerCodeCheck", "host": ["{{baseUrl}}"], "path": ["perBillPayment", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}}}, {"name": "Biller Create Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/perBillPayment/createOrder", "host": ["{{baseUrl}}"], "path": ["perBillPayment", "createOrder"]}}}, {"name": "Biller Pay Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/perBillPayment/payOrder", "host": ["{{baseUrl}}"], "path": ["perBillPayment", "payOrder"]}}}, {"name": "Airtime List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/bundle/query/airtime/list", "host": ["{{baseUrl}}"], "path": ["bundle", "query", "airtime", "list"]}}}, {"name": "Bundle Product Type List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/bundle/query/product/type", "host": ["{{baseUrl}}"], "path": ["bundle", "query", "product", "type"]}}}, {"name": "Bundle Product List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/bundle/query/page", "host": ["{{baseUrl}}"], "path": ["bundle", "query", "page"]}}}, {"name": "App Bundle Product List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/bundle/query/app/list", "host": ["{{baseUrl}}"], "path": ["bundle", "query", "app", "list"]}}}, {"name": "Airtime/Bundle Buy Apply", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/perBuyAirtimeOrBundle/apply", "host": ["{{baseUrl}}"], "path": ["perBuyAirtimeOrBundle", "apply"]}}}, {"name": "Airtime/Bundle Create Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/perBuyAirtimeOrBundle/createOrder", "host": ["{{baseUrl}}"], "path": ["perBuyAirtimeOrBundle", "createOrder"]}}}, {"name": "Airtime/Bundle Pay Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/perBuyAirtimeOrBundle/payOrder", "host": ["{{baseUrl}}"], "path": ["perBuyAirtimeOrBundle", "payOrder"]}}}]}, {"name": "Junior Account", "item": [{"name": "Get User Info By Id", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/user/getUserInfoById", "host": ["{{baseUrl}}"], "path": ["user", "getUserInfoById"]}}}, {"name": "List", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/juniorAccount/list", "host": ["{{baseUrl}}"], "path": ["juniorAccount", "list"]}}}, {"name": "Apply Check", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/juniorAccount/applyCheck", "host": ["{{baseUrl}}"], "path": ["juniorAccount", "<PERSON><PERSON><PERSON><PERSON>"]}}}, {"name": "Apply", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/juniorAccount/apply", "host": ["{{baseUrl}}"], "path": ["juniorAccount", "apply"]}}}, {"name": "Check ID & Name", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/juniorAccount/checkIdAndName", "host": ["{{baseUrl}}"], "path": ["juniorAccount", "checkIdAndName"]}}}, {"name": "Update PIN", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/juniorAccount/updatePin", "host": ["{{baseUrl}}"], "path": ["juniorAccount", "updatePin"]}}}, {"name": "Upgrade Check", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/juniorAccount/upgradeCheck", "host": ["{{baseUrl}}"], "path": ["juniorAccount", "upgradeCheck"]}}}, {"name": "Upgrade", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/juniorAccount/upgrade", "host": ["{{baseUrl}}"], "path": ["juniorAccount", "upgrade"]}}}, {"name": "Delete Check", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/juniorAccount/deleteCheck", "host": ["{{baseUrl}}"], "path": ["juniorAccount", "deleteCheck"]}}}, {"name": "Delete", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/juniorAccount/delete", "host": ["{{baseUrl}}"], "path": ["juniorAccount", "delete"]}}}, {"name": "Balance Pay", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/juniorSubjectInf/walletEnquiryPay", "host": ["{{baseUrl}}"], "path": ["juniorSubjectInf", "walletEnquiryPay"]}}}, {"name": "Query Order Record Pay", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/juniorSubjectInf/queryOrderRecordPay", "host": ["{{baseUrl}}"], "path": ["juniorSubjectInf", "queryOrderRecordPay"]}}}]}, {"name": "Zipit & Cash Out", "item": [{"name": "User Z<PERSON>it Lookup", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/user/zipit/lookup", "host": ["{{baseUrl}}"], "path": ["user", "zipit", "lookup"]}}}, {"name": "User Zipit Check", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/user/zipit/check", "host": ["{{baseUrl}}"], "path": ["user", "zipit", "check"]}}}, {"name": "User Zipit Create Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/user/zipit/createOrder", "host": ["{{baseUrl}}"], "path": ["user", "zipit", "createOrder"]}}}, {"name": "Cash Out Remittance Code Check", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/cashOut/remittanceCodeCheck", "host": ["{{baseUrl}}"], "path": ["cashOut", "remittanceCodeCheck"]}}}, {"name": "Cash Out Check", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "url": {"raw": "{{baseUrl}}/cashOut/check", "host": ["{{baseUrl}}"], "path": ["cashOut", "check"]}}}, {"name": "Cash Out Create Order", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "security-token", "value": "{{securityToken}}"}], "body": {"mode": "raw", "raw": "{}"}, "url": {"raw": "{{baseUrl}}/cashOut/createOrder", "host": ["{{baseUrl}}"], "path": ["cashOut", "createOrder"]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["(function() {", "  function isObject(x) { return x && typeof x === 'object' && !Array.isArray(x); }", "  function sortObject(obj) {", "    if (Array.isArray(obj)) return obj.map(sortObject);", "    if (!isObject(obj)) return obj;", "    const keys = Object.keys(obj).sort();", "    const out = {};", "    for (var i = 0; i < keys.length; i++) { var k = keys[i]; out[k] = sortObject(obj[k]); }", "    return out;", "  }", "  function ensureRawBody() {", "    if (!pm.request.body || pm.request.body.mode !== 'raw') {", "      pm.request.body = pm.request.body || {};", "      pm.request.body.mode = 'raw';", "      pm.request.body.raw = '{}';", "    }", "  }", "  function genRandom(len) {", "    len = len || 16;", "    var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';", "    var s = '';", "    for (var i = 0; i < len; i++) s += chars.charAt(Math.floor(Math.random() * chars.length));", "    return s;", "  }", "  ensureRawBody();", "  var raw = (pm.request.body && pm.request.body.raw) ? pm.request.body.raw : '{}';", "  var dataFields = {};", "  try { dataFields = raw ? JSON.parse(raw) : {}; } catch (e) { dataFields = {}; }", "  var env = pm.environment;", "  var envelope = {", "    timestamp: (Date.now()).toString(),", "    random: <PERSON><PERSON><PERSON><PERSON><PERSON>(24),", "    reqSource: env.get('reqSource') || '3',", "    signMethod: env.get('signMethod') || 'SHA256-MD5',", "    platform: env.get('platform') || 'iOS',", "    appVersion: env.get('appVersion') || '1.0.0',", "    brand: env.get('brand') || 'Apple',", "    series: env.get('series') || 'iPhone',", "    systemVersion: env.get('systemVersion') || '17.0',", "    language: env.get('language') || 'en'", "  };", "  var keyId = env.get('keyId'); if (keyId !== undefined && keyId !== null && keyId !== '') envelope.keyId = keyId;", "  var registrationId = env.get('registrationId'); if (registrationId !== undefined && registrationId !== null && registrationId !== '') envelope.registrationId = registrationId;", "  var merged = Object.assign({}, envelope, dataFields);", "  var sigField = env.get('signatureFieldName') || 'signature';", "  try {", "    var signingObj = Object.assign({}, merged);", "    delete signingObj[sigField];", "    var sorted = sortObject(signingObj);", "    var compact = JSON.stringify(sorted);", "    var sha256Hex = CryptoJS.SHA256(compact).toString(CryptoJS.enc.Hex).toUpperCase();", "    var md5Hex = CryptoJS.MD5(sha256Hex).toString(CryptoJS.enc.Hex).toUpperCase();", "    merged[sigField] = md5Hex;", "    pm.request.body.raw = JSON.stringify(merged);", "    pm.variables.set('lastSignedPayload', pm.request.body.raw);", "  } catch (e) {", "    // If signing fails, still send the envelope + existing fields", "    pm.request.body.raw = JSON.stringify(merged);", "  }", "})();"]}}], "variable": [{"key": "baseUrl", "value": ""}, {"key": "authorization", "value": ""}, {"key": "securityToken", "value": ""}, {"key": "language", "value": "en"}, {"key": "reqSource", "value": "3"}, {"key": "sign<PERSON>ethod", "value": "SHA256-MD5"}, {"key": "platform", "value": "iOS"}, {"key": "appVersion", "value": "1.0.0"}, {"key": "brand", "value": "Apple"}, {"key": "series", "value": "iPhone"}, {"key": "systemVersion", "value": "17.0"}, {"key": "keyId", "value": ""}, {"key": "registrationId", "value": ""}, {"key": "signatureFieldName", "value": "signature"}]}