package om.rrtx.mobile.rrtxcommon1.base;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Configuration;
import android.content.res.Resources;
import android.os.Bundle;
import android.view.MotionEvent;
import android.view.View;
import android.view.WindowManager;
import android.view.inputmethod.InputMethodManager;
import android.widget.EditText;

import androidx.appcompat.app.AppCompatActivity;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.scwang.smartrefresh.layout.footer.ClassicsFooter;
import com.scwang.smartrefresh.layout.header.ClassicsHeader;

import butterknife.ButterKnife;
import cn.bingoogolapple.swipebacklayout.BGASwipeBackHelper;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.R;
import om.rrtx.mobile.rrtxcommon1.receiver.RefreshPagerReceive;
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController;
import om.rrtx.mobile.rrtxcommon1.utils.AdaptScreenUtils;
import om.rrtx.mobile.rrtxcommon1.utils.LocaleManager;

/**
 * 作者: *贺金龙
 * 创建时间: *  2017/10/16 13:17
 * 类描述: *
 * 修改人: *
 * 修改内容: *
 * 修改时间: *
 * 类说明: *
 *  
 */
public abstract class BaseSuperActivity<V, P extends BasePresenter<V>> extends AppCompatActivity implements BGASwipeBackHelper.Delegate {

    public Activity mContext;
    protected P mPresenter;

    public static final int designWidthInpx = 750;
    private BGASwipeBackHelper mSwipeBackHelper;
    /**
     * 本地广播
     */
    private LocalBroadcastManager mLocalBroadcastManager;
    private RefreshPagerReceive mRefreshBanksReceive;

    /**
     * 初始化刷新的控件
     */
    private void initRefresh() {
        // ClassicsHeader.REFRESH_HEADER_PULLING = getString(R.string.refresh_header_pulling);
        ClassicsHeader.REFRESH_HEADER_REFRESHING = getString(R.string.refresh_header_refreshing);
        ClassicsHeader.REFRESH_HEADER_LOADING = getString(R.string.refresh_header_loading);
        ClassicsHeader.REFRESH_HEADER_RELEASE = getString(R.string.refresh_header_release);
        ClassicsHeader.REFRESH_HEADER_FINISH = getString(R.string.refresh_header_finish);
        ClassicsHeader.REFRESH_HEADER_FAILED = getString(R.string.refresh_header_failed);
        ClassicsHeader.REFRESH_HEADER_LASTTIME = getString(R.string.refresh_header_update);
        ClassicsHeader.REFRESH_HEADER_PULLDOWN = getString(R.string.refresh_header_pulling);

        //"上拉加载更多";
        // ClassicsFooter.REFRESH_FOOTER_PULLING = getString(R.string.refresh_footer_pulling);
        //"释放立即加载";
        ClassicsFooter.REFRESH_FOOTER_RELEASE = getString(R.string.refresh_footer_release);
        //"正在刷新...";
        ClassicsFooter.REFRESH_FOOTER_LOADING = getString(R.string.refresh_footer_loading);
        //"正在加载...";
        ClassicsFooter.REFRESH_FOOTER_REFRESHING = getString(R.string.refresh_footer_refreshing);
        //"加载完成";
        ClassicsFooter.REFRESH_FOOTER_FINISH = getString(R.string.refresh_footer_finish);
        //"加载失败";
        ClassicsFooter.REFRESH_FOOTER_FAILED = getString(R.string.refresh_footer_failed);
        //"全部加载完成";
        ClassicsFooter.REFRESH_FOOTER_ALLLOADED = getString(R.string.refresh_footer_nothing);

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        mContext = this;
        //高斯模糊效果
        //getWindow().setFlags(WindowManager.LayoutParams.FLAG_SECURE,WindowManager.LayoutParams.FLAG_SECURE);
        //禁止页面被截屏、录屏
        //getWindow().addFlags(WindowManager.LayoutParams.FLAG_SECURE);
        initSwipeBackFinish();

        /*获取相应的Intent的传值*/
        getExtra();

        initRefresh();

        ActivityController.getInstance().addActivity(this);

        mPresenter = createPresenter();
        if (mPresenter != null) {
            mPresenter.attachView((V) this);
        }

        setContentView(createContentView());

        ButterKnife.bind(this);

        initView();
        //初始化数据的方法
        initDate();
        //初始化监听的方法
        initListener();

        initRefreshPager();
    }


    private void initRefreshPager() {
        //接收一条广播,刷新银行列表
        mRefreshBanksReceive = new RefreshPagerReceive((intent) -> {
            refreshPager();
        });
        mLocalBroadcastManager = LocalBroadcastManager.getInstance(mContext);
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction(BaseConstants.REFRESHPAGERACTION);
        mLocalBroadcastManager.registerReceiver(mRefreshBanksReceive, intentFilter);
    }

    /**
     * 刷新页面的广播
     */
    public void refreshPager() {

    }

    public void initSwipeBackFinish() {
        mSwipeBackHelper = new BGASwipeBackHelper(this, this);

        // 「必须在 Application 的 onCreate 方法中执行 BGASwipeBackHelper.init 来初始化滑动返回」
        // 下面几项可以不配置，这里只是为了讲述接口用法。

        // 设置滑动返回是否可用。默认值为 true
        mSwipeBackHelper.setSwipeBackEnable(true);
        // 设置是否仅仅跟踪左侧边缘的滑动返回。默认值为 true
        mSwipeBackHelper.setIsOnlyTrackingLeftEdge(true);
        // 设置是否是微信滑动返回样式。默认值为 true
        mSwipeBackHelper.setIsWeChatStyle(true);
        // 设置阴影资源 id。默认值为 R.drawable.bga_sbl_shadow
        mSwipeBackHelper.setShadowResId(R.drawable.bga_sbl_shadow);
        // 设置是否显示滑动返回的阴影效果。默认值为 true
        mSwipeBackHelper.setIsNeedShowShadow(true);
        // 设置阴影区域的透明度是否根据滑动的距离渐变。默认值为 true
        mSwipeBackHelper.setIsShadowAlphaGradient(true);
        // 设置触发释放后自动滑动返回的阈值，默认值为 0.3f
        mSwipeBackHelper.setSwipeBackThreshold(0.3f);
        // 设置底部导航条是否悬浮在内容上，默认值为 false
        mSwipeBackHelper.setIsNavigationBarOverlap(false);
    }

    /**
     * author :  贺金龙
     * create time : 2018/1/12 17:32
     * description : 获取相应的intent传值
     * instructions :
     * version :
     */
    private void getExtra() {
        Intent intent = getIntent();
        if (intent != null) {
            doGetExtra();
        }
    }

    @Override
    public Resources getResources() {
        return AdaptScreenUtils.adaptWidth(super.getResources(), designWidthInpx);
    }

    @Override
    public void applyOverrideConfiguration(Configuration overrideConfiguration) {
        if (overrideConfiguration != null) {
            int uiMode = overrideConfiguration.uiMode;
            overrideConfiguration.setTo(getBaseContext().getResources().getConfiguration());
            overrideConfiguration.uiMode = uiMode;
        }
        super.applyOverrideConfiguration(overrideConfiguration);
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(LocaleManager.getInstance().setLocale(base));
    }

    //----------------------------------------------必须实现的抽象方法----------------------------------------------//

    /**
     * author :  贺金龙
     * create time : 2018/1/12 17:29
     * description : 根据相应的ID创建Activity的布局
     * instructions :
     * version :
     */
    protected abstract int createContentView();

    /**
     * author :  贺金龙
     * create time : 2018/1/12 17:27
     * description : 创建相应的Presenter
     * instructions :
     * version : 2.0.1
     */
    protected abstract P createPresenter();

    /**
     * author :  贺金龙
     * create time : 2018/1/12 17:31
     * description : 初始化相应的控件
     * instructions :
     * version : 2.0.1
     */
    protected abstract void initView();

    //----------------------------------------------必须实现的抽象方法----------------------------------------------//


    //----------------------------------------------相应IBaseView的实现方法----------------------------------------------//
    //----------------------------------------------相应生命周期方法的实现----------------------------------------------//

    @Override
    protected void onResume() {
        super.onResume();
    }

    /**
     * 生命周期的结束方法
     */
    @Override
    protected void onDestroy() {
        super.onDestroy();
        ActivityController.getInstance().finishActivity(this);
        if (mPresenter != null) {
            mPresenter.detachView();
        }
        if (mLocalBroadcastManager != null && mRefreshBanksReceive != null) {
            mLocalBroadcastManager.unregisterReceiver(mRefreshBanksReceive);
        }
    }

    //----------------------------------------------相应生命周期方法的实现----------------------------------------------//

    //----------------------------------------------可去进行复写的方法----------------------------------------------//

    /**
     * author :  贺金龙
     * create time : 2018/1/12 17:35
     * description : Intent传值的获取方法
     * instructions : 这里已经判断相应的Intent不为空了
     * version : 2.0.1
     */
    public void doGetExtra() {
    }


    /**
     * author :  贺金龙
     * create time : 2018/1/12 17:40
     * description : 初始化数据的方法
     * instructions :
     * version : 2.0.1
     */
    public void initDate() {
    }

    /**
     * author :  贺金龙
     * create time : 2018/1/12 17:39
     * description : 初始化监听的方法
     * instructions :
     * version : 2.0.1
     */
    public void initListener() {
    }

    @Override
    public boolean onTouchEvent(MotionEvent event) {
        return super.onTouchEvent(event);
    }


    @Override
    public boolean dispatchTouchEvent(MotionEvent ev) {
        if (ev.getAction() == MotionEvent.ACTION_DOWN) {
            View v = getCurrentFocus();
            if (isShouldHideInput(v, ev)) {
                InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                if (imm != null) {
                    imm.hideSoftInputFromWindow(v.getWindowToken(), 0);
                }
            }
            return super.dispatchTouchEvent(ev);
        }
        // 必不可少，否则所有的组件都不会有TouchEvent了
        if (getWindow().superDispatchTouchEvent(ev)) {
            return true;
        }
        return onTouchEvent(ev);
    }

    /**
     * 是否显示键盘
     *
     * @param v
     * @param event
     * @return
     */
    public boolean isShouldHideInput(View v, MotionEvent event) {
        if (v != null && (v instanceof EditText)) {
            int[] leftTop = {0, 0};
            // 获取输入框当前的location位置
            v.getLocationInWindow(leftTop);
            int left = leftTop[0];
            int top = leftTop[1];
            int bottom = top + v.getHeight();
            int right = left + v.getWidth();
            if (event.getX() > right && event.getY() > top && event.getY() < bottom) {
                //如果是输入框右边的部分就保留
                return false;
            }
            if (event.getX() > left && event.getX() < right && event.getY() > top && event.getY() < bottom) {
                // 点击的是输入框区域，保留点击EditText的事件
                return false;
            } else {
                return true;
            }
        }
        return false;
    }

    //----------------------------------------------可去进行复写的方法----------------------------------------------//
    //----------------------------------------------对外暴露的方法----------------------------------------------//

    @Override
    public boolean isSupportSwipeBack() {
        return false;
    }

    @Override
    public void onSwipeBackLayoutSlide(float slideOffset) {

    }

    @Override
    public void onSwipeBackLayoutCancel() {

    }

    @Override
    public void onSwipeBackLayoutExecuted() {
        mSwipeBackHelper.swipeBackward();
    }

    @Override
    public void onBackPressed() {
        // 正在滑动返回的时候取消返回按钮事件
        if (mSwipeBackHelper.isSliding()) {
            return;
        }
        mSwipeBackHelper.backward();
    }
    //----------------------------------------------对外暴露的方法----------------------------------------------//
}
