package om.rrtx.mobile.rrtxcommon1;

/**
 * <AUTHOR>
 * Login模块的数据整理
 */
public class UserConstants {

    public interface Parameter {
        /**
         * 用户名称
         */
        String USERNAME = "userName";
        String JUNIOR_USER_ID = "juniorUserId";
        String PIN = "pin";
        String ID_NUMBER = "idNumber";
        /**
         * 安全问题
         */
        String SECRETWORD = "secretWord";
        /**
         * 用户密码
         */
        String PASSWORD = "password";
        /**
         * 加密字段标识
         */
        String KEYID = "keyId";
        /**
         * 二维码
         */
        String QRCODE = "qrCode";
        /**
         * 二维码类型
         */
        String QRTYPE = "qrType";
        /**
         * id卡
         */
        String IDCARD = "idCard";
        /**
         * 手机号码
         */
        String MOBILE = "mobile";
        /**
         * 手机区号
         */
        String MOBILEAREACODE = "mobileAreaCode";
        /**
         * 性别
         */
        String GENDER = "gender";
        /**
         * 邮箱
         */
        String EMAIL = "email";
        /**
         * 昵称
         */
        String NICKNAME = "nickName";
        /**
         * app标识
         */
        String APPNAME = "appName";
        /**
         * 轮播图状态
         */
        String POSITION = "position";
        /**
         * 版本号
         */
        String VERSIONNO = "versionNo";

        /**
         * 版本号
         */
        String PAGENUM = "pageNum";

        /**
         * 版本号
         */
        String PAGESIZE = "pageSize";
        /**
         * 操作系统类型 （1：Android 2：iOS）
         */
        String OSTYPE = "osType";
        /**
         * 语言
         */
        String LANGUAGE = "language";
        /**
         * sdk名称
         */
        String SDKNAME = "sdkName";
        /**
         * 用户id
         */
        String USERID = "userId";

        /**
         * 协议类型 01-钱包个人 02-钱包商户
         */
        String CONDITIONTYPE = "conditionType";
        /**
         * 协议版本
         */
        String CONDITIONVERSIONNO = "conditionVersionNo";
        /**
         * 协议名称
         */
        String CONDITIONTITLE = "conditionTitle";
    }

    public interface Transmit {
        /**
         * 转账的相应数据
         */
        String DETAILSBEAN = "detailsBean";
        /**
         * QrCode
         */
        String QRCODE = "qrCode";
        /**
         * PaycheckDetails
         */
        String PAYCHECKDETAILS = "paycheckDetails";
        /**
         * 编辑类型
         */
        String EDITTYPE = "editType";
        /**
         * 邮箱
         */
        String EMAIL = "email";
        /**
         * 昵称
         */
        String NICKNAME = "nickName";
        /**
         * 电话号码
         */
        String MOBILE = "mobile";
        /**
         * 电话区号
         */
        String MOBILEAREACODE = "mobileAreaCode";
        /**
         * 页面信息
         */
        String PAGERINFO = "pagerInfo";

        /**
         * 协议类型 01-钱包个人 02-钱包商户
         */
        String CONDITIONTYPE = "conditionType";
        /**
         * 协议版本
         */
        String CONDITIONVERSIONNO = "conditionVersionNo";
        /**
         * 协议名称
         */
        String CONDITIONTITLE = "conditionTitle";
    }

    public interface URL {
        /**
         * 获取最新的协议
         */
        String LASTESTCONDITION = "/condition/latest/condition";

        /**
         * 获取公钥接口
         */
        String GETPUB = "/encrypt/public/key";

        String CUSTOMERSERVICE = "/user/system/customerService";

        /**
         * 登出
         */
        String LOGOUT = "/logout";
        /**
         * 解析二维码接口
         */
        String ANALYZE = "/qrCode/qrAnalyze";
        /**
         * 获取二维码
         */
        String GETCODE = "/qrCode/get";
        /**
         * 是否有活动返现
         */
        String CHECKACTIVITY = "/inviteActivity/checkActivity";
        /**
         * 获取用户信息
         */
        String GETUSERINFO = "/user/getUserInfo";
        String GETUSERINFOBY = "/user/getUserInfoByIdCardAndMobile";
        /**
         * 查询用户信息（亲子账户用）
         */
        String GETUSERINFO_BYID = "/user/getUserInfoById";
        String CHECK_JUNIOR_ACCOUNT = "/juniorAccount/checkIdAndName";
        /**
         * 编辑用户信息
         */
        String MODIFYUSERINFO = "/user/modifyUserInfo";
        /**
         * 查询是否重复
         */
        String CHECKREPEAT = "/user/checkRepeat";
        /**
         * 请求时区接口
         */
        String RAGION = "/nation/list";
        /**
         * 更新头像接口
         */
        String UPLOADUSERAVATAR = "/user/uploadUserAvatar";
        /**
         * 上传图片
         */
        String UPLOADPHOTO = "/user/uploadUserPhoto";
        /**
         * 查询是否有更新的版本
         */
        String APPVERSION = "/appVersion/compare";
        /**
         * 查询是否有新消息
         */
        String CHECKNEW = "/user/notice/checkNew";
        /**
         * 查询消息列表
         */
        String NEWLISTS = "/user/notice/list";
        /**
         * 轮播图
         */
        String ROTATEPICTURE = "/promotion/advertisingPromotion/rotatePicture";
        /**
         * 悬浮图
         */
        String FLOATINGPICTURE = "/promotion/advertisingPromotion/floatingPicture";
        /**
         * 邀请好友推广链接
         */
        String INVITEFRIENDSGET = "/inviteFriends/get";
        /**
         * 语言切换
         */
        String LANGUAGESETUP = "/user/language/setup";
        /**
         * 获取SDK信息
         */
        String SDKINFO = "/sdk/user/info";
        /**
         * 获取应用参数(可用于获取所有币种)
         */
        String APPDICTCODE = "/common/appDictCode";
        String JUNIOR_LIST = "/juniorAccount/list";
        String JUNIOR_DELETE_CHECK = "/juniorAccount/deleteCheck";
        String Check_Upgrade_Junior_Info = "/juniorAccount/upgradeCheck";

        /**
         * 个人用户同意协议
         */
        String AGREECONDITION = "/user/condition/agree";

        /**
         * 获取验证码
         */
        String GET_VER_CODE = "/code/sms/get";
    }

    /**
     * 二维码类型
     */
    public interface QrType {
        /**
         * 个人收款信息
         */
        String PSK = "PSK";
        /**
         * 商户收款信息
         */
        String MER = "MER";
        /**
         * 个人二维码名片
         */
        String PMP = "PMP";

        /**
         * 扫描外部二维码
         */
        String QRCODEPREFIX = "https://xwallet-short.rrtx.vimbug.com/";

        /**
         * 扫描外部订单
         */
        String QRCODE = "qrCode";
    }

    public interface EditType {
        String NICKNAMETYPE = "nickName";
        String EMAILTYPE = "email";
    }

    public interface Gender {
        // 0:保密 1:女 2:男
        String Private = "0";
        String Male = "2";
        String Female = "1";
    }
}
