<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="500pt"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:visibility="gone"
        android:id="@+id/titleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30pt"
        android:textColor="@color/color_131313"
        android:textSize="32pt"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/contentTv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30pt"
        android:layout_marginTop="30pt"
        android:layout_marginRight="30pt"
        android:gravity="center"
        android:textColor="@color/color_131313"
        android:textSize="28pt"
        app:layout_constraintTop_toBottomOf="@id/titleTv"
        tools:text="sdaslkdjalsjdalksjdlaskjdlaksjsdaslkdjalsjdalksjdlaskjdlaksjsdaslkdjalsjdalksjdlaskjdlaksjsdaslkdjalsjdalksjdlaskjdlaksj" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1pt"
        android:layout_marginTop="30pt"
        android:background="@color/color_dfdfdf"
        app:layout_constraintTop_toBottomOf="@id/contentTv" />

    <TextView
        android:id="@+id/leftTv"
        android:layout_width="0dp"
        android:layout_height="88pt"
        android:gravity="center"
        android:textColor="@color/color_131313"
        android:textSize="22pt"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/rightTv"
        app:layout_constraintTop_toBottomOf="@id/line"
        tools:text="Set Now" />

    <TextView
        android:id="@+id/rightTv"
        android:layout_width="0dp"
        android:layout_height="88pt"
        android:gravity="center"
        android:textColor="@color/common_ye_F3881E"
        android:textSize="22pt"
        android:textStyle="bold"
        app:layout_constraintLeft_toRightOf="@id/leftTv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line"
        tools:text="Set Now" />


    <View
        android:id="@+id/line2"
        android:layout_width="1pt"
        android:layout_height="88pt"
        android:background="@color/color_dfdfdf"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toRightOf="@id/leftTv"
        app:layout_constraintRight_toLeftOf="@id/rightTv"
        app:layout_constraintTop_toTopOf="@id/line" />

</androidx.constraintlayout.widget.ConstraintLayout>