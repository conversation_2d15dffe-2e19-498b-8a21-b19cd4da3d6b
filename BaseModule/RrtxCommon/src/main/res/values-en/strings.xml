<resources>
    <string name="app_name">OneMoney Mobile</string>
    <string name="base_app_name">Rrt<PERSON><PERSON>om<PERSON></string>
    <string name="common_tip_network_error">There was a problem connecting to the network.Please try again later.</string>
    <!--下拉刷新-->
    <string name="refresh_header_pulling">Drop down to refresh</string>
    <string name="refresh_header_refreshing">Refresh ...</string>
    <string name="refresh_header_loading">Loading ...</string>
    <string name="refresh_header_release">Release to refresh</string>
    <string name="refresh_header_finish">Release to finish</string>
    <string name="refresh_header_failed">Release to failed</string>
    <string name="refresh_header_update">\'Last update\' M-d HH:mm</string>
    <string name="refresh_footer_failed">Load Failed</string>
    <string name="refresh_footer_finish">Load Success</string>
    <string name="refresh_footer_loading">Loading…</string>
    <string name="refresh_footer_pulling">Pull Up To Load More</string>
    <string name="refresh_footer_refreshing">Wait For Refreshing…</string>
    <string name="refresh_footer_release">Release To Load More</string>
    <string name="common_label_pwd">Password</string>
    <string name="common_label_fullname">Full Name</string>
    <string name="common_label_nickname">Nickname</string>
    <string name="common_label_original_pwd">Original Password</string>
    <string name="common_label_new_pwd">New Password</string>
    <string name="common_label_confirm_pwd">Confirm Password</string>
    <string name="common_label_pwd_tip">Password must consist of 8&#8211;20 characters including at least one number(0&#8211;9)、one uppercase letter(A&#8211;Z)、one lower case letter (a&#8211;z)、one special character(.~!@#$%&#38;*()_-+={}&#060;&#062;)</string>
    <string name="success">Success</string>
    <string name="common_label_total_amt">Total Amount</string>
    <string name="submit">Submit</string>
    <string name="common_btn_agree">Agree</string>
    <string name="common_btn_disagree_and_exit">Disagree And Exit</string>
    <string name="common_btn_date">Date</string>
    <string name="common_btn_forgot_pay_pin">Forgot Your Payment PIN?</string>
    <string name="common_alert_prompt">Prompt</string>
    <string name="common_alert_continue">Continue</string>
    <string name="common_tab_aa">AA Records</string>
    <string name="common_search_tip">Enter the Name/Mobile No.</string>
    <string name="common_alert_verify_touchid">Verify fingerprint to continue</string>
    <string name="common_alert_verify_faceid">Verify current Face ID to continue</string>
    <string name="common_btn_switch_account">Switch Account</string>
    <string name="common_btn_fund_transfer">Transfer</string>
    <string name="common_title_fund_transfer">Transfer</string>
    <string name="common_label_fund_transfer">Transfer</string>
    <string name="common_tip_amount_error_tip">Wrong amount format.</string>
    <string name="common_label_fingerprint_xWallet">Touch ID for \"xWallet\"</string>
    <string name="common_alert_closed_loginlock">Are you sure to close the fingerprint recognition？</string>
    <string name="common_label_enter_payment_pin_tip">Enter Payment PIN for authentication</string>
    <string name="common_lable_n_a">N/A</string>
    <string name="common_tip_error_fullname_tip">Invalid character(s) in Full Name.</string>
    <string name="common_tip_error_username_tip">Invalid character(s) in UserName.</string>
    <string name="common_tip_error_nric_tip">Invalid in NRIC.</string>
    <string name="common_tip_error_mobile_tip">Invalid mobile No. format.</string>
    <string name="common_tip_no_match_pwd_tip">Password and confirm password do not match</string>
    <string name="common_tip_error_pwd_tip">Invalid password format.</string>
    <string name="common_label_norecord_tip">You may use other functions</string>
    <string name="common_label_norecord">No record！</string>
    <string name="common_label_sll_error">SSL certificate verification error</string>
    <string name="common_google_map_not_available">Google Map not available</string>
    <string name="common_label_0_00">0.00</string>
    <string name="common_label_60">+60</string>
    <string name="jia_263">+263</string>
    <string name="nommal_263">263</string>
    <string name="common_label_usd">USD</string>
    <string name="common_label_hide">******</string>
    <string name="common_label_1">1</string>
    <string name="common_label_2">2</string>
    <string name="common_label_3">3</string>
    <string name="common_label_4">4</string>
    <string name="common_label_5">5</string>
    <string name="common_label_6">6</string>
    <string name="common_label_7">7</string>
    <string name="common_label_8">8</string>
    <string name="common_label_9">9</string>
    <string name="common_label_0">0</string>

    <string-array name="date_select">
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
        <item>6</item>
        <item>7</item>
        <item>8</item>
        <item>9</item>
        <item>10</item>
        <item>11</item>
        <item>12</item>
    </string-array>

    <string-array name="gender_select">
        <item>Privacy</item>
        <item>Male</item>
        <item>Female</item>
    </string-array>

    <string-array name="register_step">
        <item>Preferred Language</item>
        <item>User Information</item>
        <item>Mobile Verification</item>
        <item>Set PIN</item>
        <item>Confirm PIN</item>
        <item>Authentication</item>
    </string-array>

    <string-array name="id_type">
        <item>National ID</item>
        <item>Passport</item>
    </string-array>

    <string-array name="id_type_junior">
        <item>Birth Certificate</item>
        <item>Passport</item>
    </string-array>

    <string name="portrait_select_takephoto">Take a Photo</string>
    <string name="portrait_select_album">Choose from Album</string>
    <string name="number_AZ">0123456789qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM</string>
    <string name="text_error">%1$s format error</string>
    <string name="identity_auth">Identity Authentication</string>
    <string name="identity_info">Identity Information</string>
    <string name="check_info">Check Information</string>
    <string name="standard">Standard</string>
    <string name="incomplete">Incomplete</string>
    <string name="indistinct">Indistinct</string>
    <string name="reflective">Reflective</string>
    <string name="face_name">Name</string>
    <string name="valid_period">Valid Period</string>
    <string name="auth_type">Authentication Type</string>
    <string name="auth_time">Authentication Time</string>
    <string name="certification_info">Certification Information</string>
    <string name="start_auth">Start Authentication</string>
    <string name="face_hint">Please make sure the operation is done by you</string>
    <string name="face_hint2">Please ensure that the ID card is clear, complete\nand free from light spots.</string>
    <string name="face_hint3">Put the portrait face in the frame and\nalign the edges</string>
    <string name="face_hint4">1、Put the portrait face in the frame and align the edges，and put the ID card  on the dark background to shoot.\n2、Please make sure there is enough light to avoid reflection.\n3、Please make sure your ID card is complete and unobstructed.\n4、Please turn your ID card to the portrait</string>
    <string name="protrait_collect">Portrait Collection Authentication</string>
    <string name="scan_certificate">Scan Certificate</string>
    <string name="confirm_info">Confirm the identity information is correct.</string>
    <string name="face_check_hint">Please make sure the photos meet the standards.</string>
    <string name="idcard_format">National ID format error</string>
    <string name="reset_PIN">Reset PIN</string>
    <string name="enter_the_please">Please enter the PIN</string>
    <string name="reset_Successful">Reset Successful</string>
    <string name="network_msg">Transaction submitted, please confirm the payment result in the history bill.</string>
    <string name="network_error">Network is not good</string>
    <string name="payee_mobile">Payee Mobile</string>
    <string name="payee_name">Payee Name</string>
    <string name="payer_mobile">Payer Mobile</string>
    <string name="payer_name">Payer Name</string>
    <string name="history">History</string>
    <string name="oneMoney">OneMoney</string>
    <string name="bar_code">QR/Bar Code</string>
    <string name="biller">Biller</string>
    <string name="biller_Type">Biller Type</string>
    <string name="Reason_of_Payment">Reason of Payment</string>
    <string name="name">Name</string>
    <string name="School_name">School Name</string>
    <string name="junior_name">Junior Name</string>
    <string name="account_no">Account Number</string>
    <string name="zANU_PF">ZANU PF \nMembership Payments</string>
    <string name="zwl">ZWG</string>
    <string name="usd">USD</string>
    <string name="first_term">For First Term</string>
    <string name="second_term">For Second Term</string>
    <string name="third_term">For Third Term</string>
    <string name="amount">Amount</string>
    <string name="school_name">School Name</string>
    <string name="owner_name">Owner Name</string>
    <string name="bill_reason">Reason of Payment</string>
    <string name="bull_account">Account Number</string>
    <string name="business_bype0">Loan Disbursement</string>
    <string name="business_bype1">International Remittance In</string>
    <string name="business_bype_pay0">Loan Repayment</string>
    <string name="business_bype_pay1">International Remittance Out</string>
    <string name="to_link">to link.</string>
    <string name="succeed">Succeed</string>
    <string name="failed">Failed</string>
    <string name="you_can_visit_the_branch_of_Bank">You can visit the branch of Bank</string>
    <string name="payment_Record">Payment Record</string>
    <string name="no_PIN_Payment_hint">Single payment within the set amount of each currency does not require verification of PIN</string>
    <string name="free_Charge">Free Charge</string>
    <string name="no_Linked_Bank_Accounts">No Linked Bank Accounts</string>
    <string name="temp_unverified">Temporary Freeze</string>
    <string name="freeze">Freeze</string>
    <string name="small_Amount_No_PIN_Service_Agreement">Small Amount No PIN Service Agreement</string>
    <string name="limit">Limit</string>
    <string name="view">View</string>
    <string name="automatic_Debit_Signing">Automatic Debit Signing</string>
    <string name="close_no_pin_pay_hint">Are you sure you want to close the %1$s no PIN service?</string>
    <string name="set_no_pin_pay_success_hint">You have successfully modified below %1$s %2$s no PIN payment.</string>
    <string name="are_you_sure_you_want_to_close_the_automatic_deduction_service">Are you sure you want to close the automatic deduction service?</string>
    <string name="merchants_have_activated_automatic_deduction">The following merchants have activated automatic deduction services.</string>
    <string name="auto_debit_hint">After activating the automatic deduction service, you will allow
    merchants to deduct funds from your account without requiring a PIN code.</string>
    <string name="automatic_Deduction_Agreement">Automatic Deduction Agreement</string>
    <string name="signed_Merchant">Signed Merchant</string>
    <string name="auto_debit_success_hint">You have activated the automatic deduction service for merchant Starbucks Coffee.</string>
    <string name="effective">Effective</string>
    <string name="to_be_inputted">To be inputted</string>
    <string name="more">More</string>
    <string name="nearby_Merchants">Nearby Merchants</string>
    <string name="thirty_title">Third Party Paying</string>
    <string name="reset_hint">System Update，Please reset your PIN to ensure security.</string>
    <string name="pay_account_hint1">Deduct in the following order</string>
    <string name="the_OTP_code_has_been_sent_to">The OTP code has been sent to</string>
    <string name="delete_Account">Delete Account</string>
    <string name="agent_Bill_Payemnt">Sell ZESA</string>
    <string name="upgrade_Pending_Verification">Upgrade Pending Verification</string>
    <string name="please_go_to_OneMoney_branch_for_identity_authentication">Please go to OneMoney branch for identity authentication</string>
    <string name="please_enter_your_PIN">Please enter your PIN</string>
    <string name="the_account_has_a_balance_and">The account has a balance and cannot be cancelled</string>
    <string name="unlink_Account">Unlink Account</string>
    <string name="statement">Statement</string>
    <string name="function_app_name">FunctionApi</string>
    <string name="common_app_name">OneMoney Mobile</string>
    <string name="SMS_title_mobile_verification">Mobile No. Verification</string>
    <string name="SMS_btn_resend">To resend</string>
    <string name="paypin_alert_set_now">Set Now</string>
    <string name="paypin_title_edit_payment_pin_success">Edit Payment PIN Success</string>
    <string name="loginPwd_label_edit_loginpwd_tip">Edit the login password success</string>
    <string name="paypin_title_check_payment_pin">Check Payment PIN</string>
    <string name="paypin_label_auth_pin_input_tip">Enter Payment PIN to authorize</string>
    <string name="paypin_title_edit_payment_pin">Edit Payment PIN</string>
    <string name="paypin_label_pin_input_again_tip">Enter 4 digits Payment PIN again</string>
    <string name="paypin_title_retrieve_payment_pin_success">Retrieve Payment PIN Success</string>
    <string name="paypin_label_retrieve_payment_pin_tip">Retrieve Payment PIN success</string>
    <string name="loginPwd_title_edit_loginpassword">Edit Login Password</string>
    <string name="paypin_label_pin_atypism_tip">Password and confirm password do not match</string>
    <string name="login_btn_forgot_username">Forgot Username?</string>
    <string name="login_label_no_account_tip">You don’t have an account?</string>
    <string name="loginpwd_tip_newpwd_notmatch_pwd_tip">New password cannot be same as the old password.</string>
    <string name="loginpwd_title_edit_loginpwd_success">Edit Login Password Success</string>
    <string name="register_label_username_tip">1. Minimum 5 characters\n2. Maximum 30 characters\n3. Only alphanumeric characters with optional characters (_ @ .)</string>
    <string name="register_label_full_name_tip">Name MUST be the same with your name on national identity.</string>
    <string name="register_label_nric_tip">Only alphanumeric characters.</string>
    <string name="register_label_accept_the">Accept the</string>
    <string name="accept_the">Accept the    </string>
    <string name="register_label_terms_condition">Terms &#38; Conditions</string>
    <string name="register_label_registration_tip">You have successfully registered.</string>
    <string name="register_label_invitation_code">Invitation code</string>
    <string name="retrieve_title_forgot_username">Forgot Username</string>
    <string name="retrieve_title_forgot_pwd">Forget Password</string>
    <string name="retrieve_title_set_login_pwd">Set login password</string>
    <string name="retrieve_label_send_username_tip">Username has been sent to</string>
    <string name="retrieve_title_set_login_pwd_success">Set login password success</string>
    <string name="retrieve_label_set_login_pwd_success">Set the login password success</string>
    <string name="home_label_hi">Hi</string>
    <string name="home_label_hi_pls_login">Hi,Please Login</string>
    <string name="home_label_last_logintime">Last login time :</string>
    <string name="home_label_balance_tip">E-wallet Account Balance</string>
    <string name="home_label_account_asset">Account Asset</string>
    <string name="home_btn_split_bill">Split Bill</string>
    <string name="home_btn_history">History</string>
    <string name="home_btn_merchant">Nearby Merchants</string>
    <string name="slider_btn_feedback">Feedback</string>
    <string name="slider_btn_about">About</string>
    <string name="slider_btn_term_conditions">Terms &#38; Conditions</string>
    <string name="slider_btn_faq">FAQ</string>
    <string name="slider_btn_broadcast">Broadcast</string>
    <string name="about_title_about">About</string>
    <string name="about_label_copyright_tip">Powered by Bamboo Fintech.</string>
    <string name="about_label_app_introduction">xWallet provides customers convenient digital payment experience and integrated management capability to merchants and acquirers.</string>
    <string name="contact_label_no_contacts">No contact！</string>
    <string name="contact_label_find_friend_tip">Search your contacts.</string>
    <string name="contact_btn_added">Added</string>
    <string name="contact_btn_delete_contact">Delete Contact</string>
    <string name="contact_alert_confirm_delete">Confirm Delete</string>
    <string name="receiveqr_label_pay_for_me">Scan code to pay me</string>
    <string name="paymentqr_label_show_qr_tip">Show QR Code to the merchant.</string>
    <string name="paymentqr_btn_wallet_pay_tip">Pay with balance</string>
    <string name="paymentqr_btn_show_barcode_tip">For your security: Do NOT send QR code to others</string>
    <string name="paymentqr_btn_continue_see">Continue</string>
    <string name="payment_label_pay_to">Pay to</string>
    <string name="payment_label_pay_success">Pay success</string>
    <string name="payment_label_amt">Amount</string>
    <string name="payment_alert_cancal_pay_tip">Cancel payment ？</string>
    <string name="payment_tip_amount_must_greater">The payment amount must be greater than</string>
    <string name="bill_title_split_bill">Split Bill</string>
    <string name="bill_label_total_bill_amount">Total Bill </string>
    <string name="bill_label_persons_involve">People Involve</string>
    <string name="bill_btn_choose">Choose</string>
    <string name="bill_label_change_to">Change to</string>
    <string name="bill_btn_different_amount_mode"><u>Specified Amount</u></string>
    <string name="bill_label_amt_receivable">Amount</string>
    <string name="bill_label_per_person">Cost Per Person</string>
    <string name="bill_btn_equal_amount_mode"><u>Identical Amount</u></string>
    <string name="bill_btn_collect">Collect</string>
    <string name="bill_title_split_individually">Split Individually</string>
    <string name="bill_tip_select_acount_tip">Choose too many people</string>
    <string name="aa_title_aa_records">Split Bill Records</string>
    <string name="aa_btn_lssued">Initiate</string>
    <string name="aa_btn_receive">Received</string>
    <string name="aa_label_total_collect_tip">Total amount to collect</string>
    <string name="aa_label_has_received">Received</string>
    <string name="aa_label_cancelled">Cancelled</string>
    <string name="aa_label_need_pay_tip">You need to pay</string>
    <string name="aa_label_bill_from">Split Bill from</string>
    <string name="aa_label_time">Time</string>
    <string name="aa_label_paid">Paid</string>
    <string name="aa_title_split_bill">Split Bill</string>
    <string name="lssued_label_amt_receivable">Amount</string>
    <string name="lssued_label_not_receive_tip">Payment has not been received yet</string>
    <string name="lssued_label_has_received">Has been collected</string>
    <string name="aa_label_unpaid">Unpaid</string>
    <string name="aa_label_perople">People</string>
    <string name="aa_label_pending_amt">Pending Amount</string>
    <string name="aa_label_paid_amt">Paid Amount</string>
    <string name="lssued_alert_collect_stop_tip">Stop collecting money. Do you want to continue?</string>
    <string name="receive_label_have_pay">You need to pay</string>
    <string name="receive_btn_pay_now">Pay Now</string>
    <string name="account_label_total_assets">Total asset</string>
    <string name="account_btn_withdrawal">Withdrawal</string>
    <string name="account_btn_history">History</string>
    <string name="account_label_myqrcode">My virtual business card</string>
    <string name="account_label_qrcode_add_friends_tip">Sweep the QR code to add contacts</string>
    <string name="personal_label_profile_photo">Profile Photo</string>
    <string name="personal_title_nickname">Nickname</string>
    <string name="personal_tip_no_modify">No modification</string>
    <string name="personal_label_origin_mobile">Original Mobile No.</string>
    <string name="personal_label_new_mobile">New Mobile No.</string>
    <string name="personal_label_reset_mobile_tip">Mobile No. reset successful</string>
    <string name="portrait_title_portrait_setting">Portrait Settings</string>
    <string name="topup_label_select_topup_method">Select a Top Up Method</string>
    <string name="topup_btn_card">Credit/Debit Card</string>
    <string name="topup_label_topup_amt">Top Up Amount</string>
    <string name="topup_label_card_no">Card Number</string>
    <string name="topup_label_expiry_date">Expiry Date</string>
    <string name="topup_label_cvv">CVV</string>
    <string name="topup_label_MMYY">MM/YY</string>
    <string name="topup_label_top_result">You have successfully top up.</string>
    <string name="topup_btn_save_card">Save Card Details</string>
    <string name="topup_btn_continue">Continue</string>
    <string name="topup_tip_cardsaved">Your card has been saved</string>
    <string name="topup_label_valid_until">Valid Until</string>
    <string name="topup_alert_confirm_delete">Confirm to delete</string>
    <string name="topup_label_oops">Oops</string>
    <string name="topup_label_oops_tip">Something went wrong.Please try again later.</string>
    <string name="topup_label_timeout">Timeout</string>
    <string name="topup_label_timeout_tip">Your top up request has been closed.</string>
    <string name="topup_label_error_tip">Transaction submitted, please confirm the payment result in the history bill.</string>
    <string name="topup_label_topup_account">Top Up Account</string>
    <string name="common_label_select_account">Please Select Account</string>
    <string name="common_label_select_act">Select Account</string>
    <string name="withdrawal_label_withdrawal_account">Withdrawal Account</string>
    <string name="withdrawal_title_withdrawal">Withdrawal</string>
    <string name="withdrawal_label_select_bankcard">Select Withdrawal Bank card</string>
    <string name="withdrawal_label_click_select">Click select</string>
    <string name="withdrawal_label_withdrawal_amt">Withdrawal Amount</string>
    <string name="withdrawal_label_withdrawal_details">Withdrawal Details</string>
    <string name="withdrawal_btn_all_withdrawal">Withdrawal All</string>
    <string name="withdrawal_label_service_charge">Service charge</string>
    <string name="withdrawal_btn_withdrawal">Withdrawal</string>
    <string name="withdrawal_label_initiate">Initiate</string>
    <string name="withdrawal_label_processing">Processing</string>
    <string name="withdrawal_btn_bill_schedule">Billing Schedule</string>
    <string name="withdrawal_title_select_bankcard">Select Bank Card</string>
    <string name="withdrawal_label_citibank_name">CitiBank</string>
    <string name="withdrawal_label_bank_of_america_name">Bank of America</string>
    <string name="withdrawal_label_select_bank_card_tip">Please Select Bank Card</string>
    <string name="bankcard_title_bankcard_bind">Bind Bank Cards</string>
    <string name="bankcard_label_cardno">Card No.</string>
    <string name="bankcard_btn_bind">Bind</string>
    <string name="bankcard_label_nocontent">No content！</string>
    <string name="history_title_norecord">No record！</string>
    <string name="history_title_norecord_tip">You may use other functions</string>
    <string name="history_btn_filter">Filter</string>
    <string name="history_btn_all">All</string>
    <string name="history_btn_fund_transfer">Transfer</string>
    <string name="history_btn_split_bill">Split Bill</string>
    <string name="history_btn_topup">Top Up</string>
    <string name="history_label_split_bill">Split Bill</string>
    <string name="history_label_withdrawal">Withdrawal</string>
    <string name="history_label_agent_mobile">Sell Airtime / Bundle</string>
    <string name="history_label_agent_tran">Transfer For Customer</string>
    <string name="history_label_agent_name">Agent Name</string>
    <string name="history_label_service_provider">Service Provider</string>
    <string name="history_label_payee_name">Payee Name</string>
    <string name="history_label_payer_name">Payer Name</string>
    <string name="history_label_name">Name</string>
    <string name="history_label_original_orderno">Original Order No.</string>
    <string name="history_label_bankcard">Bank Card</string>
    <string name="history_label_service_charge">Service charge</string>
    <string name="history_label_initiate">Initiate</string>
    <string name="history_label_processing">Processing</string>
    <string name="history_label_successful">Success</string>
    <string name="history_label_failed">Failed</string>
    <string name="history_label_closed">Closed</string>
    <string name="history_label_success">Success</string>
    <string name="history_label_partial_refund">Partial refund</string>
    <string name="history_label_current_state">Current State</string>
    <string name="history_label_default_commodity">Default</string>
    <string name="history_label_this_month">This Month</string>
    <string name="history_label_description">Description</string>
    <string name="history_label_wallet">Wallet</string>
    <string name="history_label_cash">Cash</string>
    <string name="security_btn_password_manage">Password Management</string>
    <string name="security_btn_gesture_lockscreen">Gesture Lock Screen</string>
    <string name="security_title_password_manage">Password Management</string>
    <string name="security_btn_edit_login_pwd">Edit Login Password</string>
    <string name="security_btn_edit_payment_pin">Edit Payment PIN</string>
    <string name="security_btn_retrieve_payment_pin">Retrieve Payment PIN</string>
    <string name="security_alert_touch_open_tip">Are you sure you want to open the fingerprint recognition？</string>
    <string name="security_lab_password">Password</string>
    <string name="security_alert_touch_not_ternon">The Touch recognition is  turned off, please turn on the Touch recognition first</string>
    <string name="security_alert_touch_select_other">The Touch recognition is turned off, please select another authentication </string>
    <string name="sec_pin_account_activate_tip">You are about to activate your %1$s account</string>
    <string name="sec_pin_label_account_activation">Account Activation</string>
    <string name="sec_pin_label_activate_succ">Activation Successful</string>
    <string name="security_gesture_pattern_pwd_login_tip">For account security, opening Gesture Pattern requires verifying the login password</string>
    <string name="security_close_gesture_pattern_tip">For account security, closing Gesture Pattern requires verifying the login password</string>
    <string name="checkout_btn_please">Please select</string>
    <string name="checkout_alert_cancel_pay_tip">Are you sure to cancel the payment？</string>
    <string name="checkout_alert_cancel_withdrawal_tip">Are you sure to cancel the withdrawal？</string>
    <string name="checkout_label_option_payment">Payment Option</string>
    <string name="checkout_label_add_bank_card">Add Bank Card</string>
    <string name="checkout_title_success">Success</string>
    <string name="checkout_label_pay_success">Payment for Success</string>
    <string name="checkout_label_default_commodity">Default Commodity</string>
    <string name="checkout_label_transfer">Transfer</string>
    <string name="checkout_label_split_bill">Split Bill</string>
    <string name="checkout_label_top_up">Top Up</string>
    <string name="checkout_label_withdrawal">Withdrawal</string>
    <string name="checkout_label_service_charge">Service Charge</string>
    <string name="about_btn_term_conditions">Terms &#38; Conditions</string>
    <string name="about_btn_privacy_policy">Privacy policy</string>
    <string name="contact_label_service_phone">+80 9382222</string>
    <string name="update_label_force_update_tip">You have a new version to update.</string>
    <string name="update_label_prompt">Prompt</string>
    <string name="promotion_title_mer_list">Merchant List</string>
    <string name="promotion_title_mer_infomation">Merchant Infomation</string>
    <string name="promotion_label_view_detail">View Detail</string>
    <string name="promotion_label_recommended">Recommended</string>
    <string name="promotion_label_empty_tip">No merchant data!</string>
    <string name="promotion_label_no_googlemap_tip">You don\'t have Google Maps installed. Click to continue to App Store.</string>
    <string name="history_label_register">Register</string>
    <string name="history_label_paticipate_role">Participation role</string>
    <string name="history_label_initiator">Initiator</string>
    <string name="history_label_recommender">Recommender</string>
    <string name="history_label_cashback_deduction">Cashback Deduction</string>
    <string name="qrcode_tip_store_success">Save Success</string>
    <string name="broacast_title_broadcast_setting">Broadcast Settings</string>
    <string name="broacast_label_transfer">Transfer</string>
    <string name="condition_label_accept_content">Accept the above content</string>
    <string name="term_condition_tip1">According to the requirements of relevant departments, we have updated the agreement and terms. Please read carefully and fully understand the agreement and terms.</string>
    <string name="term_condition_tip2">You can read the following content for details.</string>
    <string name="term_condition_tip3">If you agree, please check accept and click "Agree" to accept our service.</string>
    <string name="underline_terms_condition"><u>Terms &#38; Conditions</u></string>
    <!--翻译处理-->
    <string name="common_label_nric">National Id</string>
    <string name="refresh_footer_nothing">No More Data</string>
    <string name="common_alert_ok">OK</string>
    <string name="common_btn_reset">Reset</string>
    <string name="common_btn_remark">Remark(Optional)</string>
    <string name="common_label_remark">Remark</string>
    <string name="remark">Remark</string>
    <string name="history_label_remark">Remark</string>
    <string name="common_btn_confirm">Confirm</string>
    <string name="confrim">Confirm</string>
    <string name="common_alert_cancel_pay">Yes</string>
    <string name="yes">Yes</string>
    <string name="no">No</string>
    <string name="common_alert_keep_pay">No</string>
    <string name="common_alert_cancel">Cancel</string>
    <string name="common_btn_cancel">Cancel</string>
    <string name="common_label_fingerprint_verify_tip">Verify current Touch ID to continue</string>
    <string name="common_label_fingerprint_locked_error_tip">Fingerprint locked, please try again later</string>
    <string name="common_label_open_fingerprint">Set biometric successful</string>
    <string name="common_label_closed_fingerprint">Fingerprint recognition closed</string>
    <string name="login_PIN">Login PIN</string>
    <string name="forgot_PIN">Forgot PIN</string>
    <string name="secret_hint">Please enter the secret words</string>
    <string name="forgot">Forgot?</string>
    <string name="common_btn_login">Login</string>
    <string name="login_title_login">Login</string>
    <string name="login_btn_sign_up">Registration</string>
    <string name="close">Close</string>
    <string name="authentication">Authentication</string>
    <string name="completed">Completed</string>
    <string name="user_info">User Information</string>
    <string name="preferred_language">Preferred Language</string>
    <string name="enter_initial_pin">Please enter the initial PIN</string>
    <string name="to_security_change_initial_pin">To ensure security, please change the initial login PIN</string>
    <string name="shona">Shona</string>
    <string name="english">English</string>
    <string name="slider_btn_language">English</string>
    <string name="ndebele">Ndebele</string>
    <string name="next">Next</string>
    <string name="check_account">Check Account Holder</string>
    <string name="cancel_transaction">Cancel Transaction</string>
    <string name="proceed_transaction">Proceed Transaction</string>
    <string name="account_holder">Account Holder:</string>
    <string name="lookup_hint">Customer Lookup Failed, Do you still want to proceed with the transactions?</string>
    <string name="withdrawal_btn_next">Next</string>
    <string name="scan_again">Scan Again</string>
    <string name="first_name">First Name</string>
    <string name="middle_name">Middle Name(Optional)</string>
    <string name="last_name">Last Name</string>
    <string name="gender">Gender</string>
    <string name="personal_label_gender">Gender</string>
    <string name="male">Male</string>
    <string name="female">Female</string>
    <string name="date_of_birth">Date of Birth</string>
    <string name="id_type">ID Type</string>
    <string name="id_number">ID Number</string>
    <string name="location">Location</string>
    <string name="address">Address</string>
    <string name="email_optional">Optional</string>
    <string name="read_and_accept">I have read and accept the</string>
    <string name="terms_and_conditions">Terms &amp; Conditions</string>
    <string name="mobile_error">Mobile number format error</string>
    <string name="email_error">Email format error</string>
    <string name="resend">Resend</string>
    <string name="idcard_error">Only numbers and letters can be entered</string>
    <string name="please_check">Input error，please check</string>
    <string name="security_input_err_pls_chk">Input error, please check</string>
    <string name="input_pin_format">PIN consists of 4 digits, do not set consecutive digits or the same digits , such as 1234、1111</string>
    <string name="input_pin_fromat_hint">Non compliant with rules</string>
    <string name="re_input_pin">Please re-enter the PIN</string>
    <string name="input_pin_inconsistent_hint">The two inputs are inconsistent</string>
    <string name="have_received_registration1">We have received your junior registration application.</string>
    <string name="go_authentication1">Please bring valid identification and accompany your child to any OneMoney branch for authentication</string>
    <string name="have_received_registration">We have received your registration application.</string>
    <string name="security_center">Security Center</string>
    <string name="change_PIN">Change PIN</string>
    <string name="setpin">Set PIN</string>
    <string name="confirm_pin">Confirm PIN</string>
    <string name="enter_old_PIN">Please enter the old PIN</string>
    <string name="enter_new_PIN">Please enter the new PIN</string>
    <string name="re_enter_new_PIN">Please re-enter the new PIN</string>
    <string name="account_no_find_hint">OneMoney account not found</string>
    <string name="account_status_hint">Account status is abnormal</string>
    <string name="account_exists_hint">Contact already exists</string>
    <string name="transfer_label_received_tip">has received your transfer</string>
    <string name="send_to_OneMoney">Send To OneMoney</string>
    <string name="enter_the_PIN">Enter the PIN</string>
    <string name="done">Done</string>
    <string name="withdrawal_btn_done">Done</string>
    <string name="input_new_pin_fromat_hint">Non compliant with rules</string>
    <string name="input_new_pin_error_hint">The new PIN cannot be the same as the old PIN</string>
    <string name="Change_Successful">Change Successful</string>
    <string name="complete">Complete</string>
    <string name="home_btn_records">Records</string>
    <string name="Account_Records">Account Records</string>
    <string name="account_label_balance_account">Balance Account</string>
    <string name="balance_Account">Balance Account</string>
    <string name="send_money">Send Money</string>
    <string name="transfer_label_recents">Recents</string>
    <string name="Payment_Details">Payment Details</string>
    <string name="order_info">Order Info</string>
    <string name="fee">Fee</string>
    <string name="history_label_fee">Fee</string>
    <string name="checkout_label_fee">Fee</string>
    <string name="payment_label_fee">Fee</string>
    <string name="common_label_fee">Fee</string>
    <string name="tax">Tax</string>
    <string name="payment_method">Payment Method</string>
    <string name="insufficient_balance">Insufficient Balance</string>
    <string name="pay">Pay</string>
    <string name="common_keyboard_tip">Uses a secured purpose-built keyboard</string>
    <string name="has_send_money">has received your send Money</string>
    <string name="search">Search</string>
    <string name="contacts_details">Contacts Details</string>
    <string name="send_records">Send Records</string>
    <string name="add_contacts">Add Contacts</string>
    <string name="add">Add</string>
    <string name="successfully_added">Successfully added</string>
    <string name="delete">Delete</string>
    <string name="common_label_delete">Delete</string>
    <string name="contact_asking_delete">Are you sure you want to delete this contact?</string>
    <string name="successfully_deleted">Successfully deleted</string>
    <string name="buy_airtime">Buy Airtime</string>
    <string name="merchant_code">Merchant Code</string>
    <string name="merchant_name_hint">The merchant code does not exist</string>
    <string name="city_of_Harare">City of Harare</string>
    <string name="ZANU_PF_Membership_Payments">ZANU PF Membership Payments</string>
    <string name="zESA">ZESA</string>
    <string name="pay_billcode">Pay Using Biller Code</string>
    <string name="school_Fees">School Fees</string>
    <string name="others">Others</string>
    <string name="airtime_To">Airtime To</string>
    <string name="biller_Name">Biller Name</string>
    <string name="bill_name">Biller Name</string>
    <string name="biller_Code">Biller Code</string>
    <string name="bill_code">Biller Code</string>
    <string name="trading_source">Trading Source</string>
    <string name="branch_name">Branch Name</string>
    <string name="history_label_agent_with">Cash Out</string>
    <string name="history_label_agent_topup">Cash In</string>
    <string name="using_code">Pay Using Merchant Code</string>
    <string name="code_hint">Only available to merchants</string>
    <string name="bill_code_hint">Both Buy Airtime/Bundle and ZESA do not support pay using biller code</string>
    <string name="membership_Number">Membership Number</string>
    <string name="Semester">Semester</string>
    <string name="schoolt_term">School Term</string>
    <string name="School_term">School Term</string>
    <string name="student_class">Student\'s Class</string>
    <string name="Students_Class">Student\'s Class</string>
    <string name="Student_Class">Student Class</string>
    <string name="Students_Name">Student\'s Name</string>
    <string name="Student_Name">Student Name</string>
    <string name="student_1name">Student\'s Name</string>
    <string name="social_Amenities">Ministry of National Housing and Social Amenities</string>
    <string name="bill_house">Ministry of National Housing and Social Amenities</string>
    <string name="Ministry_of_National_Housing_and_Social_Amenities">Ministry of National Housing and Social Amenities</string>
    <string name="city">City</string>
    <string name="get_Balance">Get Balance</string>
    <string name="balance_Enquiry">Balance Enquiry</string>
    <string name="statement_Enquiry">Statement Enquiry</string>
    <string name="get_Statement">Get Statement</string>
    <string name="bank_Account_Management">Bank Account Management</string>
    <string name="bank_to_OneMoney">Bank to OneMoney</string>
    <string name="home_bank_to_OneMoney">Bank To \n OneMoney</string>
    <string name="account_btn_top_up">Bank to OneMoney</string>
    <string name="oneMoney_to_Bank">OneMoney to Bank</string>
    <string name="home_oneMoney_to_Bank">OneMoney \n To Bank</string>
    <string name="unbound_bank_hint">Unbound Bank Account</string>
    <string name="amount_To_OneMoney">Amount To OneMoney</string>
    <string name="min_amount_is">Min amount is %1$s %2$s</string>
    <string name="min_amount_is_ZWL">Min amount is ZWG 1.00</string>
    <string name="account_of_City_of_Harare">Account of City of Harare</string>
    <string name="name_city">Account of City of Harare</string>
    <string name="student_first">Student\'s First Name</string>
    <string name="house_first">Owner First Name</string>
    <string name="house_middle">Owner Middle Name</string>
    <string name="house_last">Owner Last Name</string>
    <string name="student_middle">Student\'s Middle Name</string>
    <string name="student_last">Student\'s Last Name</string>
    <string name="there_are_linked_bank_accounts_available">There are linked bank accounts available</string>
    <string name="pick">Pick</string>
    <string name="last_Update">Last Update：</string>
    <string name="payment_Account">Payment Account</string>
    <string name="failed_Reason">Failed Reason</string>
    <string name="per_Charge">/ Per Charge</string>
    <string name="bank_Account">Bank Account</string>
    <string name="Last_m">Last：</string>
    <string name="your_wallet_is_not_linked_to_any_bank_account">Your wallet is not linked to any bank account.</string>
    <string name="cBZ_FBC_POSB_CABS">CBZ/ FBC/ POSB/ CABS</string>
    <string name="withdrawal_hint">If you need cash, please go to the OneMoney branch to process cash transactions</string>
    <string name="transaction_Details">Transaction Details</string>
    <string name="format_error_please_input_11_digit">Format error，please input 11 digit</string>
    <string name="meter_Number_">Meter Number: </string>
    <string name="meter_Number">Meter Number</string>
    <string name="common_label_username">Username</string>
    <string name="user_Name_">User Name: </string>
    <string name="user_Name">User Name</string>
    <string name="personal_label_user_name">User Name</string>
    <string name="the_ZESA_bill_token_has_been_sent_to">The ZESA bill token has been sent to</string>
    <string name="resent_Token">Resent Token</string>
    <string name="iD_Expiring_Date">ID Expiring Date</string>
    <string name="my_QR_Code">My QR Code</string>
    <string name="save_as_Image">Save as Image</string>
    <string name="nationality">Nationality</string>
    <string name="home_buy_Airtime_Bundle">Buy Airtime \n / Bundle</string>
    <string name="buy_Airtime_Bundle">Buy Airtime / Bundle</string>
    <string name="buy_Bundle">Buy Bundle</string>
    <string name="common_label_mobile">Mobile No.</string>
    <string name="mobile_No">Mobile No.</string>
    <string name="personal_title_mobile">Mobile No.</string>
    <string name="currency">Currency</string>
    <string name="common_label_currecny">Currency</string>
    <string name="bill_label_currecny_slient">Currency:</string>
    <string name="bundle_Name">Bundle Name</string>
    <string name="khuluma_24_7_Bundle">Khuluma 24/7 Bundle</string>
    <string name="data_Mo_Gigs_Bundle">Data Mo’Gigs Bundle</string>
    <string name="data_Bundle">Data Bundle</string>
    <string name="designated_APP_Bundle">Designated APP Bundle</string>
    <string name="bundle_Plan">Bundle Plan</string>
    <string name="whatsApp_Bundle">WhatsApp Bundle</string>
    <string name="payment_Settings">Payment Settings</string>
    <string name="activate_Wallet_No_PIN_Payment">Activate Wallet No PIN Payment</string>
    <string name="activate_Now">Activate Now</string>
    <string name="home_label_activate_now">Activate Now</string>
    <string name="email">Email</string>
    <string name="personal_label_email">Email</string>
    <string name="personal_title_email">Email</string>
    <string name="optiona_email">Email(Optional)</string>
    <string name="users_must_be_at_least_16_years_old">Users must be at least 16 years old</string>
    <string name="mobile_Verification">Mobile Verification</string>
    <string name="agent_Bill_Payemnt_">Agent Bill Payment -&#160;</string>
    <string name="the_ZESA_bill_token_will_be_resent_to">The ZESA bill token will be resent to</string>
    <string name="send_ver_code">Your verification code has been sent to</string>
    <string name="SMS_label_verifiy_code_tip">Your verification code has been sent to</string>
    <string name="login_label_authcode_input_tip">Please enter authorization code</string>
    <string name="login_label_tempdevice_tip">The system has detected that you have bound other devices. Please log in through the master device to obtain the authorization code.</string>
    <string name="home_title_home">Home</string>
    <string name="common_tab_home">Home</string>
    <string name="home_btn_receive_payment">Receive Payment</string>
    <string name="receive_Payment">Receive Payment</string>
    <string name="make_Payment">Make Payment</string>
    <string name="home_btn_make_payment">Make Payment</string>
    <string name="home_btn_scan">Scan</string>
    <string name="home_btn_billpayment">Bill Payment</string>
    <string name="bill_Payment">Bill Payment</string>
    <string name="history_label_bill_pay">Bill Payment</string>
    <string name="account">Account</string>
    <string name="common_label_account">Account</string>
    <string name="home_label_account">Account</string>
    <string name="account_label_my_account">Account</string>
    <string name="scan_label_scan_tip">Position the QR code inside the scan area</string>
    <string name="scan_tip_scan_error_tip">Invalid QR code</string>
    <string name="qrcode_tip_store_fail">Invalid qr code</string>
    <string name="slider_btn_update">Check Update</string>
    <string name="slider_btn_logout">Log Out</string>
    <string name="slider_alert_logout_tip">Are you sure to log out?</string>
    <string name="slider_alert_logout_lock_tip">Logon lock will be closed for account security!</string>
    <string name="slider_alert_version_new_tip">Discover a new version</string>
    <string name="setting_alert_newversion">Discover a new version</string>
    <string name="receiveqr_btn_set_price">Set Amount</string>
    <string name="price_title_set_price">Set Amount</string>
    <string name="price_title_clear_amount">Clear Amount</string>
    <string name="contact_title_my_contacts">My Contacts</string>
    <string name="my_contacts">My Contacts</string>
    <string name="account_btn_invite_friends">Invite Friends</string>
    <string name="invitation_title_invitation">Invite Friends</string>
    <string name="account_label_reward">Reward</string>
    <string name="payment_label_merchant">Merchant</string>
    <string name="merchant">Merchant</string>
    <string name="personal_info">Personal Information</string>
    <string name="personal_title_personal_info">Personal Info</string>
    <string name="personal_btn_save">Save</string>
    <string name="bankcard_label_bankname">Bank Name</string>
    <string name="bank_Name">Bank Name</string>
    <string name="bank">Bank</string>
    <string name="successful">Successful</string>
    <string name="withdrawal_label_successful">Successful</string>
    <string name="merchants_no">No Merchants</string>
    <string name="history_btn_all_category">All Category</string>
    <string name="history_label_payment">Payment</string>
    <string name="history_btn_payment">Payment</string>
    <string name="payment">Payment</string>
    <string name="payment_title_payment">Payment</string>
    <string name="history_label_date_time">Date Time</string>
    <string name="date_time">Date Time</string>
    <string name="transaction_no">Transaction No.</string>
    <string name="history_label_transaction_no">Transaction No.</string>
    <string name="original_transaction">Original Transaction No.</string>
    <string name="history_label_original_no">Original Transaction No.</string>
    <string name="commodity">Commodity</string>
    <string name="history_label_commodity">Commodity</string>
    <string name="merchant_Order_No">Merchant Order No.</string>
    <string name="history_label_mer_orderno">Merchant Order No.</string>
    <string name="history_label_mer_order_no">Merchant Order No.</string>
    <string name="history_label_balance">Balance</string>
    <string name="balance">Balance</string>
    <string name="change_device_hint1">The master device has been changed to </string>
    <string name="change_device_hint2">, and the current device has logged out.</string>
    <string name="security_btn_login_lock">Login Lock</string>
    <string name="security_title_loginlock">Login Lock</string>
    <string name="security_btn_touchid">Touch ID</string>
    <string name="security_txt_gesture_pattern">Gesture Pattern</string>
    <string name="security_txt_change_gesture_pattern">Change Gesture Pattern</string>
    <string name="security_title_login_unlock">Login Unlock</string>
    <string name="security_label_tap_verify">Tap to Verify</string>
    <string name="security_alert_use_loginpwd">Use Login password</string>
    <string name="security_btn_verify">Verify</string>
    <string name="security_pls_confirm_login_pwd">Please confirm your login password</string>
    <string name="security_pls_draw_gesture_pattern">Please drawing the gesture pattern</string>
    <string name="security_pls_draw_old_gesture_pattern">Please drawing the old gesture pattern</string>
    <string name="security_pls_draw_new_gesture_pattern">Please drawing the new gesture pattern</string>
    <string name="security_repeat_draw_new_gesture_pattern">Repeat drawing the new gesture pattern</string>
    <string name="security_repeat_draw_gesture_pattern">Repeat drawing the gesture pattern</string>
    <string name="security_draw_err_pls_chk">Drawing error, please check</string>
    <string name="security_draw_to_verify">Drawing To Verify</string>
    <string name="security_login_pwd_ver">Login Password Verification</string>
    <string name="security_pwd_login">Password Login</string>
    <string name="security_least_5_point">Draw at least 5 connected points</string>
    <string name="security_two_draw_inconsistent">Two drawings are inconsistent</string>
    <string name="security_login_now">Login Now</string>
    <string name="security_change_successed">Change Successed</string>
    <string name="security_change_successed_content">Gesture pattern changed successfully，Please login again</string>
    <string name="checkout_label_unavailable">Unavailable</string>
    <string name="merchant_name">Merchant Name</string>
    <string name="history_label_merchant_name">Merchant Name</string>
    <string name="checkout_label_mer_name">Merchant Name</string>
    <string name="device_title_device_manage">Device Management</string>
    <string name="security_btn_device_manage">Device Management</string>
    <string name="device_btn_current_device">Current Device</string>
    <string name="device_btn_other_device">Other Device</string>
    <string name="device_label_blank_device">No Device Information！</string>
    <string name="device_label_device_type">Device Type</string>
    <string name="device_label_operate_system">Operating System</string>
    <string name="device_label_bind_time">Bind Time</string>
    <string name="device_label_device_level">Device Level</string>
    <string name="device_label_used_time">Used Time</string>
    <string name="device_label_generation_time">Generation Time</string>
    <string name="device_label_authorization_code">Authorization Code</string>
    <string name="device_label_master_device">Master Device</string>
    <string name="device_label_temporary_device">Temporary Device</string>
    <string name="device_btn_get_auth_code">Get Authorization Code</string>
    <string name="device_last_used">Last Used Device</string>
    <string name="device_btn_change_master_device">Change Master Device</string>
    <string name="device_title_change_master_device">Change Master Device</string>
    <string name="device_title_auth_code_record">Authorization Code Record</string>
    <string name="device_label_authcode_success_tip1">The authorization code has been sent to your registered mobile phone. Please use within</string>
    <string name="device_label_authcode_success_tip2">minute.</string>
    <string name="device_alert_change_device_tip1">Are you sure you want to change the temporary device</string>
    <string name="device_alert_change_device_tip2">to the master device?After the change, the current device will logout automatically.</string>
    <string name="device_alert_change_master_device_tip">Are you sure you want to change the temporary device $TempDevice to the master device?After the change, the current device will logout automatically.</string>
    <string name="device_label_authcode_tip">Your authorization code is</string>
    <string name="account_label_account_balance">Account Balance</string>
    <string name="business_bype">Business Type</string>
    <string name="history_label_business_type">Business Type</string>
    <string name="promotion_btn_show_stores_tip">Show stores in this range</string>
    <string name="setting_alert_lastversion">It\'s the latest version</string>
    <string name="slider_alert_version_latest_tip">It\'s the latest version</string>
    <string name="promotion_label_category">Category</string>
    <string name="activat_small_amount_no_PIN_payment">Activate %1$s small amount no PIN payment</string>
    <string name="not_verification_hint">Single Payment below %1$s %2$s does not require PIN verification</string>
    <string name="open_no_pin_pay_success_hint">You have successfully activated below %1$s %2$s no PIN payment.</string>
    <string name="closed_no_pin_pay_success_hint">%1$s no PIN payment has been closed.</string>
    <string name="close_no_PIN_payment">Close %1$s no PIN payment</string>
    <string name="no_IN_Payment">No PIN Payment</string>
    <string name="no_PIN_for_Payment">No PIN for Payment</string>
    <string name="transaction_Type">Transaction Type</string>
    <string name="airtime">Airtime</string>
    <string name="bulk_Payment">Bulk Payment</string>
    <string name="agent">Agent</string>
    <string name="operator">Operator</string>
    <string name="edit_Portrait">Edit Portrait</string>
    <string name="edit_Email">Edit Email</string>
    <string name="automatic_Debit">Automatic Debit</string>
    <string name="close_Automatic_Deduction_Service">Close Automatic Deduction Service</string>
    <string name="scan_QR_code_to_receive_payment">Scan QR code to receive payment</string>
    <string name="show_QR_Bar_code_to_make_payment">Show QR / Bar code to make payment</string>
    <string name="update">Update</string>
    <string name="update_btn_update">Update</string>
    <string name="slider_alert_update">Update</string>
    <string name="setting_alert_update">Update</string>
    <string name="bar_code_hint">The bar code number is only displayed when making payments to merchants.\n
Please do not send it to others to prevent fraud.</string>
    <string name="i_Know">I Know</string>
    <string name="signed_User">Signed User</string>
    <string name="transaction_Mode">Transaction Mode</string>
    <string name="activation_Date">Activation Date：</string>
    <string name="service_No_">Service No.</string>
    <string name="activation_Time">Activation Time</string>
    <string name="status">Status</string>
    <string name="auto_debit_close_hint">Are you sure you want to close the automatic deduction service?</string>
    <string name="notice">Notice</string>
    <string name="reverse_title">Reverse</string>
    <string name="statement_Details">Statement Details</string>
    <string name="automatic_Debit_Activation">Automatic Debit Activation</string>
    <string name="has_activated_small_amount_no_PIN_payment">%1$s has activated small amount no PIN payment</string>
    <string name="contact_title_contact_us">Contact Us</string>
    <string name="slider_btn_contact_us">Contact Us</string>
    <string name="contact_label_call_tip">If you have any feedback and suggestions,please contact us.</string>
    <string name="contact_label_service_time">Customer Service Time </string>
    <string name="contact_btn_call_us">Call us</string>
    <string name="contact_btn_feedback">Online Contact</string>
    <string name="other_social">Other Social Platforms</string>
    <string name="agent_transfer">Agent Transfer</string>
    <string name="history_label_agent_tran_refund">Agent Transfer Refund</string>
    <string name="history_label_full_refund">Refunded</string>
    <string name="refund">Refund</string>
    <string name="history_btn_refund">Refund</string>
    <string name="history_label_refund">Refund</string>
    <string name="history_label_pending">Pending</string>
    <string name="aa_label_pending">Pending</string>
    <string name="junior_Account_Management">Junior Account Management</string>
    <string name="linked_Junior_Accounts">Linked Junior Accounts</string>
    <string name="unlinked_Junior_Accounts">Unlinked Junior Accounts</string>
    <string name="activated">Activated</string>
    <string name="upgrade_Unverified">Upgrade Unverified</string>
    <string name="unverified">Unverified</string>
    <string name="register_junior">Register Junior Account</string>
    <string name="register_Junior_Account">Register Junior Account</string>
    <string name="personal_title1">Junior Info</string>
    <string name="junior_info">Junior Information</string>
    <string name="uses_limit">User age limit 9-15 years old</string>
    <string name="personal_relationship">Relationship</string>
    <string name="relationship">Relationship</string>
    <string name="mother">Mother</string>
    <string name="father">Father</string>
    <string name="legal_Guardian">Legal Guardian</string>
    <string name="notice_registered2">The Mobile has been registered and cannot register for  OneMoney personal wallet account</string>
    <string name="notice_registered1">The mobile number has been submitted for registration. Please go to OneMoney branch for identity authentication.</string>
    <string name="notice_registered">The mobile has been registered and cannot register for a junior account</string>
    <string name="notice_go_authentication">The mobile has been submitted to register junior account.
        Please go to OneMoney branch for identity authentication.</string>
    <string name="notice_idcard">The ID document has been registered and cannot register for a junior account</string>
    <string name="send_junior_code">Junior account verification code has been sent to</string>
    <string name="set_juniorpin">Set Junior Account PIN</string>
    <string name="confirm_juniorpin">Confirm Junior Account PIN</string>
    <string name="go_authentication">Please bring your valid Identification to the nearby OneMoney branch for identity authentication.</string>
    <string name="register_title_user_registration">Register Regular Account</string>
    <string name="account_type">Regular Account</string>
    <string name="account_type_junior">Junior Account</string>
    <string name="junior_Account">Junior Account</string>
    <string name="upgrade_Regular_Account">Upgrade To Regular Account</string>
    <string name="cancel_Junior_Account">Cancel Junior Account</string>
    <string name="please_enter_the_junior_account_old_PIN">Please enter the junior account old PIN</string>
    <string name="please_enter_the_junior_account_new_PIN">Please enter the junior account new PIN</string>
    <string name="please_re_enter_the_junior_account_new_PIN">Please re-enter the junior account new PIN</string>
    <string name="please_enter_the_junior_account_PIN">Please enter the junior account PIN</string>
    <string name="update_Information">Update Information</string>
    <string name="upgrade_hint1">We have received your junior account upgrade application.</string>
    <string name="upgrade_hint2">Please bring your valid Identification to the any OneMoney branch for identity authentication.</string>
    <string name="are_you_sure_you_want_to_delete_junior_account">If there are cashbacks, points, coupons, etc., they will become invalid immediately after cancellation</string>
    <string name="there_are_unfinished_transactions">There are unfinished transactions that cannot be cancelled.</string>
    <string name="delete_junior_success_hint">If the junior account account has balance， it will be automatically transferred to the parant/Guardian account</string>
    <string name="check_Junior_Account">Check Junior Account</string>
    <string name="enquiry_Type">Enquiry Type</string>
    <string name="enquiry_Currency_Account">Enquiry Currency Account</string>
    <string name="check">Check</string>
    <string name="notice_hint">Please contact your parents/guardian to reset the PIN</string>
    <string name="personal_title2">Parent/Guardian Info</string>
    <string name="personal_link_date">Link Date</string>
    <string name="payaccount_sequence">Payment Account Sequence</string>
    <string name="pay_account_hint2">Long press and drag</string>
    <string name="pay_account_hint3">The deduction order is related to the account status and balance.If the account deduction fails, it will be postponed to the next account. When all accounts cannot be deducted, it will cause transaction deduction failure. Please ensure that the account balance is sufficient and the account status is normal.</string>
    <string name="zipit_title">ZIPIT</string>
    <string name="zipit_reversal_title">ZIPIT Reversal</string>
    <string name="account_btn_loyalty_rewards">Loyalty Rewards</string>
    <string name="coupon_str">Coupon</string>
    <string name="membership_Points">Membership Points</string>
    <string name="membership_Bonus">Membership Bonus</string>
    <string name="history_label_order_no">Order No.</string>
    <string name="order_No_">Order No.</string>
    <string name="checkout_label_order_no">Order No.</string>
    <string name="checkout_label_order_amount">Order Amount</string>
    <string name="order_Amount">Order Amount</string>
    <string name="cashback_Account">Cashback Account</string>
    <string name="account_label_cashback_account">Cashback Account</string>
    <string name="discount_Amount">Discount Amount</string>
    <string name="wallet_Account_Payment_Amount">Wallet Account Payment Amount</string>
    <string name="bank_Account_Payment_Amount">Bank Account Payment Amount</string>
    <string name="cashBack_Account_Payment_Amount">CashBack Account Payment Amount</string>
    <string name="wallet_Account_Refund_Amount">Wallet Account Refund Amount</string>
    <string name="cashBack_Account_Refund_Amount">CashBack Account Refund Amount</string>
    <string name="related_Payment_Transaction_No">Related Payment Transaction No.</string>
    <string name="history_btn_cashback">Cashback Reversal</string>
    <string name="common_tip_double_click_tip">Press again to exit</string>
    <string name="channel_no">Channel Transaction No.</string>
    <string name="send_money_reversal">Send Money Reversal</string>
    <string name="school_bill_code">School\'s Biller Code</string>
    <string name="common_label_sender">Sender Reference</string>
    <string name="account_btn_edit">Edit</string>
    <string name="security_title_loginpwd_unlock">Login password unlock</string>
    <!--新增-->
    <string name="adjustment_reason">Adjustment Reason</string>
    <string name="cashout_hint">Please check the branch information.Once submitted, it cannot be changed</string>
    <string name="remittance_code">Remittance Code</string>
    <string name="no_find_hint">Not Found</string>
    <string name="adjust">Account Adjustment</string>
    <string name="cash_hint">Please go to the designated branch for withdrawal in a timely manner</string>
    <string name="register_success">Registered Successfully</string>
</resources>
