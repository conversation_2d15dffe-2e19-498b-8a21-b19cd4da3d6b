apply plugin: 'com.android.library'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply plugin: 'kotlin-kapt'
//配置相应的引入参数
def cfg = rootProject.ext.configuration // 配置
def libs = rootProject.ext.libraries // 库

android {
    compileSdkVersion cfg.compileVersion
    buildToolsVersion cfg.buildToolsVersion
    defaultConfig {
        minSdkVersion cfg.minSdk
        targetSdkVersion cfg.targetSdk
        versionCode cfg.version_code
        versionName cfg.version_name
        // 控制日志Log 输出打印
        buildConfigField("boolean", "enableLog", "true")
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [AROUTER_MODULE_NAME: project.getName()]
            }
        }
    }

    buildTypes {
        release {
            // 控制日志Log 输出打印
            buildConfigField("boolean", "enableLog", "false")
            minifyEnabled true
            consumerProguardFiles 'proguard-rules.pro'
        }
        debug {
            // 控制日志Log 输出打印
            buildConfigField("boolean", "enableLog", "true")
            minifyEnabled false
            consumerProguardFiles 'proguard-rules.pro'
        }
    }

    dataBinding {
        enabled true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    /*repositories {
        flatDir {
            dir 'libs'
        }
    }*/
}
repositories  {
    flatDir{ dirs 'libs'  }
}
dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    implementation "androidx.appcompat:appcompat:${libs.androidx_appcompat}"
    implementation "androidx.constraintlayout:constraintlayout:${libs.androidx_constraintlayout}"

    //模块间通信Aapi
    api project(path: ':BaseModule:FunctionApi')

    //基础库
    api "androidx.recyclerview:recyclerview:${libs.androidx_recyclerview}"
    api "com.google.android.material:material:${libs.meterial}"

    //网络请求相关
    api "com.squareup.okhttp3:logging-interceptor:${libs.logging_interceptor}"
    api "com.squareup.retrofit2:retrofit:${libs.retrofit}"
    api "com.squareup.retrofit2:converter-gson:${libs.converter_gson}"
    api "com.squareup.retrofit2:adapter-rxjava2:${libs.adapter_rxjava2}"
    api "io.reactivex.rxjava2:rxandroid:${libs.rxandroid}"
    api "io.reactivex.rxjava2:rxjava:${libs.rxjava}"

    //butterKnife
    api "com.jakewharton:butterknife:${libs.butterknife}"
    kapt "com.jakewharton:butterknife-compiler:${libs.butterknife_compiler}"

    //权限
    api "com.github.tbruyelle:rxpermissions:${libs.rxpermissions}"

    //日志
    api "com.orhanobut:logger:${libs.logger}"

    //服务器连接
    api "com.github.franmontiel:PersistentCookieJar:${libs.persistent_cookie_jar}"

    //页面路由
    //api "com.alibaba:arouter-api:${libs.arouter_api}"
    //annotationProcessor "com.alibaba:arouter-compiler:${libs.arouter_compiler}"
    api 'com.alibaba:arouter-annotation:1.0.6'
    api (name: 'arouter-api-v1.5.3-release', ext: 'aar')

    //MVVM框架
    api "androidx.lifecycle:lifecycle-extensions:${libs.lifecycle_extensions}"

    //顶部标题栏
    api "com.gyf.immersionbar:immersionbar:${libs.immersionbar}"
    api "com.gyf.immersionbar:immersionbar-components:${libs.immersionbar_components}"

    //原型头像
    api "de.hdodenhof:circleimageview:${libs.circleimageview}"

//    //图片加载
    api "com.github.bumptech.glide:glide:${libs.glide}"
    kapt "com.github.bumptech.glide:compiler:${libs.glide_compiler}"

    //hutool
    api "cn.hutool:hutool-all:${libs.hutool_all}"
//    api files('libs/commons-codec-1.11.jar')

    //侧滑删除组件https://github.com/daimajia/AndroidSwipeLayout
    api "com.daimajia.swipelayout:library:${libs.swipelayout}"

    //智能刷新
//    api "com.scwang.smartrefresh:SmartRefreshLayout:${libs.SmartRefreshLayout}"
    api 'com.scwang.smartrefresh:SmartRefreshLayout:*******'

    //TabLayout扩展类https://github.com/H07000223/FlycoTabLayout/blob/master/README_CN.md
    api "com.flyco.tablayout:FlycoTabLayout_Lib:${libs.FlycoTabLayout_Lib}"

    //吸顶效果
    api "com.gavin.com.library:stickyDecoration:${libs.stickyDecoration}"

    //本地广播
    api "androidx.localbroadcastmanager:localbroadcastmanager:${libs.localbroadcastmanager}"

    //扫码的库
    api "cn.bingoogolapple:bga-qrcode-zbar:${libs.bga_qrcode_zbar}"

    //二维码
    api "com.google.zxing:core:${libs.zxing_code}"

    //时间滚轮
    api "com.cncoderx.wheelview:library:${libs.wheelview_lib}"

    //侧滑退出
    api "cn.bingoogolapple:bga-swipebacklayout:${libs.swipebacklayout}"

    //图片放大
    api "com.github.chrisbanes:PhotoView:${libs.photoview}"

    //lottie
    api "com.airbnb.android:lottie:${libs.lottie}"

    //英文翻译
//    api files('libs/pinyin4j-2.5.0.jar')
    api('com.rrtx.android:xMarketingSDKMFS:1.2.7-SNAPSHOT') {
        exclude group: 'com.google.zxing'
        exclude group: 'com.squareup.okio'
        exclude group: 'com.scwang.smartrefresh.layout'
        exclude group: 'com.bumptech.glide', module: 'glide'
    }
//    api(fileTree("libs"))
//    api(name: 'XmarketingSdk-debug', ext: 'aar') {
//        exclude group: 'com.google.zxing'
//        exclude group: 'com.squareup.okio'
//        exclude group: 'com.scwang.smartrefresh.layout'
//        exclude group: 'com.bumptech.glide', module: 'glide'
//    }

    api('com.alibaba:fastjson:1.2.60')

}

