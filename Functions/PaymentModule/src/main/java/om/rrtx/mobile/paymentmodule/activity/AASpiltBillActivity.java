package om.rrtx.mobile.paymentmodule.activity;

import android.content.Intent;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.DecimalFormatSymbols;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.dialog.CommonCurrencyDialog;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.paymentmodule.adapter.AASpiltBillRVAdapter;
import om.rrtx.mobile.paymentmodule.bean.ByMobileBean;
import om.rrtx.mobile.paymentmodule.bean.ContactBean;
import om.rrtx.mobile.paymentmodule.bean.PayerInfosBean;
import om.rrtx.mobile.paymentmodule.presenter.AASplitBillPresenter;
import om.rrtx.mobile.paymentmodule.utils.EditInputFilter;
import om.rrtx.mobile.paymentmodule.utils.LogUtil;
import om.rrtx.mobile.paymentmodule.utils.SelectContactHelper;
import om.rrtx.mobile.paymentmodule.view.AASplitBillView;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.BigDecimalUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SoftKeyBoardListener;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * aa转账页面
 */
@Route(path = ARouterPath.Payment.AASpiltBillActivity)
public class AASpiltBillActivity extends BaseSuperActivity<AASplitBillView, AASplitBillPresenter>
        implements TextWatcher, AASplitBillView {


    private final int SELECTREQUESTCODE = 0x1;

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.changeMonth)
    TextView mChangeMonth;
    @BindView(R2.id.chooseTv)
    TextView mChooseTv;
    @BindView(R2.id.totalEt)
    EditText mTotalEt;
    @BindView(R2.id.collectTv)
    TextView mCollectTv;
    @BindView(R2.id.totalTv)
    TextView mTotalTv;
    @BindView(R2.id.remarkEt)
    EditText mRemarkEt;
    @BindView(R2.id.line)
    View mLine;
    @BindView(R2.id.total)
    TextView mTotal;
    @BindView(R2.id.totalHint)
    TextView mTotalHint;
    @BindView(R2.id.line2)
    View mLine2;
    @BindView(R2.id.noEqualRv)
    RecyclerView mNoEqualRv;
    @BindView(R2.id.currencyTv)
    TextView mCurrencyTv;
    @BindView(R2.id.selectCurrencyTv)
    TextView mSelectCurrencyTv;
    @BindView(R2.id.line1)
    View mLine1;
    @BindView(R2.id.currencyHint)
    TextView mCurrencyHint;
    @BindView(R2.id.currencyArrIv)
    ImageView mCurrencyArrIv;

    private ContactBean mContactBean;
    /**
     * 输入金额
     */
    private String mAmountStr;
    private List<ByMobileBean> mSelectList;
    /**
     * 是否是等额的标识
     */
    private boolean isNonEqual;
    private String mTotalAmt;
    private String mRecAmt;
    private AASpiltBillRVAdapter mAaSpiltBillRVAdapter;

    CommonCurrencyDialog mCommonCurrencyDialog;
    String mCurrencyStr = "USD";

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    protected int createContentView() {
        return R.layout.payment_activity_aaspilt_bill;
    }

    @Override
    protected AASplitBillPresenter createPresenter() {
        return new AASplitBillPresenter(mContext);
    }

    @Override
    public void doGetExtra() {
        mCurrencyStr = CurrencyUtils.setCurrency(this,getIntent().getStringExtra(CommonConstants.Transmit.CURRENCY));
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.payment_ic_back);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setText(R.string.bill_title_split_bill);
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));

        if (isNonEqual) {
            //非等额
            mTotalHint.setText(R.string.common_label_total_amt);
        } else {
            //等额
            mTotalHint.setText(R.string.bill_label_per_person);
        }

    }

    @Override
    public void initDate() {
        super.initDate();

        mSelectList = new ArrayList<>();

        //设置EditText的过滤
        InputFilter[] filters = {new EditInputFilter(mContext, 9999999999.99)};
        mTotalEt.setFilters(filters);

        mNoEqualRv.setLayoutManager(new LinearLayoutManager(mContext) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });
        mAaSpiltBillRVAdapter = new AASpiltBillRVAdapter(mContext);
        mNoEqualRv.setAdapter(mAaSpiltBillRVAdapter);

        mTotalEt.requestFocus();
    }

    @Override
    public void initListener() {
        super.initListener();

        mChangeMonth.setOnClickListener(aaSpiltCustom);
        mChooseTv.setOnClickListener(aaSpiltCustom);
        mLeftBg.setOnClickListener(aaSpiltCustom);
        mSelectCurrencyTv.setOnClickListener(aaSpiltCustom);
        mTotalEt.addTextChangedListener(this);
        SoftKeyBoardListener.setListener(mContext, new SoftKeyBoardListener.OnSoftKeyBoardChangeListener() {
            @Override
            public void keyBoardShow(int height) {
                //键盘显示
                LogUtil.e("done", "keyBoardShow: ");
            }

            @Override
            public void keyBoardHide(int height) {
                LogUtil.e("done", "keyBoardHide: ");
                //键盘隐藏的时候设置相应的金额
                mAmountStr = mTotalEt.getText().toString();
                if (!TextUtils.isEmpty(mAmountStr)) {
                    mAmountStr = BigDecimalUtils.getStandardAmount(mAmountStr, 2, BigDecimal.ROUND_HALF_UP);
                    mTotalEt.setText(mAmountStr);
                    mTotalEt.setSelection(mAmountStr.length());
                }
            }
        });
        mCollectTv.setOnClickListener(aaSpiltCustom);
    }

    private CustomClickListener aaSpiltCustom = new CustomClickListener() {

        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.changeMonth) {
                isNonEqual = !isNonEqual;

                if (isNonEqual) {
                    //非等额
                    mTotalEt.setVisibility(View.GONE);
                    mTotal.setVisibility(View.GONE);
                    mLine.setVisibility(View.GONE);
                    mChangeMonth.setText(R.string.bill_btn_equal_amount_mode);
                    mTotalHint.setText(R.string.common_label_total_amt);

                    mLine1.setVisibility(View.GONE);
                    mCurrencyHint.setVisibility(View.GONE);
                    mCurrencyArrIv.setVisibility(View.GONE);
                    mSelectCurrencyTv.setVisibility(View.GONE);
                } else {
                    //等额
                    mTotalEt.setVisibility(View.VISIBLE);
                    mTotal.setVisibility(View.VISIBLE);
                    mLine.setVisibility(View.VISIBLE);

                    mLine1.setVisibility(View.VISIBLE);
                    mCurrencyHint.setVisibility(View.VISIBLE);
                    mCurrencyArrIv.setVisibility(View.VISIBLE);
                    mSelectCurrencyTv.setVisibility(View.VISIBLE);

                    mChangeMonth.setText(R.string.bill_btn_different_amount_mode);
                    mTotalHint.setText(R.string.bill_label_per_person);
                    mNoEqualRv.setVisibility(View.GONE);
                    mLine2.setVisibility(View.GONE);
                }

                //更新状态清空数据
                mChooseTv.setText(R.string.bill_btn_choose);
                mTotalTv.setText(R.string.common_label_0_00);
                mRemarkEt.setText("");
                mCollectTv.setEnabled(false);
                mCollectTv.setBackgroundResource(R.drawable.payment_drawable_btn_unselect);
                mSelectList = new ArrayList<>();
                mTotalEt.setText("");
                mContactBean = null;
            } else if (view.getId() == R.id.chooseTv) {
                Log.e("AASpiltBillActivity>>>", "zfw onSingleClick>>>mCurrencyStr :"+mCurrencyStr );
                if (isNonEqual) {
                    Intent selectIntent = new Intent(mContext, SelectContactsActivity.class);
                    ContactBean contactBean = new ContactBean();
                    contactBean.setContactList(mSelectList);
                    selectIntent.putExtra(PaymentConstants.Transmit.JUMPFLAG, PaymentConstants.JumpFlag.NoEqual);
                    selectIntent.putExtra(PaymentConstants.Transmit.SELECTLIST, new Gson().toJson(contactBean));
                    selectIntent.putExtra(CommonConstants.Transmit.CURRENCY, mCurrencyStr);
                    startActivityForResult(selectIntent, SELECTREQUESTCODE);
                } else {
                    Intent selectIntent = new Intent(mContext, SelectContactsActivity.class);
                    ContactBean contactBean = new ContactBean();
                    contactBean.setContactList(mSelectList);
                    selectIntent.putExtra(PaymentConstants.Transmit.JUMPFLAG, PaymentConstants.JumpFlag.Equal);
                    selectIntent.putExtra(PaymentConstants.Transmit.SELECTLIST, new Gson().toJson(contactBean));
                    selectIntent.putExtra(CommonConstants.Transmit.CURRENCY, mCurrencyStr);
                    startActivityForResult(selectIntent, SELECTREQUESTCODE);
                }
            } else if (view.getId() == R.id.leftBg) {
                finish();
            } else if (view.getId() == R.id.collectTv) {
                if (isNonEqual) {
                    //非等额
                    String totalStr = SelectContactHelper.getTotalAmt(mSelectList);
                    String remark = mRemarkEt.getText().toString();

                    List<PayerInfosBean> payerInfos = SelectContactHelper.handlerSelectPayer(mSelectList, "");
                    mPresenter.requestCreateOrder(totalStr, totalStr, String.valueOf(mSelectList.size()), isNonEqual, remark, payerInfos,mCurrencyStr);
                } else {
                    DecimalFormatSymbols decimalFormatSymbols = new DecimalFormatSymbols(Locale.ENGLISH);
                    DecimalFormat df = new DecimalFormat("0.00", decimalFormatSymbols);
                    mTotalAmt = mTotalEt.getText().toString();
                    mTotalAmt = df.format(new BigDecimal(mTotalAmt));
                    String oneAmt = showAmount(mSelectList.size(), mTotalEt.getText().toString());
                    BigDecimal oneBd = new BigDecimal(oneAmt);
                    BigDecimal copies = new BigDecimal(mSelectList.size());
                    BigDecimal multiply = oneBd.multiply(copies);
                    mRecAmt = multiply.setScale(2, BigDecimal.ROUND_HALF_UP).toString();
                    String remark = mRemarkEt.getText().toString();

                    List<PayerInfosBean> payerInfos = SelectContactHelper.handlerSelectPayer(mSelectList, oneAmt);

                    mPresenter.requestCreateOrder(mTotalAmt, mRecAmt,
                            String.valueOf(mSelectList.size()), isNonEqual, remark, payerInfos,mCurrencyStr);
                }
            } else if (view.getId() == R.id.selectCurrencyTv) {
                mCommonCurrencyDialog = new CommonCurrencyDialog(AASpiltBillActivity.this);
                mCommonCurrencyDialog.setOnCurrencyClickListener(new CommonCurrencyDialog.OnCurrencyClickListener() {
                    @Override
                    public void onCurrencyClick(String currency) {
                        // 赋值货币符号
                        mCurrencyStr = currency;
                        mSelectCurrencyTv.setText(currency);
                        mCurrencyTv.setText(currency);
                        if (mCommonCurrencyDialog != null) {
                            mCommonCurrencyDialog = null;
                        }
                    }
                });
                if (mCommonCurrencyDialog != null) {
                    mCommonCurrencyDialog.show();
                }
            }
        }
    };

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == SELECTREQUESTCODE && resultCode == RESULT_OK) {
            //固定金额返回
            if (data != null) {
                String dataStr = data.getStringExtra(PaymentConstants.Transmit.DETAILSBEAN);
                mContactBean = new Gson().fromJson(dataStr, ContactBean.class);
                mSelectList = mContactBean.getContactList();
                String chooseStr = getString(R.string.bill_btn_choose) + "(" + (mSelectList.size() + 1) + ")";
                mChooseTv.setText(chooseStr);

                String total = mTotalEt.getText().toString();

                if (isShowBottom(mContactBean, total)) {
                    String showAmount = showAmount(mSelectList.size(), total);
                    LogUtil.e("TAG", "onActivityResult: " + StringUtils.formatAmount(showAmount));
                    mTotalTv.setText(StringUtils.formatAmount(showAmount));
                    mCollectTv.setEnabled(true);
                    mCollectTv.setBackgroundResource(R.drawable.payment_drawable_btn_select);
                } else {
                    mCollectTv.setEnabled(false);
                    mCollectTv.setBackgroundResource(R.drawable.payment_drawable_btn_unselect);
                }
            }
        }
    }

    /**
     * 底部按钮是否可以点击
     *
     * @param contactBean 选择的联系人
     * @param total       总数
     * @return 是否可以点击
     */
    private boolean isShowBottom(ContactBean contactBean, String total) {
        return contactBean != null && !TextUtils.isEmpty(total);
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        if (isShowBottom(mContactBean, s.toString())) {
            String showAmount = showAmount(mContactBean.getContactList().size(), s.toString());
            mTotalTv.setText(StringUtils.formatAmount(showAmount));
            mCollectTv.setEnabled(true);
            mCollectTv.setBackgroundResource(R.drawable.payment_drawable_btn_select);
        } else {
            mCollectTv.setEnabled(false);
            mCollectTv.setBackgroundResource(R.drawable.payment_drawable_btn_unselect);
        }
    }

    /**
     * 设置显示金额
     *
     * @param count     人数
     * @param amountStr 设置的金额
     */
    public String showAmount(int count, String amountStr) {
        if (count == 0) {
            return getString(R.string.common_label_0_00);
        }
        BigDecimal totalBd = new BigDecimal(amountStr);
        BigDecimal countBd = new BigDecimal(count + 1);
        return totalBd.divide(countBd, 2, BigDecimal.ROUND_UP).toString();
    }

    @Override
    public void createOrderSuccess() {
        LogUtil.e("done", "createOrderSuccess: " + mRecAmt + "-----" + mTotalAmt);
        SpiltBillSuccessActivity.jumpSpiltBillSuccess(mContext, mTotalAmt, mRecAmt,mCurrencyStr);
    }

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent != null) {
            String dataJson = intent.getStringExtra(PaymentConstants.Transmit.DETAILSBEAN);
            mContactBean = new Gson().fromJson(dataJson, ContactBean.class);
            mSelectList = mContactBean.getContactList();
            String chooseStr = getString(R.string.bill_btn_choose) + "(" + mSelectList.size() + ")";
            mChooseTv.setText(chooseStr);

            mCollectTv.setEnabled(true);
            mCollectTv.setBackgroundResource(R.drawable.payment_drawable_btn_select);

            //设置总金额
            mTotalAmt = SelectContactHelper.getTotalAmt(mSelectList);
            mTotalTv.setText(StringUtils.formatAmount(mTotalAmt));

            mNoEqualRv.setVisibility(View.VISIBLE);
            mLine2.setVisibility(View.VISIBLE);
            mAaSpiltBillRVAdapter.setData(mSelectList);

            mLine1.setVisibility(View.GONE);
            mCurrencyHint.setVisibility(View.GONE);
            mCurrencyArrIv.setVisibility(View.GONE);
            mSelectCurrencyTv.setVisibility(View.GONE);
        }
    }
}
