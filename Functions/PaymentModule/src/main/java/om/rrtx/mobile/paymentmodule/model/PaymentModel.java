package om.rrtx.mobile.paymentmodule.model;

import android.text.TextUtils;

import com.google.gson.Gson;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import okhttp3.RequestBody;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.bean.BillCodeBean;
import om.rrtx.mobile.functioncommon.bean.BillPayRecordBean;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.PaymentService;
import om.rrtx.mobile.paymentmodule.bean.AAPayDetailBean;
import om.rrtx.mobile.paymentmodule.bean.AARecDetailBean;
import om.rrtx.mobile.paymentmodule.bean.AirtimeListBean;
import om.rrtx.mobile.paymentmodule.bean.AppBundleListBean;
import om.rrtx.mobile.paymentmodule.bean.BundleListBean;
import om.rrtx.mobile.paymentmodule.bean.BundleTypeListBean;
import om.rrtx.mobile.paymentmodule.bean.ChangeFlagBean;
import om.rrtx.mobile.paymentmodule.bean.ContactBean;
import om.rrtx.mobile.paymentmodule.bean.MakePaymentCodeBean;
import om.rrtx.mobile.rrtxcommon1.bean.AppDictListBean;
import om.rrtx.mobile.rrtxcommon1.bean.CodeOrderBean;
import om.rrtx.mobile.paymentmodule.bean.PayListBean;
import om.rrtx.mobile.paymentmodule.bean.PayerInfosBean;
import om.rrtx.mobile.paymentmodule.bean.PubBean;
import om.rrtx.mobile.paymentmodule.bean.QrCodeBean;
import om.rrtx.mobile.paymentmodule.bean.AASpiltBillBean;
import om.rrtx.mobile.paymentmodule.bean.RecListBean;
import om.rrtx.mobile.functioncommon.bean.ZesaMerBean;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.PostJsonBody;
import om.rrtx.mobile.rrtxcommon1.net.BaseLoader;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.net.RetrofitServiceManager;

/**
 * <AUTHOR>
 * 登录模块的整天请求
 */
public class PaymentModel extends BaseLoader {

    private PaymentService mPaymentService;

    public PaymentModel() {
        mPaymentService = RetrofitServiceManager.getInstance().create(PaymentService.class);
    }

    public void commonPub(BaseObserver<PubBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        observe(mPaymentService.requestPub(map)).subscribe(baseObserver);
    }

    /**
     * 获取二维码
     *
     * @param amt    金额
     * @param remark 留言
     */
    public void requestQrCode(String userId, String amt, String remark, String currency, BaseObserver<QrCodeBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        if (!TextUtils.isEmpty(remark)) {
            map.put(PaymentConstants.Parameter.REMARK, remark);
        }
        map.put(PaymentConstants.Parameter.AMT, amt);
        map.put(PaymentConstants.Parameter.USERID, userId);
        map.put(PaymentConstants.Parameter.CURRENCY, currency);
        observe(mPaymentService.requestQrCode(map)).subscribe(baseObserver);
    }

    /**
     * 查询联系人列表
     */
    public void requestContactList(String userId, BaseObserver<ContactBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(PaymentConstants.Parameter.USERID, userId);
        observe(mPaymentService.requestContactList(map)).subscribe(baseObserver);
    }

    /**
     * 创建AA订单
     */
    public void requestCreateOrder(String userId, String totalAmt, String recAmt, String splitNumber, boolean splitType, String remark, List<PayerInfosBean> payerInfos, String currency, BaseObserver<Object> baseObserver) {

        AASpiltBillBean aaSpiltBillBean = new AASpiltBillBean();
        aaSpiltBillBean.setPayerInfos(payerInfos);
        aaSpiltBillBean.setRecAmt(recAmt);
        aaSpiltBillBean.setSplitNumber(splitNumber);
        aaSpiltBillBean.setSplitType(splitType ? "1" : "0");
        aaSpiltBillBean.setTotalAmt(totalAmt);
        aaSpiltBillBean.setUserId(userId);
        aaSpiltBillBean.setCurrency(currency);
        if (!TextUtils.isEmpty(remark)) {
            aaSpiltBillBean.setRemark(remark);
        }

        RequestBody requestBody = PostJsonBody.create(new Gson().toJson(aaSpiltBillBean));

        observe(mPaymentService.requestCreateOrder(requestBody)).subscribe(baseObserver);
    }


    /**
     * 查询发布AA的列表
     */
    public void requestRecList(String userId, int pageNum, int pageSize, BaseObserver<RecListBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(PaymentConstants.Parameter.PAGENUM, String.valueOf(pageNum));
        map.put(PaymentConstants.Parameter.PAGESIZE, String.valueOf(pageSize));
        map.put(PaymentConstants.Parameter.USERID, userId);
        observe(mPaymentService.requestRecList(map)).subscribe(baseObserver);
    }

    /**
     * 收到AA的列表
     */
    public void requestPayList(String userId, int pageNum, int pageSize, BaseObserver<PayListBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(PaymentConstants.Parameter.PAGENUM, String.valueOf(pageNum));
        map.put(PaymentConstants.Parameter.PAGESIZE, String.valueOf(pageSize));
        map.put(PaymentConstants.Parameter.USERID, userId);
        observe(mPaymentService.requestPayList(map)).subscribe(baseObserver);
    }

    /**
     * 请求Pending发起订单详情
     */
    public void requestRecDetail(String userId, String orderNo, BaseObserver<AARecDetailBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(PaymentConstants.Parameter.ORDERNO, orderNo);
        map.put(PaymentConstants.Parameter.USERID, userId);
        observe(mPaymentService.requestRecDetail(map)).subscribe(baseObserver);
    }

    /**
     * Pending付款订单详情
     */
    public void requestPayDetail(String userId, String orderNo, BaseObserver<AAPayDetailBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(PaymentConstants.Parameter.DETAILORDERNO, orderNo);
        map.put(PaymentConstants.Parameter.USERID, userId);
        observe(mPaymentService.requestPayDetail(map)).subscribe(baseObserver);
    }

    /**
     * 请求关闭订单
     */
    public void requestCancelOrder(String userId, String orderNo, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(PaymentConstants.Parameter.ORDERNO, orderNo);
        map.put(PaymentConstants.Parameter.USERID, userId);
        observe(mPaymentService.requestCancelOrder(map)).subscribe(baseObserver);
    }

    /**
     * 支付AA订单
     */
    public void requestPayOrder(String detailOrderNo, String paymentPassword, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(PaymentConstants.Parameter.PAYMENTPASSWORD, detailOrderNo);
        map.put(PaymentConstants.Parameter.DETAILORDERNO, detailOrderNo);
        observe(mPaymentService.requestPayOrder(map)).subscribe(baseObserver);
    }

    /**
     * 请求付款二维码页面
     */
    public void requestMakePaymentCode(String userId, BaseObserver<MakePaymentCodeBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(PaymentConstants.Parameter.USERID, userId);
        observe(mPaymentService.requestMakePaymentCode(map)).subscribe(baseObserver);
    }

    /**
     * 请求付款二维码页面
     */
    public void requestPayCheck(String userId, String qrCode, BaseNoDialogObserver<CodeOrderBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(PaymentConstants.Parameter.QRCODE, qrCode);
        map.put(PaymentConstants.Parameter.USERID, userId);
        observe(mPaymentService.requestPayCheck(map)).subscribe(baseObserver);
    }

    /**
     * 查询联系人是否需要更新
     */
    public void requestIsContactChange(String userId, String hashToken, BaseObserver<ChangeFlagBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(PaymentConstants.Parameter.HASHTOKEN, hashToken);
        map.put(PaymentConstants.Parameter.USERID, userId);
    }

    public void queryZesaNo(String meterNo, BaseObserver<ZesaMerBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(PaymentConstants.Parameter.METERNO, meterNo);
        observe(mPaymentService.queryZesaNo(map)).subscribe(baseObserver);
    }

    public void getLimitAmount(BaseObserver<ZesaMerBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        observe(mPaymentService.getLimitAmount(map)).subscribe(baseObserver);
    }

    /**
     * 获取区域信息
     *
     * @param baseObserver 回调
     */
    public void requestDictCodeList(BaseNoDialogObserver<AppDictListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put("dictCode", "register_address");
        observe(mPaymentService.requestDictCodeList(map)).subscribe(baseObserver);
    }

    public void merchantCodeCheck(String merchantCode, BaseNoDialogObserver<BillCodeBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(PaymentConstants.Parameter.MERCHANTCODE, merchantCode);
        observe(mPaymentService.merchantCodeCheck(map)).subscribe(baseObserver);
    }

    public void billerCodeCheck(String billerCategory,String merchantCode, BaseObserver<BillCodeBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(PaymentConstants.Parameter.MERCHANTCODE, merchantCode);
        map.put(CommonConstants.Parameter.BILL_CATEGORY, billerCategory);
        observe(mPaymentService.billerCodeCheck(map)).subscribe(baseObserver);
    }

    public void getZesaPayRecord(int pageNum, int pageSize, String startDate, String endDate, BaseObserver<BillPayRecordBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.PAGE_NUM, pageNum + "");
        map.put(CommonConstants.Parameter.PAGE_SIZE, pageSize + "");
        map.put(CommonConstants.Parameter.START_DATE, startDate);
        map.put(CommonConstants.Parameter.END_DATE, endDate);
        observe(mPaymentService.getZesaPayRecord(map)).subscribe(baseObserver);
    }

    public void getAppBundleList(String bundleType, BaseObserver<AppBundleListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.BUNDLE_TYPE, bundleType);
        observe(mPaymentService.getAppBundleList(map)).subscribe(baseObserver);
    }

    public void getBundleTypeList(BaseObserver<BundleTypeListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.CUSTOMER_TYPE, "00");
        // 新增OneMoney Exclusive Bundles类型，移除的话会显示之前版本的内容；没有排序
        map.put("bundleVersion", "2.0");
        observe(mPaymentService.getBundleTypeList(map)).subscribe(baseObserver);
    }

    public void getAirtimeList(String currency, BaseObserver<AirtimeListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        observe(mPaymentService.getAirtimeList(map)).subscribe(baseObserver);
    }

    public void getBundleList(String bundleType, String appNameCode, String currency, int page, int limit,
                              BaseObserver<BundleListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(CommonConstants.Parameter.BUNDLE_TYPE, bundleType);
        map.put(CommonConstants.Parameter.APP_NAME_CODE, appNameCode);
        map.put(CommonConstants.Parameter.CURRENCY, currency);
        map.put(CommonConstants.Parameter.PAGE, page + "");
        map.put(CommonConstants.Parameter.LIMIT, limit + "");
        observe(mPaymentService.getBundleList(map)).subscribe(baseObserver);
    }

}
