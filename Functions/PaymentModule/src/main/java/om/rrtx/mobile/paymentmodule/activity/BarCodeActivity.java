package om.rrtx.mobile.paymentmodule.activity;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.res.Resources;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.paymentmodule.utils.QRCodeEncoder;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.AdaptScreenUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;

/**
 * <AUTHOR>
 * 条码页面
 */
public class BarCodeActivity extends BaseSuperActivity {

    @BindView(R2.id.barCodeIv)
    ImageView mBarCodeIv;
    @BindView(R2.id.barCodeTv)
    TextView mBarCodeTv;
    @BindView(R2.id.rootCl)
    ConstraintLayout mRootCl;
    private String mQrCode;
    private CodeBroadcastReceiver mCodeReceiver;

    public static void jumpBarCode(Context context, String qrCode) {
        Intent intent = new Intent(context, BarCodeActivity.class);
        intent.putExtra(PaymentConstants.Transmit.QRCODE, qrCode);
        context.startActivity(intent);
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        mQrCode = getIntent().getStringExtra(PaymentConstants.Transmit.QRCODE);
    }

    @Override
    protected int createContentView() {
        return R.layout.payment_activity_bar_code;
    }

    @Override
    public Resources getResources() {
        return AdaptScreenUtils.adaptHeight(super.getResources(), designWidthInpx);
    }

    @Override
    protected BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();


        //注册刷新的广播
        IntentFilter intentFilter = new IntentFilter();
        mCodeReceiver = new CodeBroadcastReceiver();
        intentFilter.addAction(getPackageName() + PaymentConstants.CodeRefreshAction);
        LocalBroadcastManager.getInstance(this).registerReceiver(mCodeReceiver, intentFilter);
    }

    @Override
    public void onWindowFocusChanged(boolean hasFocus) {
        super.onWindowFocusChanged(hasFocus);
        if (!TextUtils.isEmpty(mQrCode)) {
            showBarCode(mQrCode);
        } else {
            finish();
        }
    }

    private void showBarCode(String qrCode) {
        Bitmap barCodeBit = QRCodeEncoder.syncEncodeBarcode(qrCode,
                mBarCodeIv.getWidth(),
                mBarCodeIv.getHeight(),
                0);

        mBarCodeIv.setImageBitmap(barCodeBit);

        //条形码
        mBarCodeTv.setText(qrCode);
    }

    @Override
    public void initListener() {
        super.initListener();
        mRootCl.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                Intent intent = new Intent(mContext, MakePaymentAllActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                startActivity(intent);
            }
        });
    }

    @Override
    public void onBackPressed() {
        Intent intent = new Intent(mContext, MakePaymentAllActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        startActivity(intent);
    }

    private class CodeBroadcastReceiver extends BroadcastReceiver {

        @Override
        public void onReceive(Context context, Intent intent) {
            if (intent != null) {
                mQrCode = intent.getStringExtra(PaymentConstants.Transmit.QRCODE);
                showBarCode(mQrCode);
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mCodeReceiver != null) {
            LocalBroadcastManager.getInstance(this).unregisterReceiver(mCodeReceiver);
        }
    }
}
