package om.rrtx.mobile.paymentmodule.presenter;

import android.content.Context;
import android.text.TextUtils;

import om.rrtx.mobile.paymentmodule.bean.MakePaymentCodeBean;
import om.rrtx.mobile.rrtxcommon1.bean.CodeOrderBean;
import om.rrtx.mobile.paymentmodule.bean.PubBean;
import om.rrtx.mobile.paymentmodule.model.PaymentModel;
import om.rrtx.mobile.paymentmodule.view.MakePayementView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * AA订单页面的P层
 */
public class MakePaymentPresenter extends BasePresenter<MakePayementView> {

    private final PaymentModel mPaymentModel;
    private Context mContext;

    public MakePaymentPresenter(Context context) {
        mPaymentModel = new PaymentModel();
        mContext = context;
    }

    /**
     * 获取请求付款二维码
     */
    public void requestMakePaymentCode() {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestMakePaymentCode(userId,new BaseObserver<MakePaymentCodeBean>(mContext) {
                @Override
                public void requestSuccess(MakePaymentCodeBean makePaymentCodeBean) {
                    if (getView() != null) {
                        getView().makePaymentCodeSuccess(makePaymentCodeBean);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        }else {
            mPaymentModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mPaymentModel.requestMakePaymentCode(userId,new BaseObserver<MakePaymentCodeBean>(mContext) {
                        @Override
                        public void requestSuccess(MakePaymentCodeBean makePaymentCodeBean) {
                            if (getView() != null) {
                                getView().makePaymentCodeSuccess(makePaymentCodeBean);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    /**
     * 获取请求付款二维码
     */
    public void requestPayCheck(String code) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestPayCheck(userId,code, new BaseNoDialogObserver<CodeOrderBean>(mContext) {
                @Override
                public void requestSuccess(CodeOrderBean payCheckBean) {
                    if (getView() != null) {
                        getView().payCheckSuccess(payCheckBean);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestLoopFail(sResMsg);
                    }
                }
            });
        }else {
            mPaymentModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mPaymentModel.requestPayCheck(userId,code, new BaseNoDialogObserver<CodeOrderBean>(mContext) {
                        @Override
                        public void requestSuccess(CodeOrderBean payCheckBean) {
                            if (getView() != null) {
                                getView().payCheckSuccess(payCheckBean);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestLoopFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }
}
