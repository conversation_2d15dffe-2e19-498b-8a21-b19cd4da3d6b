package om.rrtx.mobile.paymentmodule.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import om.rrtx.mobile.functioncommon.bean.RecordBean
import om.rrtx.mobile.paymentmodule.databinding.ItemPayRecordBinding
import om.rrtx.mobile.rrtxcommon1.base.BaseAdapterDataCallback
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.TimeUtils


class PayRecordAdapter(val callBack: BaseAdapterDataCallback<RecordBean>) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    var data = ArrayList<RecordBean>()

    lateinit var context: Context
    lateinit var inflater: LayoutInflater

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        context = recyclerView.context
        inflater = LayoutInflater.from(context)
    }

    fun setNewData(list: ArrayList<RecordBean>) {
        data = list
        notifyDataSetChanged()
    }
    fun addData(list: ArrayList<RecordBean>) {
        data.addAll(list)
        notifyDataSetChanged()
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        val binding = ItemPayRecordBinding.inflate(inflater, parent, false)
        return object : RecyclerView.ViewHolder(binding.root) {}
    }

    override fun getItemCount() = data.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val binding =
            DataBindingUtil.getBinding<ItemPayRecordBinding>(holder.itemView) as ItemPayRecordBinding

        val bean = data[position]
        binding.titleTv.text = bean.billOrderNo
        binding.amtTv.text = bean.currency + " " + StringUtils.formatAmount(bean.trxAmt)
        binding.timeTv.text = TimeUtils.getTime(bean.trxTime)
        if ("30"==bean.status)
        {
            binding.resendTv.visibility = View.VISIBLE
        }else{
            binding.resendTv.visibility = View.GONE
        }
        binding.resendTv.setOnClickListener(object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                callBack.callBack(position, data[position])
            }
        })
    }
}