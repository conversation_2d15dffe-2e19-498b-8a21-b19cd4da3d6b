package om.rrtx.mobile.paymentmodule.view;

import om.rrtx.mobile.paymentmodule.bean.RecListBean;

/**
 * <AUTHOR>
 * pending页面的V层
 */
public interface LssuedView {

    /**
     * 请求失败
     *
     * @param sResMsg 失败信息
     */
    void requestFail(String sResMsg);

    /**
     * 获取列表数据成功
     *
     * @param sResData 成功数据
     */
    void recListSuccess(RecListBean sResData);

    /**
     * 请求更多数据成功
     *
     * @param sResData 返回实体
     */
    void recListMoreSuccess(RecListBean sResData);
}
