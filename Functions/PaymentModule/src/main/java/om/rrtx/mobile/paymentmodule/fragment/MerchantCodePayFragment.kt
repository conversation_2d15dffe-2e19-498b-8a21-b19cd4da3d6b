package om.rrtx.mobile.paymentmodule.fragment

import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.util.Log
import android.view.View
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.Gson
import kotlinx.android.synthetic.main.payment_fragment_merchant.code_ed
import kotlinx.android.synthetic.main.payment_fragment_merchant.nameTv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.functioncommon.dialog.CommonCurrencyDialog
import om.rrtx.mobile.paymentmodule.R
import om.rrtx.mobile.paymentmodule.databinding.PaymentFragmentMerchantBinding
import om.rrtx.mobile.paymentmodule.viewModel.MakePaymentWM
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.EditInputFilter
import om.rrtx.mobile.rrtxcommon1.base.BaseFragment
import om.rrtx.mobile.rrtxcommon1.bean.QueryOrderBean
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.BigDecimalUtils
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.SoftKeyBoardListener
import om.rrtx.mobile.rrtxcommon1.utils.TextChangeEndListener
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil
import java.math.BigDecimal

/**汇款吗付款**/
class MerchantCodePayFragment : BaseFragment<MakePaymentWM, PaymentFragmentMerchantBinding>() {

    var curCurrency: String = "ZWG"

    override fun initView() {

        curCurrency = CurrencyUtils.setCurrency(mContext,"ZWG")
        dataBinding.currencyTv.text = curCurrency
        val drawableLeft = ResourceHelper.getDrawable(R.drawable.ic_extend)
        drawableLeft?.setBounds(0, 0, 14.pt2px(), 8.pt2px())
        dataBinding.currencyTv.setCompoundDrawables(null, null, drawableLeft, null);
        dataBinding.currencyTv.compoundDrawablePadding = 10.pt2px()

        initVMListener()
        val filters = arrayOf<InputFilter>(EditInputFilter(mContext))
        dataBinding.moneyTv.filters = filters
        //键盘的监听
        SoftKeyBoardListener.setListener(
            activity,
            object : SoftKeyBoardListener.OnSoftKeyBoardChangeListener {
                override fun keyBoardShow(height: Int) {
                    //键盘显示
                    Log.e("done", "keyBoardShow: ")
                }

                override fun keyBoardHide(height: Int) {
                    Log.e("done", "keyBoardHide: ")
                    if (code_ed.text.length > 4){
                        nameTv.text = ""
                        viewModel.merchantCodeCheck(code_ed.text.toString())
                    }
                    //键盘隐藏的时候设置相应的金额
                    var mAmountStr = dataBinding.moneyTv.text.toString();
                    if (!TextUtils.isEmpty(mAmountStr)) {
                        mAmountStr = BigDecimalUtils.getStandardAmount(
                            mAmountStr,
                            2,
                            BigDecimal.ROUND_HALF_UP
                        );
                        dataBinding.moneyTv.setText(mAmountStr);
                        dataBinding.moneyTv.setSelection(mAmountStr.length);
                    }
                }
            })

    }

    private fun initVMListener() {
        viewModel.checkMerchantCodeSuccess.observe(requireActivity()) {
            nameTv.text = it.merTradeName
        }
        // 订单信息回调
        viewModel.orderInfoBeanLD.observe(this) {
            val queryOrderBean = QueryOrderBean(
                "",
                code_ed.text.toString(),
                "",
                "",
                "",
                "",
                "",
                dataBinding.moneyTv.text.toString(),
                "",
                curCurrency,
                CommonConstants.PaymentProduct.MERCHANT_CODE_PAY
            )

            ARouter.getInstance().build(ARouterPath.Cashier.CashierPayActivity)
                .withString(BaseConstants.Transmit.JSON, Gson().toJson(queryOrderBean))
                .withString(BaseConstants.Transmit.ORDERJSON, Gson().toJson(it))
                .navigation()
        }
    }

    override fun initListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    dataBinding.currencyTv -> {
                        val mCommonCurrencyDialog =
                            CommonCurrencyDialog(
                                activity!!
                            )
                        mCommonCurrencyDialog.setOnCurrencyClickListener(CommonCurrencyDialog.OnCurrencyClickListener { currency -> // 赋值货币符号
                            curCurrency = currency
                            dataBinding.currencyTv.text = curCurrency
                            dataBinding.moneyTv.text.clear()
                        })
                        mCommonCurrencyDialog.show()
                    }

                    dataBinding.nextTv -> {
                        if (TextUtils.isEmpty(nameTv.text)) {
                            ToastUtil.show(
                                mContext,
                                resources.getString(R.string.merchant_name_hint))
                            return
                        }
                        var mAmountStr = dataBinding.moneyTv.text.toString()
                        mAmountStr = BigDecimalUtils.getStandardAmount(
                            mAmountStr,
                            2,
                            BigDecimal.ROUND_HALF_UP
                        )
                        dataBinding.moneyTv.setText(mAmountStr)
                        dataBinding.moneyTv.setSelection(mAmountStr.length)
                        if (TextUtils.equals(mAmountStr, "0") || TextUtils.equals(
                                mAmountStr,
                                "0.00"
                            )
                        ) {
                            ToastUtil.show(
                                mContext,
                                resources.getString(R.string.payment_tip_amount_must_greater) + mAmountStr
                            )
                            return
                        }

                        viewModel.checkMerCodePayOrder(
                            code_ed.text.toString(),
                            dataBinding.moneyTv.text.toString(),
                            curCurrency
                        )
                    }
                }
            }

        }.apply {
            dataBinding.currencyTv.setOnClickListener(this)
            dataBinding.nextTv.setOnClickListener(this)
        }

        dataBinding.moneyTv.onFocusChangeListener = View.OnFocusChangeListener { view, b ->
            Log.e("done", "焦点: "+b)
            if (!b) {
                var mAmountStr = dataBinding.moneyTv.text.toString()
                if (!TextUtils.isEmpty(mAmountStr)) {
                    mAmountStr =
                        BigDecimalUtils.getStandardAmount(mAmountStr, 2, BigDecimal.ROUND_HALF_UP)
                    dataBinding.moneyTv.setText(mAmountStr)
                    dataBinding.moneyTv.setSelection(mAmountStr.length)
                }
            }
        }

        dataBinding.moneyTv.addTextChangedListener(object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                orderIsFull()
            }
        })

        /*dataBinding.codeEd.addTextChangedListener(object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                if (editable.length > 4) {
                    nameTv.text = ""
                    viewModel.merchantCodeCheck(editable.toString())
                }
            }
        })*/

        dataBinding.codeEd.onFocusChangeListener = View.OnFocusChangeListener { view, b ->
            Log.e("done", "焦点: "+b)
            if (!b && code_ed.text.length > 4) {
                nameTv.text = ""
                viewModel.merchantCodeCheck(code_ed.text.toString())
            }
        }
    }

    fun orderIsFull() {
        var isMoney = dataBinding.moneyTv.text.trim()
            .isNotEmpty() && (dataBinding.moneyTv.text.toString() != "0.00" || dataBinding.moneyTv.text.toString() != "0")
        if (isMoney &&
            dataBinding.codeEd.text.isNotEmpty() &&
            dataBinding.codeEd.text.length > 4
        ) {
            dataBinding.nextTv.isEnabled = true
            dataBinding.nextTv.setBackgroundResource(R.drawable.common_usable_btn)
        } else {
            dataBinding.nextTv.isEnabled = false
            dataBinding.nextTv.setBackgroundResource(R.drawable.common_unusable_btn)
        }
    }

    override fun createViewLayoutId() = R.layout.payment_fragment_merchant

}