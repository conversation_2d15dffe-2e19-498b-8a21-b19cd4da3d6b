package om.rrtx.mobile.paymentmodule.adapter;

import android.content.Context;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.bean.ByMobileBean;
import om.rrtx.mobile.paymentmodule.utils.EditInputFilter;
import om.rrtx.mobile.rrtxcommon1.base.BaseHolder;
import om.rrtx.mobile.rrtxcommon1.image.ImageLoaderManager;
import om.rrtx.mobile.rrtxcommon1.utils.BigDecimalUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.widget.SWImageView;

/**
 * <AUTHOR>
 * 联系人添加金额页面适配器
 */
public class AddAmtConRVAdapter extends RecyclerView.Adapter<BaseHolder> {
    private Context mContext;
    private List<ByMobileBean> mBeanLists;
    private AAItemCallBack mItemCallBack;

    public AddAmtConRVAdapter(Context context) {
        mContext = context;
        mBeanLists = new ArrayList<>();
    }

    @NonNull
    @Override
    public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.payment_item_add_amt, parent, false);
        return new BaseHolder(rootView);
    }

    @Override
    public void onBindViewHolder(@NonNull BaseHolder holder, int position) {
        ByMobileBean byMobileBean = mBeanLists.get(position);

        SWImageView swipeLayout = holder.getView(R.id.heardIv);
        ImageLoaderManager.getInstance().disPlayImage(mContext, byMobileBean.getUserAvatar(), R.drawable.payment_ic_head_contact_default, swipeLayout);

        TextView userName = holder.getView(R.id.nameTv);
        userName.setText(byMobileBean.getRealName());

        TextView phoneTv = holder.getView(R.id.phoneTv);
        phoneTv.setText(StringUtils.stringMask(byMobileBean.getMobileAreaCode(), byMobileBean.getMobile()));


        TextWatcher textWatcher = new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {

            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {

            }

            @Override
            public void afterTextChanged(Editable s) {
                if (mItemCallBack != null) {
                    mItemCallBack.afterTextChanged(byMobileBean, position, s);
                }
            }
        };

        EditText amtEt = holder.getView(R.id.amtEt);

        amtEt.setOnFocusChangeListener(new View.OnFocusChangeListener() {
            @Override
            public void onFocusChange(View v, boolean hasFocus) {
                if (mItemCallBack != null) {
                    mItemCallBack.focusChange(hasFocus, position);
                }
            }
        });

        //设置EditText的过滤
        InputFilter[] filters = {new EditInputFilter(mContext, 9999999999.99)};
        amtEt.setFilters(filters);

        if (amtEt.getTag() != null && amtEt.getTag() instanceof TextWatcher) {
            amtEt.removeTextChangedListener((TextWatcher) amtEt.getTag());
        }

        if (!TextUtils.isEmpty(byMobileBean.getAmount())) {
            String standardAmount = BigDecimalUtils.getStandardAmount(byMobileBean.getAmount(), 2, BigDecimal.ROUND_HALF_UP);
            amtEt.setText(standardAmount);
            //将光标移至文字末尾
            amtEt.setSelection(standardAmount.length());
        } else {
            amtEt.setText("");
            amtEt.setHint(R.string.common_label_0_00);
        }

        amtEt.addTextChangedListener(textWatcher);
        amtEt.setTag(textWatcher);
    }

    @Override
    public int getItemCount() {
        return mBeanLists != null ? mBeanLists.size() : 0;
    }

    public void setData(List<ByMobileBean> list) {
        mBeanLists = list;
        notifyDataSetChanged();
    }

    public void setItemCallBack(AAItemCallBack itemCallBack) {
        mItemCallBack = itemCallBack;
    }

    public interface AAItemCallBack {
        void afterTextChanged(ByMobileBean byMobileBean, int position, Editable s);

        void focusChange(boolean hasFocus, int position);
    }
}
