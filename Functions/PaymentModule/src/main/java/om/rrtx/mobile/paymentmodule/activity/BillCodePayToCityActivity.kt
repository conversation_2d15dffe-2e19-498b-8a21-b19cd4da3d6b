package om.rrtx.mobile.paymentmodule.activity

import android.content.Context
import android.content.Intent
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.util.Log
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.google.gson.Gson
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.payment_activity_billcode_city.currency_tv
import kotlinx.android.synthetic.main.payment_activity_billcode_city.icon_iv
import kotlinx.android.synthetic.main.payment_activity_billcode_city.moneyTv
import kotlinx.android.synthetic.main.payment_activity_billcode_city.nameCity_ed
import kotlinx.android.synthetic.main.payment_activity_billcode_city.nameDang_ed
import kotlinx.android.synthetic.main.payment_activity_billcode_city.name_tv
import kotlinx.android.synthetic.main.payment_activity_billcode_city.nextTv
import kotlinx.android.synthetic.main.payment_activity_billcode_city.title_tv
import kotlinx.android.synthetic.main.payment_base_title.backIv
import kotlinx.android.synthetic.main.payment_base_title.leftBg
import kotlinx.android.synthetic.main.payment_base_title.statusView
import kotlinx.android.synthetic.main.payment_base_title.titleTv
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.functioncommon.activity.ComResultActivity
import om.rrtx.mobile.functioncommon.bean.BillCodeBean
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean
import om.rrtx.mobile.functioncommon.bean.ComResultBean
import om.rrtx.mobile.functioncommon.bean.OrderInfoBean
import om.rrtx.mobile.functioncommon.bean.PaymentSuccessBean
import om.rrtx.mobile.functioncommon.callback.CashierCallBack1
import om.rrtx.mobile.functioncommon.dialog.CommonCurrencyDialog
import om.rrtx.mobile.functioncommon.utils.CashierManager1
import om.rrtx.mobile.paymentmodule.R
import om.rrtx.mobile.paymentmodule.databinding.PaymentActivityBillcodeCityBinding
import om.rrtx.mobile.paymentmodule.viewModel.BillPayWM
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.EditInputFilter
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.BigDecimalUtils
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.SoftKeyBoardListener
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.TextChangeEndListener
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil
import java.math.BigDecimal

/**04党费，01市政**/
class BillCodePayToCityActivity : BaseActivity<PaymentActivityBillcodeCityBinding>() {

    lateinit var mVM: BillPayWM
    lateinit var mBean: BillCodeBean
    var curCurrency: String = "ZWG"
    override fun createContentView() = R.layout.payment_activity_billcode_city

    override fun doGetExtra() {
        val json = intent.getStringExtra(CommonConstants.Parameter.JSON_BEAN)
        if (StringUtils.isValidString(json)) {
            mBean = Gson().fromJson(json, BillCodeBean::class.java)
        }
    }

    override fun initWorkspaceAction() {
        mVM = ViewModelProvider(this).get(BillPayWM::class.java)
        mVM.orderInfoBeanSuccess.observe(this) {
            showCashier(it)
        }
    }

    private fun showCashier(mOrderInfoBean: OrderInfoBean) {
        var payType = CommonConstants.CashierPaymentType.MERCHANT_CODE
        var transType = CommonConstants.TransType.Bill_PAY
        var orderInfo = mOrderInfoBean.commodityRemark
        var amt = moneyTv.text.toString()
        var merName = if (mBean.billerCategory == "01") {
            nameCity_ed.text.toString()
        } else {
            nameDang_ed.text.toString()
        }
        Log.e("====bean==", "billerCategory=" + mBean.billerCategory)
        val cashierOrderInfoBean = CashierOrderInfoBean.Builder()
            .setPayType(payType)
            .setTransType(transType)
            .setBillSubType(mBean.billerCategory)
            .setOrderSource(CommonConstants.OrderSource.EXTERNALORDER)
            .setOrderInfo(orderInfo)
            .setMerNo(mBean.recMerNo)
            .setMerName(merName).setCurrency(mOrderInfoBean.currency)
            .setOrderAmt(amt).setOrderNo(mOrderInfoBean.trxOrderNo)
            .setAfterDisCountAmt("")
            .setIsFixMethod(true)
            .builder()
        CashierManager1(this, Gson().toJson(cashierOrderInfoBean), object : CashierCallBack1 {
            override fun paymentSuccess(dataJson: String) {
                //支付成功页面
                Log.e("TAG", "paymentSuccess: $dataJson")
                val fromJson = Gson().fromJson(dataJson, PaymentSuccessBean::class.java)
                val comResultBean = ComResultBean(
                    getString(R.string.bill_Payment),
                    s2 = fromJson.currency + " " + mOrderInfoBean.amt,//fromJson.paymentAmt,
                    flag = BaseConstants.JumpFlag.PAY_MERCHANT_CODE
                )

                ComResultActivity.jump(
                    mContext,
                    comResultBean
                )

            }

            override fun paymentFailed(message: String) {

            }

            override fun leftClick() {
                //finish()
            }

            override fun rightClick() {
                finish()
            }

        }).showCashierDialog()
    }

    override fun initView() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true, 0.2f)
            .init()
        dataBinding.includeTitle.apply {
            titleTv.setText(R.string.bill_Payment)
            titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
            backIv.setBackgroundResource(R.drawable.common_ic_back_black)
            statusView.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
            titleTv.setBackgroundColor(resources.getColor(R.color.color_FFFFFF))
        }

        curCurrency = CurrencyUtils.setCurrency(mContext,"ZWG")
        currency_tv.text = curCurrency
        if (mBean.billerCategory == "01") {
            val drawableLeft = ResourceHelper.getDrawable(R.drawable.ic_extend)
            drawableLeft?.setBounds(0, 0, 14.pt2px(), 8.pt2px())
            currency_tv.setCompoundDrawables(null, null, drawableLeft, null);
            currency_tv.compoundDrawablePadding = 10.pt2px()
            nameCity_ed.visibility = View.VISIBLE
            nameDang_ed.visibility = View.GONE
            icon_iv.setImageResource(R.drawable.billcode_city)
            title_tv.text = getString(R.string.city_of_Harare)
            name_tv.text = getString(R.string.name_city)
        } else {
            nameCity_ed.visibility = View.GONE
            nameDang_ed.visibility = View.VISIBLE
            icon_iv.setImageResource(R.drawable.billcode_dang)
            title_tv.text = getString(R.string.ZANU_PF_Membership_Payments)
            name_tv.text = getString(R.string.membership_Number)
        }

        val filters = arrayOf<InputFilter>(EditInputFilter(mContext))
        moneyTv.filters = filters

        //键盘的监听
        SoftKeyBoardListener.setListener(
            this,
            object : SoftKeyBoardListener.OnSoftKeyBoardChangeListener {
                override fun keyBoardShow(height: Int) {
                    //键盘显示
                    Log.e("done", "keyBoardShow: ")
                }

                override fun keyBoardHide(height: Int) {
                    Log.e("done", "keyBoardHide: ")
                    //键盘隐藏的时候设置相应的金额
                    var mAmountStr = dataBinding.moneyTv.text.toString();
                    if (!TextUtils.isEmpty(mAmountStr)) {
                        mAmountStr = BigDecimalUtils.getStandardAmount(
                            mAmountStr,
                            2,
                            BigDecimal.ROUND_HALF_UP
                        );
                        moneyTv.setText(mAmountStr);
                        moneyTv.setSelection(mAmountStr.length);
                    }
                }
            })
    }

    override fun initListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    currency_tv -> {
                        if (mBean.billerCategory == "01") {
                            val mCommonCurrencyDialog =
                                CommonCurrencyDialog(
                                    this@BillCodePayToCityActivity
                                )
                            mCommonCurrencyDialog.setOnCurrencyClickListener(CommonCurrencyDialog.OnCurrencyClickListener { currency -> // 赋值货币符号
                                curCurrency = currency
                                currency_tv.text = curCurrency
                                moneyTv.setText("")
                            })
                            mCommonCurrencyDialog.show()

                        }
                    }

                    leftBg -> {
                        onBackPressed()
                    }

                    nextTv -> {
                        var mAmountStr = moneyTv.text.toString()
                        mAmountStr = BigDecimalUtils.getStandardAmount(
                            mAmountStr,
                            2,
                            BigDecimal.ROUND_HALF_UP
                        )
                        moneyTv.setText(mAmountStr)
                        moneyTv.setSelection(mAmountStr.length)
                        if (TextUtils.equals(mAmountStr, "0") || TextUtils.equals(
                                mAmountStr,
                                "0.00"
                            )
                        ) {
                            ToastUtil.show(
                                mContext,
                                resources.getString(R.string.payment_tip_amount_must_greater) + mAmountStr
                            )
                            return
                        }
                        var merName = if (mBean.billerCategory == "01") {
                            nameCity_ed.text.toString()
                        } else {
                            nameDang_ed.text.toString()
                        }
                        mVM.requestMerchantOrder(
                            mBean.billerCategory,
                            mBean.recMerNo,
                            curCurrency,
                            moneyTv.text.toString(),
                            merName,
                            "",
                            "",
                            "",
                            "",
                            "",
                            "",
                            "",
                            ""
                        )
                    }
                }
            }

        }.apply {
            currency_tv.setOnClickListener(this)
            nextTv.setOnClickListener(this)
            leftBg.setOnClickListener(this)
        }

        moneyTv.onFocusChangeListener = View.OnFocusChangeListener { view, b ->
            if (!b) {
                var mAmountStr = dataBinding.moneyTv.text.toString()
                if (!TextUtils.isEmpty(mAmountStr)) {
                    mAmountStr =
                        BigDecimalUtils.getStandardAmount(mAmountStr, 2, BigDecimal.ROUND_HALF_UP)
                    moneyTv.setText(mAmountStr)
                    moneyTv.setSelection(mAmountStr.length)
                }
            }
        }

        moneyTv.addTextChangedListener(object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                orderIsFull()
            }
        })
        nameCity_ed.addTextChangedListener(object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                orderIsFull()
            }
        })
        nameDang_ed.addTextChangedListener(object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                orderIsFull()
            }
        })
    }

    fun orderIsFull() {
        var isMoney = moneyTv.text.trim()
            .isNotEmpty() && (moneyTv.text.toString() != "0.00" || moneyTv.text.toString() != "0")
        var isName = if (mBean.billerCategory == "01") {
            nameCity_ed.text.isNotEmpty()
        } else {
            nameDang_ed.text.isNotEmpty()
        }
        if (isMoney && isName) {
            nextTv.isEnabled = true
            nextTv.setBackgroundResource(R.drawable.common_usable_btn)
        } else {
            nextTv.isEnabled = false
            nextTv.setBackgroundResource(R.drawable.common_unusable_btn)
        }
    }


    companion object {
        fun jump(context: Context, bean: BillCodeBean) {
            val intent = Intent(context, BillCodePayToCityActivity::class.java)
            intent.putExtra(CommonConstants.Parameter.JSON_BEAN, Gson().toJson(bean))
            context.startActivity(intent)
        }
    }
}