package om.rrtx.mobile.paymentmodule.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.launcher.ARouter;
import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;

/**
 * <AUTHOR>
 * 转账成功页面
 */
public class SpiltBillSuccessActivity extends BaseSuperActivity {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.showAmt)
    TextView mShowAmt;
    @BindView(R2.id.doneTv)
    TextView mDoneTv;
    private String mTotalAMt;
    private String mRecAmt;
    String mCurrencyStr;

    /**
     * 跳转到本页面的方法
     *
     * @param context  上下文
     * @param totalAmt 总金额
     * @param recAmt   应收金额
     */
    public static void jumpSpiltBillSuccess(Context context, String totalAmt, String recAmt,String currency) {
        Intent intent = new Intent(context, SpiltBillSuccessActivity.class);
        intent.putExtra(PaymentConstants.Transmit.TOTALAMT, totalAmt);
        intent.putExtra(PaymentConstants.Transmit.RECAMT, recAmt);
        intent.putExtra(CommonConstants.Transmit.CURRENCY, currency);
        context.startActivity(intent);
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        mTotalAMt = getIntent().getStringExtra(PaymentConstants.Transmit.TOTALAMT);
        mRecAmt = getIntent().getStringExtra(PaymentConstants.Transmit.RECAMT);
        mCurrencyStr = getIntent().getStringExtra(CommonConstants.Transmit.CURRENCY);
    }

    @Override
    protected int createContentView() {
        return R.layout.payment_activity_spilt_bill_success;
    }

    @Override
    protected BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();


        mBackIv.setVisibility(View.GONE);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setText(R.string.bill_title_split_bill);
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));
    }

    @Override
    public void initDate() {
        super.initDate();
        if (TextUtils.isEmpty(mRecAmt)) {
            String showAmt = getString(R.string.bill_label_total_bill_amount) +" "+CurrencyUtils.setCurrency(mContext, mCurrencyStr) +" "+ StringUtils.formatAmount(mTotalAMt);
            mShowAmt.setText(showAmt);
        } else {
            String showAmt = getString(R.string.bill_label_total_bill_amount) +" " + CurrencyUtils.setCurrency(mContext, mCurrencyStr) +" "+ StringUtils.formatAmount(mTotalAMt) + "\n"
                    + getString(R.string.bill_label_amt_receivable) +" "+ CurrencyUtils.setCurrency(mContext, mCurrencyStr) +" "+ StringUtils.formatAmount(mRecAmt);
            mShowAmt.setText(showAmt);
        }
    }


    @Override
    public void initListener() {
        super.initListener();
        mDoneTv.setOnClickListener(spiltBillCustom);
    }

    private CustomClickListener spiltBillCustom = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.doneTv) {
                ARouter.getInstance()
                        .build(ARouterPath.HomePath.HomeActivity)
                        .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .navigation();
            }
        }
    };

    @Override
    public void onBackPressed() {
        ARouter.getInstance()
                .build(ARouterPath.HomePath.HomeActivity)
                .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                .navigation();
    }
}
