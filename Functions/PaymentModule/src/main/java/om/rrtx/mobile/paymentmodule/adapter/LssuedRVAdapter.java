package om.rrtx.mobile.paymentmodule.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.bean.RecListBean;
import om.rrtx.mobile.rrtxcommon1.base.BaseHolder;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;

/**
 * <AUTHOR>
 * 发起AA的列表页面
 */
public class LssuedRVAdapter extends RecyclerView.Adapter<BaseHolder> {
    private Context mContext;
    private List<RecListBean.RecordsBean> mBeans;
    private RVAdapterItemClickListener<RecListBean.RecordsBean> mClickListener;

    public LssuedRVAdapter(Context context) {
        mContext = context;
        mBeans = new ArrayList<>();
    }

    @NonNull
    @Override
    public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.payment_item_lssued, parent, false);
        return new BaseHolder(rootView);
    }

    @Override
    public void onBindViewHolder(@NonNull BaseHolder holder, int position) {
        RecListBean.RecordsBean recordsBean = mBeans.get(position);

        //总金额
        TextView totalMoney = holder.getView(R.id.totalMoney);
        totalMoney.setText(StringUtils.formatAmount(recordsBean.getTotalAmt()));

        //币种
        TextView usd = holder.getView(R.id.usd);
        usd.setText(recordsBean.getCurrencyType());

        //收到金额
        TextView receivedTv = holder.getView(R.id.receivedTv);
        String receivedStr = mContext.getString(R.string.aa_label_has_received) + ": " +CurrencyUtils.setCurrency(mContext,recordsBean.getCurrencyType()) + " " + StringUtils.formatAmount(recordsBean.getActRecAmt());
        receivedTv.setText(receivedStr);

        //设置时间
        TextView timeTv = holder.getView(R.id.timeTv);
        timeTv.setText(recordsBean.getTradeTime());

        //背景
        ConstraintLayout clBg = holder.getView(R.id.clBg);

        //订单状态
        TextView state = holder.getView(R.id.state);

        //付款人数
        TextView countTv = holder.getView(R.id.count);
        String countStr = recordsBean.getRecOrderNumber() + "/" + recordsBean.getTotalOrderNumber();
        countTv.setText(countStr);

        ImageView dot = holder.getView(R.id.dot);

        //订单状态 10:待支付 20:待支付 30:支付完成 40:已取消
        String orderStatus = recordsBean.getOrderStatus();
        if (TextUtils.equals(PaymentConstants.AAOrderStatus.PENDING, orderStatus) ||
                TextUtils.equals(PaymentConstants.AAOrderStatus.PENDINGOR, orderStatus)) {
            clBg.setBackgroundResource(R.drawable.payment_bg_lssued_pending);

            totalMoney.setTextColor(mContext.getResources().getColor(R.color.common_ye_F3881E));
            usd.setTextColor(mContext.getResources().getColor(R.color.common_ye_F3881E));

            state.setText(mContext.getString(R.string.aa_label_pending));

            dot.setVisibility(View.VISIBLE);
            dot.setImageResource(R.drawable.payment_drawable_dot_blue);

            countTv.setVisibility(View.VISIBLE);
            countTv.setTextColor(mContext.getResources().getColor(R.color.common_ye_F3881E));
        } else if (TextUtils.equals(PaymentConstants.AAOrderStatus.COMPLETE, orderStatus)) {
            clBg.setBackgroundResource(R.drawable.payment_bg_lssued_complete);

            totalMoney.setTextColor(mContext.getResources().getColor(R.color.color_17904B));
            usd.setTextColor(mContext.getResources().getColor(R.color.color_17904B));

            state.setText(mContext.getString(R.string.complete));

            dot.setVisibility(View.VISIBLE);
            dot.setImageResource(R.drawable.payment_drawable_dot_green);

            countTv.setVisibility(View.VISIBLE);
            countTv.setTextColor(mContext.getResources().getColor(R.color.color_17904B));
        } else if (TextUtils.equals(PaymentConstants.AAOrderStatus.CANCELLED, orderStatus)) {
            clBg.setBackgroundResource(R.drawable.payment_bg_lssued_cancelled);

            totalMoney.setTextColor(mContext.getResources().getColor(R.color.color_777777));
            usd.setTextColor(mContext.getResources().getColor(R.color.color_777777));

            state.setText(mContext.getString(R.string.aa_label_cancelled));

            dot.setVisibility(View.GONE);
            countTv.setVisibility(View.GONE);
        }

        holder.itemView.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                if (mClickListener != null) {
                    mClickListener.itemClickListener(recordsBean, position);
                }
            }
        });
    }

    public void setClickListener(RVAdapterItemClickListener<RecListBean.RecordsBean> clickListener) {
        mClickListener = clickListener;
    }

    @Override
    public int getItemCount() {
        return mBeans != null ? mBeans.size() : 0;
    }

    public void setData(List<RecListBean.RecordsBean> list) {
        mBeans = list;
        notifyDataSetChanged();
    }

    public void addData(List<RecListBean.RecordsBean> list) {
        mBeans.addAll(list);
        notifyDataSetChanged();
    }
}
