package om.rrtx.mobile.paymentmodule.bean;

import java.util.List;

/**
 * <AUTHOR>
 * Pending发起订单详情实体类
 */
public class AARecDetailBean {

    /**
     * orderNo : 8109332787812646984
     * totalAmt : 3.00
     * recAmt : 2.00
     * actRecAmt : 1.00
     * totalOrderNumber : 2
     * recOrderNumber : 1
     * unpaidOrderNumber : 1
     * tradeTime : 2020-02-28 12:52:07
     * currencyType : USD
     * splitType :
     * orderStatus : 20
     * remark :
     * userAvatar :
     * paidPayList : [{"payCstNo":"8098182352095674399","payCstName":"fengxz","payMobile":"+6017731140281","transAmt":"1.00","detailOrderStatus":"30","userAvatar":""}]
     * unpaidPayList : [{"payCstNo":"8097571352245973004","payCstName":"hongdou","payMobile":"+60123456789000","transAmt":"1.00","detailOrderStatus":"10","userAvatar":""}]
     */

    private String orderNo;
    private String totalAmt;
    private String recAmt;
    private String actRecAmt;
    private int totalOrderNumber;
    private int recOrderNumber;
    private int unpaidOrderNumber;
    private String tradeTime;
    private String currencyType;
    private String splitType;
    private String orderStatus;
    private String remark;
    private String userAvatar;
    private List<PayListBean> paidPayList;
    private List<PayListBean> unpaidPayList;

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(String totalAmt) {
        this.totalAmt = totalAmt;
    }

    public String getRecAmt() {
        return recAmt;
    }

    public void setRecAmt(String recAmt) {
        this.recAmt = recAmt;
    }

    public String getActRecAmt() {
        return actRecAmt;
    }

    public void setActRecAmt(String actRecAmt) {
        this.actRecAmt = actRecAmt;
    }

    public int getTotalOrderNumber() {
        return totalOrderNumber;
    }

    public void setTotalOrderNumber(int totalOrderNumber) {
        this.totalOrderNumber = totalOrderNumber;
    }

    public int getRecOrderNumber() {
        return recOrderNumber;
    }

    public void setRecOrderNumber(int recOrderNumber) {
        this.recOrderNumber = recOrderNumber;
    }

    public int getUnpaidOrderNumber() {
        return unpaidOrderNumber;
    }

    public void setUnpaidOrderNumber(int unpaidOrderNumber) {
        this.unpaidOrderNumber = unpaidOrderNumber;
    }

    public String getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(String tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(String currencyType) {
        this.currencyType = currencyType;
    }

    public String getSplitType() {
        return splitType;
    }

    public void setSplitType(String splitType) {
        this.splitType = splitType;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUserAvatar() {
        return userAvatar;
    }

    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }

    public List<PayListBean> getPaidPayList() {
        return paidPayList;
    }

    public void setPaidPayList(List<PayListBean> paidPayList) {
        this.paidPayList = paidPayList;
    }

    public List<PayListBean> getUnpaidPayList() {
        return unpaidPayList;
    }

    public void setUnpaidPayList(List<PayListBean> unpaidPayList) {
        this.unpaidPayList = unpaidPayList;
    }


    public static class PayListBean {
        /**
         * payCstNo : 8097571352245973004
         * payCstName : hongdou
         * payMobile : +60123456789000
         * transAmt : 1.00
         * detailOrderStatus : 10
         * userAvatar :
         */

        private String payCstNo;
        private String payCstName;
        private String payMobile;
        private String transAmt;
        private String detailOrderStatus;
        private String userAvatar;
        private String mobileAreaCode;
        private String currency;

        public String getPayCstNo() {
            return payCstNo;
        }

        public void setPayCstNo(String payCstNo) {
            this.payCstNo = payCstNo;
        }

        public String getPayCstName() {
            return payCstName;
        }

        public void setPayCstName(String payCstName) {
            this.payCstName = payCstName;
        }

        public String getPayMobile() {
            return payMobile;
        }

        public void setPayMobile(String payMobile) {
            this.payMobile = payMobile;
        }

        public String getTransAmt() {
            return transAmt;
        }

        public void setTransAmt(String transAmt) {
            this.transAmt = transAmt;
        }

        public String getDetailOrderStatus() {
            return detailOrderStatus;
        }

        public void setDetailOrderStatus(String detailOrderStatus) {
            this.detailOrderStatus = detailOrderStatus;
        }

        public String getUserAvatar() {
            return userAvatar;
        }

        public void setUserAvatar(String userAvatar) {
            this.userAvatar = userAvatar;
        }

        public String getMobileAreaCode() {
            return mobileAreaCode;
        }

        public void setMobileAreaCode(String mobileAreaCode) {
            this.mobileAreaCode = mobileAreaCode;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }
    }
}
