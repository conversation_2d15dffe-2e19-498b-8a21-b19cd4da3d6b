package om.rrtx.mobile.paymentmodule.presenter;

import android.content.Context;
import android.text.TextUtils;

import java.util.List;

import om.rrtx.mobile.paymentmodule.bean.PayerInfosBean;
import om.rrtx.mobile.paymentmodule.bean.PubBean;
import om.rrtx.mobile.paymentmodule.model.PaymentModel;
import om.rrtx.mobile.paymentmodule.view.AASplitBillView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * AA订单页面的P层
 */
public class AASplitBillPresenter extends BasePresenter<AASplitBillView> {

    private final PaymentModel mPaymentModel;
    private Context mContext;

    public AASplitBillPresenter(Context context) {
        mPaymentModel = new PaymentModel();
        mContext = context;
    }

    /**
     * 查询联系人列表
     */
    public void requestCreateOrder(String totalAmt, String recAmt, String splitNumber,
                                   boolean splitType, String remark, List<PayerInfosBean> payerInfos,String currency) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestCreateOrder(userId,totalAmt, recAmt, splitNumber, splitType, remark, payerInfos,currency,
                    new BaseObserver<Object>(mContext) {
                        @Override
                        public void requestSuccess(Object object) {
                            if (getView() != null) {
                                getView().createOrderSuccess();
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
        }else {
            mPaymentModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mPaymentModel.requestCreateOrder(userId,totalAmt, recAmt, splitNumber, splitType, remark, payerInfos,currency,
                            new BaseObserver<Object>(mContext) {
                                @Override
                                public void requestSuccess(Object object) {
                                    if (getView() != null) {
                                        getView().createOrderSuccess();
                                    }
                                }

                                @Override
                                public void requestFail(String sResMsg) {
                                    if (getView() != null) {
                                        getView().requestFail(sResMsg);
                                    }
                                }
                            });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }
}
