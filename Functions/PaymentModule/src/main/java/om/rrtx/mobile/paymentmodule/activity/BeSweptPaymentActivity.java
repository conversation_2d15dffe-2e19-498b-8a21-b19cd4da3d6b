package om.rrtx.mobile.paymentmodule.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean;
import om.rrtx.mobile.functioncommon.callback.CashierCallBack;
import om.rrtx.mobile.functioncommon.utils.CashierManager;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.rrtxcommon1.bean.CodeOrderBean;
import om.rrtx.mobile.functioncommon.bean.QrCodeCreateOrderBean;
import om.rrtx.mobile.paymentmodule.bean.SendMessageBean;
import om.rrtx.mobile.paymentmodule.presenter.BeSweptPaymentPresenter;
import om.rrtx.mobile.paymentmodule.view.BeSweptPaymentView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.dialog.DoubleDialog;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 被扫支付
 */
@Route(path = ARouterPath.Payment.BeSweptPaymentActivity)
public class BeSweptPaymentActivity extends BaseSuperActivity<BeSweptPaymentView, BeSweptPaymentPresenter>
        implements BeSweptPaymentView {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.iconIv)
    ImageView mIconIv;
    @BindView(R2.id.merchantTitle)
    TextView mMerchantTitle;
    @BindView(R2.id.moneyTv)
    TextView mMoneyTv;
    @BindView(R2.id.moneyUsd)
    TextView mMoneyUsd;
    @BindView(R2.id.payTv)
    TextView mPayTv;
    private String mQrCode;
    private DoubleDialog mDoubleDialog;
    private CodeOrderBean mPayCheckBean;
    private String mPayCheckBeanStr;
    private String mJumpFlag;
    private CashierManager mCashierManager;

    public static void jumpBeSweptPay(Context context, String payCheckBeanStr, String qrCode, String jumpFlag) {
        Intent intent = new Intent(context, BeSweptPaymentActivity.class);
        intent.putExtra(PaymentConstants.Transmit.PAYCHECKDETAILS, payCheckBeanStr);
        intent.putExtra(PaymentConstants.Transmit.QRCODE, qrCode);
        intent.putExtra(BaseConstants.Transmit.JUMPFLAG, jumpFlag);
        context.startActivity(intent);
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        mPayCheckBeanStr = getIntent().getStringExtra(PaymentConstants.Transmit.PAYCHECKDETAILS);
        mPayCheckBean = new Gson().fromJson(mPayCheckBeanStr, CodeOrderBean.class);
        mQrCode = getIntent().getStringExtra(PaymentConstants.Transmit.QRCODE);
        mJumpFlag = getIntent().getStringExtra(BaseConstants.Transmit.JUMPFLAG);
    }

    @Override
    protected int createContentView() {
        return R.layout.payment_activity_be_swept_payment;
    }

    @Override
    protected BeSweptPaymentPresenter createPresenter() {
        return new BeSweptPaymentPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.payment_ic_back);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setText(R.string.payment_title_payment);
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));

        mMoneyUsd.setText(CurrencyUtils.setCurrency(mContext, mPayCheckBean.getCurrency()));
    }


    @Override
    public void initDate() {
        super.initDate();

        if (mPayCheckBean != null) {
            String merStr = getString(R.string.payment_label_pay_to) + " " + mPayCheckBean.getMerName();
            mMerchantTitle.setText(merStr);
            mMoneyTv.setText(StringUtils.formatAmount(mPayCheckBean.getAmt()));
            payNow();
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mLeftBg.setOnClickListener(mCustomClickListener);
        mPayTv.setOnClickListener(mCustomClickListener);
    }

    private CustomClickListener mCustomClickListener = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.leftBg) {
                if (mDoubleDialog == null) {
                    mDoubleDialog = new DoubleDialog(mContext);
                    mDoubleDialog.setDoubleCallback(new DoubleDialog.DoubleCallback() {
                        @Override
                        public void leftCallback() {
                            mDoubleDialog.dismiss();
                            finish();
                        }

                        @Override
                        public void rightCallback() {
                            mDoubleDialog.dismiss();
                        }
                    });
                }
                mDoubleDialog.show();
                mDoubleDialog.show();
                mDoubleDialog.setMyTitle(getString(R.string.common_alert_prompt))
                        .setContentStr(getString(R.string.payment_alert_cancal_pay_tip))
                        .setLeftStr(getString(R.string.common_alert_continue))
                        .setLeftColor(getResources().getColor(R.color.color_F85A40))
                        .setRightStr(getString(R.string.common_alert_cancel))
                        .setRightColor(getResources().getColor(R.color.common_ye_F3881E));
            } else if (view.getId() == R.id.payTv) {
                payNow();
            }
        }
    };

    @Override
    public void qrCodePaymentSuccess(QrCodeCreateOrderBean qrCodeCreateOrderBean) {
//        String currencyStr = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.SAVECURRENCY, "");

        CashierOrderInfoBean cashierOrderInfoBean = new CashierOrderInfoBean.Builder()
                .setPayType(CommonConstants.CashierPaymentType.Cashier_Mer)
                .setPaymentProduct(CommonConstants.PaymentProduct.QRCODE)
                .setMerNo(mPayCheckBean.getMerNo())
                .setMerName(mPayCheckBean.getMerName())
                .setQrCode(mQrCode)
                .setOrderSource(BaseConstants.OrderSourceType.INSIDE)
                .setOrderAmt(mPayCheckBean.getAmt())
                .setOrderNo(qrCodeCreateOrderBean.getOrderNo())
                .setTransType(CommonConstants.TransType.Payment)
                .setCurrency(CurrencyUtils.setCurrency(mContext, qrCodeCreateOrderBean.getCurrency()))
                .setTransferToken(qrCodeCreateOrderBean.getOrderNo())
                .setOrderInfo(getResources().getString(R.string.checkout_label_default_commodity))
                .builder();

        mCashierManager = new CashierManager(this, new Gson().toJson(cashierOrderInfoBean), new CashierCallBack() {
            @Override
            public void paymentFailed(@NonNull String message) {
            }

            @Override
            public void cancelOrderPay() {
                mCashierManager.dismiss();
            }

            @Override
            public void forgotCallBack() {

                //忘记密码
                String mobileAreaCode = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.MOBILEAREACODE, "");
                String userMobile = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERMOBILE, "");
                SendMessageBean sendMessageBean = new SendMessageBean.Builder()
                        .setMessageTemplateType(BaseConstants.MessageTemplateType.ForgetPaymentPassword)
                        .setJumpFlag(BaseConstants.SendMessageJump.BESWEPTUNFIXEDPAYMENTACTIVITY)
                        .setMobile(userMobile)
                        .setMobileAreaCode(mobileAreaCode)
                        .build();

                ARouter.getInstance()
                        .build(ARouterPath.SecurityPath.PayPinSendActivity)
                        .withString(BaseConstants.Transmit.SENDMESSAGEJSON, new Gson().toJson(sendMessageBean))
                        .navigation();
            }

            @Override
            public void paymentSuccess(String dataJson) {
                PaymentSuccessActivity.jumpPaymentSuccess(mContext, dataJson);
            }
        });
        mCashierManager.showCashierDialog();
    }

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    public void onBackPressed() {
        if (mDoubleDialog == null) {
            mDoubleDialog = new DoubleDialog(mContext);
            mDoubleDialog.setDoubleCallback(new DoubleDialog.DoubleCallback() {
                @Override
                public void leftCallback() {
                    mDoubleDialog.dismiss();
                    finish();
                }

                @Override
                public void rightCallback() {
                    mDoubleDialog.dismiss();
                }
            });
        }
        mDoubleDialog.show();
        mDoubleDialog.show();
        mDoubleDialog.setMyTitle(getString(R.string.common_alert_prompt))
                .setContentStr(getString(R.string.payment_alert_cancal_pay_tip))
                .setLeftStr(getString(R.string.common_alert_continue))
                .setLeftColor(getResources().getColor(R.color.color_F85A40))
                .setRightStr(getString(R.string.common_alert_cancel))
                .setRightColor(getResources().getColor(R.color.common_ye_F3881E));

    }

    private void payNow() {
//        if (mPayCheckBean != null) {
//            if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.BARCODEFLAG)) {
//                mPresenter.requestQrCodePayment(PaymentConstants.PaymentProduct.BARCODE, mPayCheckBean.getMerNo(),
//                        mPayCheckBean.getMerName(), mPayCheckBean.getCheckstandNo(), mPayCheckBean.getAmt(),
//                        PaymentConstants.PaymentPasswordType.PSD, mQrCode,mMoneyUsd.getText().toString());
//            } else if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.QRCODEFLAG)) {
//                mPresenter.requestQrCodePayment(PaymentConstants.PaymentProduct.QRCODE, mPayCheckBean.getMerNo(),
//                        mPayCheckBean.getMerName(), mPayCheckBean.getCheckstandNo(), mPayCheckBean.getAmt(),
//                        PaymentConstants.PaymentPasswordType.PSD, mQrCode,mMoneyUsd.getText().toString());
//            }
//        }
    }
}
