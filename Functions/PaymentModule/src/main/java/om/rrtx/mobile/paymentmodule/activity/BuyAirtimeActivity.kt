package om.rrtx.mobile.paymentmodule.activity

import android.content.Intent
import android.view.View
import androidx.fragment.app.Fragment
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.gyf.immersionbar.ImmersionBar
import com.kapp.xmarketing.adapter.BaseVPAdapter
import kotlinx.android.synthetic.main.payment_activity_buy_airtime.tab
import kotlinx.android.synthetic.main.payment_activity_buy_airtime.vp
import kotlinx.android.synthetic.main.payment_base_title.backIv
import kotlinx.android.synthetic.main.payment_base_title.leftBg
import kotlinx.android.synthetic.main.payment_base_title.titleTv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.paymentmodule.R
import om.rrtx.mobile.paymentmodule.databinding.PaymentActivityReSendTokenBinding
import om.rrtx.mobile.paymentmodule.fragment.BuyAirtimeFragment
import om.rrtx.mobile.paymentmodule.fragment.BuyBundleFragment
import om.rrtx.mobile.paymentmodule.viewModel.BillPayWM
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseVVMActivity
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils

@Route(path = ARouterPath.Payment.BuyAirtimeActivity)
class BuyAirtimeActivity : BaseVVMActivity<BillPayWM, PaymentActivityReSendTokenBinding>() {
    override fun createContentView() = R.layout.payment_activity_buy_airtime


    override fun initView() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true, 0.2f)
            .init()
        titleTv.setText(R.string.buy_Airtime_Bundle)
        titleTv.setTextColor(resources.getColor(R.color.common_text_1d2129))
        backIv.setBackgroundResource(R.drawable.common_ic_back_black)
        titleTv.setBackgroundColor(resources.getColor(R.color.color_FFFFFF))


        viewModel.mobile =
            SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERMOBILE, "")
                .toString()

        vp.adapter = BaseVPAdapter(supportFragmentManager,
            arrayListOf<Fragment>(
                BuyAirtimeFragment(),
                BuyBundleFragment()
            )
        )

        tab.setViewPager(vp,
            arrayOf(getString(R.string.buy_airtime),
                getString(R.string.buy_Bundle)))
    }

    override fun initClickListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    leftBg -> {
                        onBackPressed()
                    }
                }
            }

        }.apply {
            leftBg.setOnClickListener(this)
        }
    }

    override fun onBackPressed() {
        ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
            .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP or Intent.FLAG_ACTIVITY_CLEAR_TOP)
            .navigation()
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == CommonConstants.ResultCode.Request && resultCode == CommonConstants.ResultCode.Success) {
            viewModel.mobile = data!!.getStringExtra(CommonConstants.Transmit.MOBILE).toString()
        }
    }

}