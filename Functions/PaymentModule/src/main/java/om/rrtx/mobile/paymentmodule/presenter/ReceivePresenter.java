package om.rrtx.mobile.paymentmodule.presenter;

import android.content.Context;
import android.text.TextUtils;

import om.rrtx.mobile.paymentmodule.bean.PayListBean;
import om.rrtx.mobile.paymentmodule.bean.PubBean;
import om.rrtx.mobile.paymentmodule.bean.RecListBean;
import om.rrtx.mobile.paymentmodule.model.PaymentModel;
import om.rrtx.mobile.paymentmodule.view.LssuedView;
import om.rrtx.mobile.paymentmodule.view.ReceiveView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * AA订单页面的P层
 */
public class ReceivePresenter extends BasePresenter<ReceiveView> {

    private final PaymentModel mPaymentModel;
    private Context mContext;

    public ReceivePresenter(Context context) {
        mPaymentModel = new PaymentModel();
        mContext = context;
    }

    /**
     * 发起的AA列表
     */
    public void requestPayList(int pagerNum, int pagerSize) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestPayList(userId,pagerNum, pagerSize,
                    new BaseObserver<PayListBean>(mContext) {
                        @Override
                        public void requestSuccess(PayListBean sResData) {
                            if (getView() != null) {
                                if (getView() != null) {
                                    getView().payListSuccess(sResData);
                                }
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
        }else {
            mPaymentModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mPaymentModel.requestPayList(userId,pagerNum, pagerSize,
                            new BaseObserver<PayListBean>(mContext) {
                                @Override
                                public void requestSuccess(PayListBean sResData) {
                                    if (getView() != null) {
                                        if (getView() != null) {
                                            getView().payListSuccess(sResData);
                                        }
                                    }
                                }

                                @Override
                                public void requestFail(String sResMsg) {
                                    if (getView() != null) {
                                        getView().requestFail(sResMsg);
                                    }
                                }
                            });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    /**
     * 发起的AA列表
     */
    public void requestMorePayList(int pagerNum, int pagerSize) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestPayList(userId,pagerNum, pagerSize,
                    new BaseObserver<PayListBean>(mContext) {
                        @Override
                        public void requestSuccess(PayListBean sResData) {
                            if (getView() != null) {
                                if (getView() != null) {
                                    getView().payListMoreSuccess(sResData);
                                }
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
        }else {
            mPaymentModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mPaymentModel.requestPayList(userId,pagerNum, pagerSize,
                            new BaseObserver<PayListBean>(mContext) {
                                @Override
                                public void requestSuccess(PayListBean sResData) {
                                    if (getView() != null) {
                                        if (getView() != null) {
                                            getView().payListMoreSuccess(sResData);
                                        }
                                    }
                                }

                                @Override
                                public void requestFail(String sResMsg) {
                                    if (getView() != null) {
                                        getView().requestFail(sResMsg);
                                    }
                                }
                            });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }
}
