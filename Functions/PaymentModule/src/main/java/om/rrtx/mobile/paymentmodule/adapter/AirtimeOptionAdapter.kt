package om.rrtx.mobile.paymentmodule.adapter

import android.content.Context
import android.graphics.Typeface
import android.text.Spannable
import android.text.SpannableString
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import kotlinx.android.synthetic.main.item_aritime_option.view.currency_tv
import kotlinx.android.synthetic.main.item_aritime_option.view.money_tv
import om.rrtx.mobile.paymentmodule.bean.AirtimeBean
import om.rrtx.mobile.paymentmodule.databinding.ItemAritimeOptionBinding
import om.rrtx.mobile.rrtxcommon1.base.BaseAdapterCallback
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener


class AirtimeOptionAdapter(var callback:BaseAdapterCallback) : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    lateinit var context: Context

    private var selectPosition = -1

    private var data = arrayListOf<AirtimeBean>()

    fun setSelectPosition(position: Int) {
        selectPosition = position
        notifyDataSetChanged()
    }

    fun isSelect() = selectPosition != -1

    fun setNewData(list: ArrayList<AirtimeBean>) {
        data = list
        notifyDataSetChanged()
    }

    fun getSelectAmount() = if (selectPosition != -1) data[selectPosition].price else "0.00"
    fun getAirtimePlanCode() = if (selectPosition != -1) data[selectPosition].airtimePlanCode else ""

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        context = recyclerView.context
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(context)
        val binding = ItemAritimeOptionBinding.inflate(
            inflater,
            parent,
            false
        )
        return object : RecyclerView.ViewHolder(binding.root) {}
    }

    override fun getItemCount() = data.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val binding =
            DataBindingUtil.getBinding<ItemAritimeOptionBinding>(holder.itemView) ?: return
        val bean = data[position]
        binding.root.money_tv.text = bean.price

        if (selectPosition == position) {
            binding.root.isSelected = true
            binding.root.money_tv.isSelected = true
            binding.root.currency_tv.isSelected = true
        } else {
            binding.root.isSelected = false
            binding.root.money_tv.isSelected = false
            binding.root.currency_tv.isSelected = false
        }
        binding.root.setOnClickListener(object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                selectPosition = position
                callback.callBack(position)
                notifyDataSetChanged()
            }
        })
    }

}