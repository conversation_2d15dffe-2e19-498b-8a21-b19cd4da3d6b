package om.rrtx.mobile.paymentmodule.viewModel

import android.app.Application
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.functioncommon.PayMethodLiveData
import om.rrtx.mobile.functioncommon.bean.OrderInfoBean
import om.rrtx.mobile.functioncommon.bean.PaymentBean
import om.rrtx.mobile.functioncommon.model.CommonModel
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.bean.MarketingBean
import om.rrtx.mobile.rrtxcommon1.bean.RequestUseMarketingBean
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver
import om.rrtx.mobile.rrtxcommon1.net.BaseObserverNoError
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController

class ScanCodePayVM(application: Application) : AndroidViewModel(application) {

    private val mCommonModel = CommonModel()

    val marketingBeanLD = MutableLiveData<MarketingBean>()
    val useMarketingBeanLD = MutableLiveData<MarketingBean>()
    val orderInfoBeanLD = MutableLiveData<OrderInfoBean>()

    fun requestOrderInfo(orderSource: String, payToken: String) {
        val currentActivity = ActivityController.getInstance().currentActivity()
        mCommonModel.requestOrderInfo("",
            orderSource,
            payToken,
            object : BaseObserverNoError<OrderInfoBean>(currentActivity) {
                override fun requestSuccess(bean: OrderInfoBean) {
                    orderInfoBeanLD.value = bean
                    //queryMarketingInfo(payToken, bean.trxOrderNo, "30")
                }
            })
    }

    fun queryMarketingInfo(payToken: String, trxOrderNo: String, trxTransType: String) {
        val activity = ActivityController.getInstance().currentActivity()
        // 营销面板信息查询
        mCommonModel.queryMarketingInfo(payToken,
            trxOrderNo, trxTransType,
            object : BaseObserverNoError<MarketingBean>(activity) {
                override fun requestSuccess(bean: MarketingBean) {
                    marketingBeanLD.value = bean
                }
            })
    }

    fun requestUseMarketing(bean: RequestUseMarketingBean) {
        val currentActivity = ActivityController.getInstance().currentActivity()
        mCommonModel.requestUseMarketing(bean,
            object : BaseObserverNoError<MarketingBean>(currentActivity) {
                override fun requestSuccess(bean: MarketingBean) {
//                    ToastUtil.show(context, "成功")
                    useMarketingBeanLD.value = bean
                }
            })
    }

    fun requestCreateQrCodeOrder(
        paymentProduct: String?,
        recMerNo: String?,
        recMerName: String?,
        checkstandNo: String?,
        amt: String?,
        qrCode: String?,
        currency: String?
    ) {
        val currentActivity = ActivityController.getInstance().currentActivity()

        mCommonModel.requestCreateQrCodeOrder("",
            paymentProduct,
            recMerNo,
            recMerName,
            checkstandNo,
            amt,
            qrCode,
            currency,
            object : BaseObserverNoError<OrderInfoBean>(currentActivity) {
                override fun requestSuccess(bean: OrderInfoBean) {
                    orderInfoBeanLD.value = bean
                    queryMarketingInfo("", bean.trxOrderNo, "30")
                }

            })

    }

    /**创建汇款吗付款订单**/
    fun checkMerCodePayOrder(merchantCode: String, amt: String, currency: String) {
        val currentActivity = ActivityController.getInstance().currentActivity()

        CommonModel().requestMerCodePayOrder(merchantCode,
            amt,
            currency,
            object : BaseObserverNoError<OrderInfoBean>(currentActivity) {
                override fun requestSuccess(bean: OrderInfoBean) {
                    orderInfoBeanLD.value = bean
                    queryMarketingInfo("", bean.trxOrderNo, "30")
                }
            })
    }

    /**创建电费订单**/
    fun checkZesaPayOrder(meterNo: String, meterName: String, trxAmt: String, currency: String) {
        val currentActivity = ActivityController.getInstance().currentActivity()

        CommonModel().requestZesaOrder(meterNo,
            meterName,
            trxAmt,
            currency,
            object : BaseObserverNoError<OrderInfoBean>(currentActivity) {
                override fun requestSuccess(bean: OrderInfoBean) {
                    orderInfoBeanLD.value = bean
                    queryMarketingInfo("", bean.trxOrderNo, "31")
                }
            })
    }

    /**创建话费订单**/
    fun checkBuyAirtimeOrBundle(
        billSubType: String,
        rcvMobile: String,
        buyAmount: String,
        currency: String,
        planCode: String,
        bundleId: String
    ) {
        val currentActivity = ActivityController.getInstance().currentActivity()
        CommonModel().requestBuyOrder(billSubType, rcvMobile, buyAmount,
            currency, planCode, bundleId,
            object : BaseObserverNoError<OrderInfoBean>(currentActivity) {
                override fun requestSuccess(bean: OrderInfoBean) {
                    orderInfoBeanLD.value = bean
                    queryMarketingInfo("", bean.trxOrderNo, "32")
                }
            })
    }

}