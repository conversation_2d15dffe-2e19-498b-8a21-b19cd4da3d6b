package om.rrtx.mobile.paymentmodule.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.bean.ByMobileBean;
import om.rrtx.mobile.rrtxcommon1.base.BaseHolder;
import om.rrtx.mobile.rrtxcommon1.image.ImageLoaderManager;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.widget.SWImageView;

/**
 * <AUTHOR>
 * 选择联系人适配器
 */
public class SelectConRVAdapter extends RecyclerView.Adapter<BaseHolder> {
    private Context mContext;
    private List<ByMobileBean> mBeanLists;
    private RVAdapterItemClickListener<ByMobileBean> mListener;

    public SelectConRVAdapter(Context context) {
        mContext = context;
        mBeanLists = new ArrayList<>();
    }

    @NonNull
    @Override
    public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.payment_item_select, parent, false);
        return new BaseHolder(rootView);
    }

    @Override
    public void onBindViewHolder(@NonNull BaseHolder holder, int position) {
        ByMobileBean byMobileBean = mBeanLists.get(position);

        ImageView icon = holder.getView(R.id.selectIv);

        if (byMobileBean.isSelect()) {
            icon.setImageResource(R.drawable.payment_ic_contacts_selected);
        } else {
            icon.setImageResource(R.drawable.payment_ic_contacts_choose);
        }

        SWImageView swipeLayout = holder.getView(R.id.heardIv);
        ImageLoaderManager.getInstance().disPlayImage(mContext, byMobileBean.getUserAvatar(), R.drawable.payment_ic_head_contact_default, swipeLayout);

        TextView userName = holder.getView(R.id.nameTv);
        userName.setText(byMobileBean.getRealName());

        TextView phoneTv = holder.getView(R.id.phoneTv);
        phoneTv.setText(StringUtils.stringMask(byMobileBean.getMobileAreaCode(), byMobileBean.getMobile()));

        holder.itemView.setOnClickListener((view) -> {
            if (mListener != null) {
                mListener.itemClickListener(byMobileBean, position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return mBeanLists != null ? mBeanLists.size() : 0;
    }

    public RVAdapterItemClickListener<ByMobileBean> getListener() {
        return mListener;
    }

    public void setListener(RVAdapterItemClickListener<ByMobileBean> listener) {
        mListener = listener;
    }

    public void setData(List<ByMobileBean> list) {
        mBeanLists = list;
        notifyDataSetChanged();
    }
}
