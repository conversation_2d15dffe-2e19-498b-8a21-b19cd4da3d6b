package om.rrtx.mobile.paymentmodule.fragment;

import android.view.View;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnLoadMoreListener;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.paymentmodule.activity.SpiltBillActivity;
import om.rrtx.mobile.paymentmodule.adapter.ReceiveRVAdapter;
import om.rrtx.mobile.paymentmodule.bean.PayListBean;
import om.rrtx.mobile.paymentmodule.presenter.ReceivePresenter;
import om.rrtx.mobile.paymentmodule.view.ReceiveView;
import om.rrtx.mobile.rrtxcommon1.base.MVPWithLazyBaseFragment;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;
import om.rrtx.mobile.rrtxcommon1.widget.EmptyRecycleView;

/**
 * <AUTHOR>
 * 收到的aa
 */
public class ReceiveFragment extends MVPWithLazyBaseFragment<ReceiveView, ReceivePresenter>
        implements ReceiveView {

    @BindView(R2.id.contentRv)
    EmptyRecycleView mContentRv;
    @BindView(R2.id.srl)
    SmartRefreshLayout mSrl;
    @BindView(R2.id.emptyView)
    ConstraintLayout mEmptyView;
    private ReceiveRVAdapter mReceiveRVAdapter;

    private int mPagerSize = 10;
    private int mPagerNum = 1;

    @Override
    protected int createViewLayoutId() {
        return R.layout.payment_fragment_receive;
    }

    @Override
    public ReceivePresenter createPresenter() {
        return new ReceivePresenter(mContext);
    }

    @Override
    protected void initView(View rootView) {

    }

    @Override
    public void initDate() {
        super.initDate();

        mContentRv.setLayoutManager(new LinearLayoutManager(mContext));
        mReceiveRVAdapter = new ReceiveRVAdapter(mContext);
        mContentRv.setAdapter(mReceiveRVAdapter);
    }

    @Override
    public void lazyDate() {
        super.lazyDate();
        mPagerNum = 1;
        filterShow();
    }

    @Override
    public void initListener() {
        super.initListener();
        mSrl.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                mPagerNum = 1;
                filterShow();
            }
        });

        mSrl.setOnLoadMoreListener(new OnLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                mPagerNum++;
                filterMoreShow();
            }
        });

        mReceiveRVAdapter.setClickListener(new RVAdapterItemClickListener<PayListBean.RecordsBean>() {
            @Override
            public void itemClickListener(PayListBean.RecordsBean recordsBean, int position) {
                SpiltBillActivity.jumpSpiltBill(mContext, recordsBean.getDetailOrderNo(), PaymentConstants.Transmit.RECEIVE);
            }
        });
    }

    public void filterShow() {
        mPresenter.requestPayList(mPagerNum, mPagerSize);
    }

    public void filterMoreShow() {
        mPresenter.requestMorePayList(mPagerNum, mPagerSize);
    }


    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
        mContentRv.setEmptyView(mEmptyView);
    }

    @Override
    public void payListSuccess(PayListBean sResData) {
        mSrl.finishLoadMore();
        mSrl.finishRefresh();
        if (sResData == null) {
            return;
        }
        List<PayListBean.RecordsBean> records = sResData.getRecords();
        if (records == null) {
            records = new ArrayList<>();
        }

        mReceiveRVAdapter.setData(records);
        mContentRv.setEmptyView(mEmptyView);
    }

    @Override
    public void payListMoreSuccess(PayListBean sResData) {
        mSrl.finishLoadMore();

        if (sResData == null) {
            return;
        }
        List<PayListBean.RecordsBean> recordsBeans = sResData.getRecords();
        if (recordsBeans == null || recordsBeans.size() == 0) {
            return;
        }

        mReceiveRVAdapter.addData(recordsBeans);
        mContentRv.setEmptyView(mEmptyView);
    }
}
