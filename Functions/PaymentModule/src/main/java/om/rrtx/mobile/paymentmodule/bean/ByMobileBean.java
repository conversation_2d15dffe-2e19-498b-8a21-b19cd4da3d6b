package om.rrtx.mobile.paymentmodule.bean;

import androidx.annotation.Nullable;

/**
 * <AUTHOR>
 * 根据电话号查找用户
 */
public class ByMobileBean implements Comparable<ByMobileBean> {

    /**
     * realName : fengxz
     * mobile : +6017731140266
     * nikeName : +6017731140266
     * userAvatar :
     * isContact : 0
     */

    private String realName;
    private String mobile;
    private String nikeName;
    private String userAvatar;
    private String isContact;
    private boolean isSelect;
    private String mobileAreaCode;
    /**
     * 这里是单独加的金额
     * 主要是为了下一个页面的传值
     */
    private String amount;

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getNikeName() {
        return nikeName;
    }

    public void setNikeName(String nikeName) {
        this.nikeName = nikeName;
    }

    public String getUserAvatar() {
        return userAvatar;
    }

    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }

    public String getIsContact() {
        return isContact;
    }

    public void setIsContact(String isContact) {
        this.isContact = isContact;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public boolean isSelect() {
        return isSelect;
    }

    public void setSelect(boolean select) {
        isSelect = select;
    }

    public String getMobileAreaCode() {
        return mobileAreaCode;
    }

    public void setMobileAreaCode(String mobileAreaCode) {
        this.mobileAreaCode = mobileAreaCode;
    }

    @Override
    public int compareTo(ByMobileBean byMobileBean) {
        // 获取ascii值
        int lhs_ascii = getRealName().toUpperCase().charAt(0);
        int rhs_ascii = byMobileBean.getRealName().toUpperCase().charAt(0);
        // 判断若不是字母，则排在字母之后
        if ((lhs_ascii < 65 || lhs_ascii > 90) && !(rhs_ascii < 65 || rhs_ascii > 90)) {
            //lhs_ascii包含字母但是没有判断rhs_ascii这个包含字母的情况
            return 1;
        } else if (rhs_ascii < 65 || rhs_ascii > 90) {
            return -1;
        } else {
            return getRealName().toUpperCase().compareTo(byMobileBean.getRealName().toUpperCase());
        }
    }
}
