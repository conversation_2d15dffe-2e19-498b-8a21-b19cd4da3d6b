package om.rrtx.mobile.paymentmodule.fragment;

import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;

import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.activity.HorizontalHintActivity;
import om.rrtx.mobile.paymentmodule.activity.PaymentSuccessActivity;
import om.rrtx.mobile.paymentmodule.databinding.PaymentFragmentMakePaymentBinding;
import om.rrtx.mobile.paymentmodule.utils.QRCodeEncoder;
import om.rrtx.mobile.paymentmodule.viewModel.MakePaymentWM;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseFragment;
import om.rrtx.mobile.rrtxcommon1.bean.QueryOrderBean;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;


/**
 * <AUTHOR>
 * 付款二维码
 */

public class MakePaymentFragment extends BaseFragment<MakePaymentWM, PaymentFragmentMakePaymentBinding> {

    private String mQrCode;
    private String mSource = "";

    private Handler mHandler = new Handler(Looper.getMainLooper());
    private Runnable mCodeRefresh = new Runnable() {
        @Override
        public void run() {
            viewModel.requestMakePaymentCode();
        }
    };

    private Runnable mLoopRun = new Runnable() {
        @Override
        public void run() {
            viewModel.requestPayCheck(mQrCode);
        }
    };

    @Override
    protected void initView() {

    }

    @Override
    public void initDate() {
        super.initDate();
        mSource = getActivity().getIntent().getStringExtra(BaseConstants.HomeJumpType.JUMP_HOME);
        viewModel.requestMakePaymentCode();
    }

    @Override
    public void initListener() {
        super.initListener();

        dataBinding.barCodeIv.setOnClickListener(makeCustom);
        dataBinding.codeIv.setOnClickListener(makeCustom);
        dataBinding.refreshTv.setOnClickListener(makeCustom);
        dataBinding.refreshIv.setOnClickListener(makeCustom);
        initVMListener();
    }

    @Override
    protected int createViewLayoutId() {
        return R.layout.payment_fragment_make_payment;
    }

    private CustomClickListener makeCustom = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.barCodeIv) {
                HorizontalHintActivity.jumpHorizontalHint(mContext, mQrCode);
            } else if (view.getId() == R.id.leftBg) {
                //finish();
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .withInt(CommonConstants.Transmit.POSITION, 0)
                        .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .navigation();
            } else if (view.getId() == R.id.refreshTv ||
                    view.getId() == R.id.refreshIv) {
                viewModel.requestMakePaymentCode();
            }
        }
    };

    private void initVMListener() {
        viewModel.getMakePaySuccess().observe(getActivity(), makePaymentCodeBean -> {
            //倒计时60秒轮训
            if (false) {
                mHandler.removeCallbacks(mCodeRefresh);
                mHandler.postDelayed(mCodeRefresh, 60 * 1000);
            }

            mQrCode = makePaymentCodeBean.getQrCode();

            //轮训订单结果
            if (mLoopRun != null) {
                mHandler.removeCallbacks(mLoopRun);
                mHandler.postDelayed(mLoopRun, 3000);
            }

            Bitmap barCodeBit = QRCodeEncoder.syncEncodeBarcode(mQrCode,
                    (int) getResources().getDimension(R.dimen.payment_bar_code_width),
                    (int) getResources().getDimension(R.dimen.payment_bar_code_height),
                    0);

            dataBinding.barCodeIv.setImageBitmap(barCodeBit);

            //条形码
            dataBinding.barCodeTv.setText(StringUtils.barCodeMask(mQrCode));

            Bitmap bitmap = QRCodeEncoder.syncEncodeQRCode(mQrCode,
                    (int) getResources().getDimension(R.dimen.payment_qr_code_size),
                    getResources().getColor(R.color.color_131313));
            dataBinding.codeIv.setImageBitmap(bitmap);

            Intent intent = new Intent();
            intent.putExtra(PaymentConstants.Transmit.QRCODE, mQrCode);
            intent.setAction(getActivity().getPackageName() + PaymentConstants.CodeRefreshAction);
            LocalBroadcastManager.getInstance(mContext).sendBroadcast(intent);
        });

        viewModel.getCodeOrderSuccess().observe(getActivity(), payCheckBean -> {
            String scanStatus = payCheckBean.getScanStatus();
            if (TextUtils.equals(scanStatus, PaymentConstants.ScanFlag.NOSCAN)) {
                //未扫
                //继续查询轮训结果
                if (mLoopRun != null) {
                    mHandler.removeCallbacks(mLoopRun);
                    mHandler.postDelayed(mLoopRun, 3000);
                }
            } else {
                //已经扫描
                String isPaid = payCheckBean.getIsPaid();
                if (TextUtils.equals(isPaid, PaymentConstants.ScanFlag.NOPAY)) {
                    //未支付
//                ScanCodePayActivity.jump(mContext, new Gson().toJson(payCheckBean), mQrCode, BaseConstants.JumpFlag.BARCODEFLAG);
//                BeSweptPaymentActivity.jumpBeSweptPay(mContext, new Gson().toJson(payCheckBean), mQrCode, BaseConstants.JumpFlag.BARCODEFLAG);
//                ToastUtil.show(mContext,"跳转");
                    QueryOrderBean queryOrderBean = new QueryOrderBean("", "", "", "",
                            payCheckBean.getMerNo(), payCheckBean.getCashierName(),
                            payCheckBean.getCheckstandNo(), payCheckBean.getAmt(),
                            mQrCode, payCheckBean.getCurrency(), CommonConstants.PaymentProduct.BARCODE);

                    ARouter.getInstance().build(ARouterPath.Cashier.CashierPayActivity)
                            .withString(BaseConstants.Transmit.JSON, new Gson().toJson(queryOrderBean))
                            .navigation();
                    getActivity().finish();
                } else {
                    //已经支付
                    PaymentSuccessActivity.jumpPaymentSuccess(mContext, new Gson().toJson(payCheckBean));
                }
            }
        });
        viewModel.getLoopFailResult().observe(getActivity(), s -> {
            //轮训订单结果
            if (mLoopRun != null) {
                mHandler.removeCallbacks(mLoopRun);
                mHandler.postDelayed(mLoopRun, 3000);
            }
        });
        viewModel.getFailResult().observe(getActivity(), sResMsg -> {
            ToastUtil.show(mContext, sResMsg);
        });

    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        if (mCodeRefresh != null) {
            mHandler.removeCallbacks(mCodeRefresh);
        }

        if (mLoopRun != null) {
            mHandler.removeCallbacks(mLoopRun);
        }
    }
}
