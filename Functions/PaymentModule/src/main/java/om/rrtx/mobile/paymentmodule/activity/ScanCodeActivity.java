package om.rrtx.mobile.paymentmodule.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;

import androidx.lifecycle.Observer;

import com.alibaba.android.arouter.core.LogisticsCenter;
import com.alibaba.android.arouter.facade.Postcard;
import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import butterknife.ButterKnife;
import cn.bingoogolapple.qrcode.core.QRCodeView;
import cn.bingoogolapple.qrcode.zbar.ZBarView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.bean.OrderInfoBean;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.paymentmodule.databinding.PaymentActivityScanCodeBinding;
import om.rrtx.mobile.paymentmodule.utils.LogUtil;
import om.rrtx.mobile.paymentmodule.viewModel.ScanCodePayVM;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.base.BaseVVMActivity;
import om.rrtx.mobile.rrtxcommon1.bean.QueryOrderBean;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 扫码页面
 */
@Route(path = ARouterPath.Payment.ScanCoceActiity)
public class ScanCodeActivity extends BaseVVMActivity<ScanCodePayVM, PaymentActivityScanCodeBinding> implements QRCodeView.Delegate {

    private boolean isFlash;
    private QueryOrderBean outJumpBean;

    @Override
    protected int createContentView() {
        return R.layout.payment_activity_scan_code;
    }

    @Override
    public void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .init();
        dataBinding.backIv.setImageResource(R.drawable.payment_ic_back_w);
    }

    @Override
    protected void initVMListener() {
        super.initVMListener();
        // 订单信息回调
        viewModel.getOrderInfoBeanLD().observe(this, new Observer<OrderInfoBean>() {
            @Override
            public void onChanged(OrderInfoBean orderInfoBean) {
                Postcard postcard = ARouter.getInstance().build(ARouterPath.Cashier.CashierPayActivity)
                        .withString(BaseConstants.Transmit.JSON, new Gson().toJson(outJumpBean))
                        .withString(BaseConstants.Transmit.ORDERJSON, new Gson().toJson(orderInfoBean))
                        .withString(BaseConstants.Transmit.FROMTYPE, "scan");
                LogisticsCenter.completion(postcard);
                Intent intent = new Intent(ScanCodeActivity.this, postcard.getDestination());
                intent.putExtras(postcard.getExtras());
                startActivityForResult(intent, CommonConstants.ResultCode.Request);
            }
        });
    }

    @Override
    public void initListener() {
        super.initListener();
        dataBinding.zXingView.setDelegate(this);
        dataBinding.leftBg.setOnClickListener(scanCustom);
        dataBinding.flashIv.setOnClickListener(scanCustom);
    }

    private CustomClickListener scanCustom = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.leftBg) {
                finish();
            } else if (view.getId() == R.id.flashIv) {
                isFlash = !isFlash;
                if (isFlash) {
                    dataBinding.zXingView.openFlashlight();
                    dataBinding.flashIv.setImageResource(R.drawable.payment_ic_flashlight_on);
                } else {
                    dataBinding.zXingView.closeFlashlight();
                    dataBinding.flashIv.setImageResource(R.drawable.payment_ic_flashlight_off);
                }
            }
        }
    };

    @Override
    protected void onStart() {
        super.onStart();
        dataBinding.zXingView.startCamera(); // 打开后置摄像头开始预览，但是并未开始识别
        dataBinding.zXingView.startSpotAndShowRect(); // 显示扫描框，并开始识别
    }

    @Override
    public void onScanQRCodeSuccess(String result) {
        dataBinding.zXingView.stopSpot();

        if (TextUtils.isEmpty(result)) {
            ToastUtil.show(this, getString(R.string.scan_tip_scan_error_tip));
            finish();
        } else {
            /*Intent intent = new Intent();
            intent.putExtra(BaseConstants.Transmit.QRCODE, result);
            setResult(RESULT_OK, intent);
            finish();*/
            disposeCode(result);
        }
    }

    private void disposeCode(String mQrCode) {
        Log.e("扫码归来", mQrCode);
//        mQrCode = mQrCode.replace("\n", "");
//        mQrCode = mQrCode.replace("\\", "");
        if (mQrCode.contains("http") || mQrCode.contains("https")) {
            //扫码前缀
            //返回的格式
            //这里解析的第一个参数是网址,第二个参数是参数集合
            String[] paramsSp = mQrCode.split("\\?");
            if (paramsSp.length > 1) {
                //这里说明后面有参数
                String[] split = paramsSp[1].split("&");
                if (split.length >= 1) {
                    //这里设置相应的参数
                    // 这个只有短信的时候有
                    String[] source = split[0].split("=");
                    // 这个只有支付的时候有
                    String[] payToken = split[1].split("=");

                    if (source.length == 1 || payToken.length == 1) {
                        Log.e("TAG", "onActivityResult: 解析参数有误");
                        return;
                    }

                    Log.e("TAG", "onActivityResult: " + source[1] + "---" + payToken[1]);
                    outJumpBean = new QueryOrderBean(payToken[1], "", source[1], "");
                    viewModel.requestOrderInfo(outJumpBean.getOrderSource(), outJumpBean.getPayToken());
                }
            } else {
                //Log.e("TAG", "onActivityResult: 扫码参数异常");
                ToastUtil.show(this, getString(R.string.scan_tip_scan_error_tip));
            }
        } else {
            Intent intent = new Intent();
            intent.putExtra(BaseConstants.Transmit.QRCODE, mQrCode);
            setResult(RESULT_OK, intent);
            finish();
        }
    }

    @Override
    public void onCameraAmbientBrightnessChanged(boolean isDark) {
        // 这里是通过修改提示文案来展示环境是否过暗的状态，接入方也可以根据 isDark 的值来实现其他交互效果
    }

    @Override
    public void onScanQRCodeOpenCameraError() {
        LogUtil.e("打开相机出错===>", "onScanQRCodeOpenCameraError: ");
    }

    @Override
    protected void onStop() {
        // 关闭摄像头预览，并且隐藏扫描框
        dataBinding.zXingView.stopCamera();
        super.onStop();
    }

    @Override
    protected void onDestroy() {
        // 销毁二维码扫描控件
        dataBinding.zXingView.onDestroy();
        super.onDestroy();
    }
}
