package om.rrtx.mobile.paymentmodule.presenter;

import android.content.Context;
import android.text.TextUtils;

import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.paymentmodule.bean.PubBean;
import om.rrtx.mobile.paymentmodule.bean.QrCodeBean;
import om.rrtx.mobile.paymentmodule.model.PaymentModel;
import om.rrtx.mobile.paymentmodule.view.ReceivePaymentView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;

/**
 * <AUTHOR>
 * 收付款页面Presenter层
 */
public class ReceivePaymentPresenter extends BasePresenter<ReceivePaymentView> {

    private final PaymentModel mPaymentModel;
    private Context mContext;

    public ReceivePaymentPresenter(Context context) {
        mPaymentModel = new PaymentModel();
        mContext = context;
    }

    /**
     * 请求二维码接口
     *
     * @param amt    金额
     * @param remark 留言
     */
    public void requestQrCode(String amt, String remark, String currency) {
        if (!StringUtils.isValidString(currency)) currency = CurrencyUtils.setCurrency(mContext,"ZWG");
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestQrCode(userId, amt, remark, currency, new BaseObserver<QrCodeBean>(mContext) {
                @Override
                public void requestSuccess(QrCodeBean qrCodeBean) {
                    if (getView() != null) {
                        getView().qrCodeSuccess(qrCodeBean);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        } else {
            String finalCurrency = currency;
            mPaymentModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mPaymentModel.requestQrCode(userId, amt, remark, finalCurrency, new BaseObserver<QrCodeBean>(mContext) {
                        @Override
                        public void requestSuccess(QrCodeBean qrCodeBean) {
                            if (getView() != null) {
                                getView().qrCodeSuccess(qrCodeBean);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }
}
