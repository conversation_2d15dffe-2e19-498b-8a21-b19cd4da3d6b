package om.rrtx.mobile.paymentmodule.view;

import om.rrtx.mobile.paymentmodule.bean.ContactBean;
import om.rrtx.mobile.paymentmodule.bean.QrCodeBean;

/**
 * <AUTHOR>
 * 选择联系人View
 */
public interface SelectContactsView {

    /**
     * 联系人列表
     *
     * @param sResData 实体类
     */
    void contactListSuccess(ContactBean sResData);

    /**
     * 请求失败
     *
     * @param sResMsg 失败信息
     */
    void requestFail(String sResMsg);
}
