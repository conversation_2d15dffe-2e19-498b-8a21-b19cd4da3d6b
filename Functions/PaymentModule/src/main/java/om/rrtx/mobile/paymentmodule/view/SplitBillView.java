package om.rrtx.mobile.paymentmodule.view;

import om.rrtx.mobile.paymentmodule.bean.AAPayDetailBean;
import om.rrtx.mobile.paymentmodule.bean.AARecDetailBean;

/**
 * <AUTHOR>
 * aa订单页面的V
 */
public interface SplitBillView {


    /**
     * 请求失败
     *
     * @param sResMsg 失败信息
     */
    void requestFail(String sResMsg);

    /**
     * Pending发起订单详情请求成功
     *
     * @param aaRecDetailBean 实体类
     */
    void recDetailSuccess(AARecDetailBean aaRecDetailBean);

    /**
     * 关闭订单成功
     */
    void cancelOrderSuccess();

    /**
     * Pending付款订单详情
     *
     * @param aaRecDetailBean 实体类
     */
    void payDetailSuccess(AAPayDetailBean aaRecDetailBean);

    /**
     * 支付AA订单成功
     */
    void payOrderSuccess();

}
