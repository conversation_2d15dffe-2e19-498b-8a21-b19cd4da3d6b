package om.rrtx.mobile.paymentmodule.activity;

import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.paymentmodule.bean.MakePaymentCodeBean;
import om.rrtx.mobile.paymentmodule.presenter.MakePaymentPresenter;
import om.rrtx.mobile.paymentmodule.utils.QRCodeEncoder;
import om.rrtx.mobile.paymentmodule.view.MakePayementView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.bean.CodeOrderBean;
import om.rrtx.mobile.rrtxcommon1.bean.QueryOrderBean;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;


/**
 * <AUTHOR>
 * 付款二维码
 */

public class MakePaymentActivity extends BaseSuperActivity<MakePayementView, MakePaymentPresenter>
        implements MakePayementView {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.barCodeIv)
    ImageView mBarCodeIv;
    @BindView(R2.id.codeIv)
    ImageView mCodeIv;
    @BindView(R2.id.barCodeTv)
    TextView mBarCodeTv;
    @BindView(R2.id.refreshTv)
    View mRefreshTv;
    @BindView(R2.id.refreshIv)
    View mRefreshIv;
    @BindView(R2.id.statusView)
    View mStatusView;
    private String mQrCode;
    private String mSource="";


    private Handler mHandler = new Handler(Looper.getMainLooper());
    private Runnable mCodeRefresh = new Runnable() {
        @Override
        public void run() {
            mPresenter.requestMakePaymentCode();
        }
    };

    private Runnable mLoopRun = new Runnable() {
        @Override
        public void run() {
            mPresenter.requestPayCheck(mQrCode);
        }
    };

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    protected int createContentView() {
        return R.layout.payment_make_payment;
    }

    @Override
    protected MakePaymentPresenter createPresenter() {
        return new MakePaymentPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar
                .with(this)
                .statusBarView(R.id.statusView)
                .init();

        mBackIv.setImageResource(R.drawable.payment_ic_back_w);

        mStatusView.setBackgroundColor(getResources().getColor(R.color.common_ye_F3881E));
        mTitleTv.setBackgroundColor(getResources().getColor(R.color.common_ye_F3881E));
        mTitleTv.setText(R.string.make_Payment);
    }

    @Override
    public void initDate() {
        super.initDate();
        mSource = getIntent().getStringExtra("BaseConstants.HomeJumpType.JUMP_HOME");
        mPresenter.requestMakePaymentCode();
    }

    @Override
    public void initListener() {
        super.initListener();

        mBarCodeIv.setOnClickListener(makeCustom);
        mCodeIv.setOnClickListener(makeCustom);
        mLeftBg.setOnClickListener(makeCustom);
        mRefreshTv.setOnClickListener(makeCustom);
        mRefreshIv.setOnClickListener(makeCustom);
    }

    private CustomClickListener makeCustom = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.barCodeIv) {
                HorizontalHintActivity.jumpHorizontalHint(mContext, mQrCode);
            } else if (view.getId() == R.id.leftBg) {
                //finish();
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .withInt(CommonConstants.Transmit.POSITION, 0)
                        .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .navigation();
            } else if (view.getId() == R.id.refreshTv ||
                    view.getId() == R.id.refreshIv) {
                mPresenter.requestMakePaymentCode();
            }
        }
    };

    @Override
    public void makePaymentCodeSuccess(MakePaymentCodeBean makePaymentCodeBean) {
        //倒计时60秒轮训
        if (false) {
            mHandler.removeCallbacks(mCodeRefresh);
            mHandler.postDelayed(mCodeRefresh, 60 * 1000);
        }

        mQrCode = makePaymentCodeBean.getQrCode();

        //轮训订单结果
        if (mLoopRun != null) {
            mHandler.removeCallbacks(mLoopRun);
            mHandler.postDelayed(mLoopRun, 3000);
        }

        Bitmap barCodeBit = QRCodeEncoder.syncEncodeBarcode(mQrCode,
                (int) getResources().getDimension(R.dimen.payment_bar_code_width),
                (int) getResources().getDimension(R.dimen.payment_bar_code_height),
                0);

        mBarCodeIv.setImageBitmap(barCodeBit);

        //条形码
        mBarCodeTv.setText(StringUtils.barCodeMask(mQrCode));

        Bitmap bitmap = QRCodeEncoder.syncEncodeQRCode(mQrCode,
                (int) getResources().getDimension(R.dimen.payment_qr_code_size),
                getResources().getColor(R.color.color_131313));
        mCodeIv.setImageBitmap(bitmap);

        Intent intent = new Intent();
        intent.putExtra(PaymentConstants.Transmit.QRCODE, mQrCode);
        intent.setAction(getPackageName() + PaymentConstants.CodeRefreshAction);
        LocalBroadcastManager.getInstance(mContext).sendBroadcast(intent);
    }

    @Override
    public void payCheckSuccess(CodeOrderBean payCheckBean) {

        String scanStatus = payCheckBean.getScanStatus();

        if (TextUtils.equals(scanStatus, PaymentConstants.ScanFlag.NOSCAN)) {
            //未扫
            //继续查询轮训结果
            if (mLoopRun != null) {
                mHandler.removeCallbacks(mLoopRun);
                mHandler.postDelayed(mLoopRun, 3000);
            }
        } else {
            //已经扫描
            String isPaid = payCheckBean.getIsPaid();
            if (TextUtils.equals(isPaid, PaymentConstants.ScanFlag.NOPAY)) {
                //未支付
//                ScanCodePayActivity.jump(mContext, new Gson().toJson(payCheckBean), mQrCode, BaseConstants.JumpFlag.BARCODEFLAG);
//                BeSweptPaymentActivity.jumpBeSweptPay(mContext, new Gson().toJson(payCheckBean), mQrCode, BaseConstants.JumpFlag.BARCODEFLAG);
//                ToastUtil.show(mContext,"跳转");
                QueryOrderBean queryOrderBean = new QueryOrderBean("", "", "", "",
                        payCheckBean.getMerNo(), payCheckBean.getCashierName(),
                        payCheckBean.getCheckstandNo(), payCheckBean.getAmt(),
                        mQrCode, payCheckBean.getCurrency(), CommonConstants.PaymentProduct.BARCODE);

                ARouter.getInstance().build(ARouterPath.Cashier.CashierPayActivity)
                        .withString(BaseConstants.Transmit.JSON, new Gson().toJson(queryOrderBean))
                        .navigation();
                finish();

            } else {
                //已经支付
                PaymentSuccessActivity.jumpPaymentSuccess(mContext, new Gson().toJson(payCheckBean));
            }
        }
    }

    @Override
    public void requestLoopFail(String sResMsg) {
        //轮训订单结果
        if (mLoopRun != null) {
            mHandler.removeCallbacks(mLoopRun);
            mHandler.postDelayed(mLoopRun, 3000);
        }
    }

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        if (mCodeRefresh != null) {
            mHandler.removeCallbacks(mCodeRefresh);
        }

        if (mLoopRun != null) {
            mHandler.removeCallbacks(mLoopRun);
        }
    }
}
