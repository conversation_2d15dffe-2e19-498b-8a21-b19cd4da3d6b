package om.rrtx.mobile.paymentmodule.fragment;

import android.view.View;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.scwang.smartrefresh.layout.SmartRefreshLayout;
import com.scwang.smartrefresh.layout.api.RefreshLayout;
import com.scwang.smartrefresh.layout.listener.OnLoadMoreListener;
import com.scwang.smartrefresh.layout.listener.OnRefreshListener;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.paymentmodule.activity.SpiltBillActivity;
import om.rrtx.mobile.paymentmodule.adapter.LssuedRVAdapter;
import om.rrtx.mobile.paymentmodule.bean.RecListBean;
import om.rrtx.mobile.paymentmodule.presenter.LssuedPresenter;
import om.rrtx.mobile.paymentmodule.view.LssuedView;
import om.rrtx.mobile.rrtxcommon1.base.MVPWithLazyBaseFragment;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;
import om.rrtx.mobile.rrtxcommon1.widget.EmptyRecycleView;

/**
 * <AUTHOR>
 * 发布的aa
 */
public class LssuedFragment extends MVPWithLazyBaseFragment<LssuedView, LssuedPresenter>
        implements LssuedView, RVAdapterItemClickListener<RecListBean.RecordsBean> {

    @BindView(R2.id.contentRv)
    EmptyRecycleView mContentRv;
    @BindView(R2.id.srl)
    SmartRefreshLayout mSrl;
    @BindView(R2.id.emptyView)
    ConstraintLayout mEmptyView;

    private int mPagerSize = 10;
    private int mPagerNum = 1;
    private LssuedRVAdapter mLssuedRVAdapter;

    @Override
    protected int createViewLayoutId() {
        return R.layout.payment_fragment_lssued;
    }

    @Override
    public LssuedPresenter createPresenter() {
        return new LssuedPresenter(mContext);
    }

    @Override
    protected void initView(View rootView) {

    }

    @Override
    public void initDate() {
        super.initDate();

        mContentRv.setLayoutManager(new LinearLayoutManager(mContext));
        mLssuedRVAdapter = new LssuedRVAdapter(mContext);
        mLssuedRVAdapter.setClickListener(this);
        mContentRv.setAdapter(mLssuedRVAdapter);
    }

    @Override
    public void lazyDate() {
        super.lazyDate();
        mPagerNum = 1;
        filterShow();
    }

    @Override
    public void initListener() {
        super.initListener();
        mSrl.setOnRefreshListener(new OnRefreshListener() {
            @Override
            public void onRefresh(@NonNull RefreshLayout refreshLayout) {
                mPagerNum = 1;
                filterShow();
            }
        });

        mSrl.setOnLoadMoreListener(new OnLoadMoreListener() {
            @Override
            public void onLoadMore(@NonNull RefreshLayout refreshLayout) {
                mPagerNum++;
                filterMoreShow();
            }
        });
    }


    public void filterShow() {
        mPresenter.requestRecList(mPagerNum, mPagerSize);
    }

    public void filterMoreShow() {
        mPresenter.requestMoreRecList(mPagerNum, mPagerSize);
    }


    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
        mContentRv.setEmptyView(mEmptyView);
    }

    @Override
    public void recListSuccess(RecListBean sResData) {
        mSrl.finishLoadMore();
        mSrl.finishRefresh();
        if (sResData == null) {
            return;
        }
        List<RecListBean.RecordsBean> records = sResData.getRecords();
        if (records == null) {
            records = new ArrayList<>();
        }

        mLssuedRVAdapter.setData(records);
        mContentRv.setEmptyView(mEmptyView);
    }

    @Override
    public void recListMoreSuccess(RecListBean sResData) {
        mSrl.finishLoadMore();

        if (sResData == null) {
            return;
        }
        List<RecListBean.RecordsBean> recordsBeans = sResData.getRecords();
        if (recordsBeans == null || recordsBeans.size() == 0) {
            return;
        }

        mLssuedRVAdapter.addData(recordsBeans);
        mContentRv.setEmptyView(mEmptyView);
    }

    @Override
    public void itemClickListener(RecListBean.RecordsBean recordsBean, int position) {
        SpiltBillActivity.jumpSpiltBill(mContext, recordsBean.getOrderNo(), PaymentConstants.Transmit.LSSUED);
    }
}
