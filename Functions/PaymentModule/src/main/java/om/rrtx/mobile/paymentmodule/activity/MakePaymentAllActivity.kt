package om.rrtx.mobile.paymentmodule.activity

import android.content.Intent
import android.view.View
import androidx.fragment.app.Fragment
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.gyf.immersionbar.ImmersionBar
import com.kapp.xmarketing.adapter.BaseVPAdapter
import kotlinx.android.synthetic.main.payment_activity_make_payment.tab
import kotlinx.android.synthetic.main.payment_activity_make_payment.vp
import kotlinx.android.synthetic.main.payment_base_title.backIv
import kotlinx.android.synthetic.main.payment_base_title.leftBg
import kotlinx.android.synthetic.main.payment_base_title.statusView
import kotlinx.android.synthetic.main.payment_base_title.titleTv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.paymentmodule.R
import om.rrtx.mobile.paymentmodule.databinding.PaymentActivityMakePaymentBinding
import om.rrtx.mobile.paymentmodule.fragment.MakePaymentFragment
import om.rrtx.mobile.paymentmodule.fragment.MerchantCodePayFragment
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener

@Route(path = ARouterPath.Payment.MakePaymentAllActivity)
class MakePaymentAllActivity : BaseActivity<PaymentActivityMakePaymentBinding>() {
    override fun createContentView() = R.layout.payment_activity_make_payment
    override fun initWorkspaceAction() {
    }

    override fun initView() {
        ImmersionBar
            .with(this)
            .statusBarView(R.id.statusView)
            .init()

        backIv.setImageResource(R.drawable.payment_ic_back_w)
        statusView.setBackgroundColor(resources.getColor(R.color.common_ye_F3881E))
        titleTv.setBackgroundColor(resources.getColor(R.color.common_ye_F3881E))
        titleTv.setText(R.string.make_Payment)

        vp.adapter = BaseVPAdapter(
            supportFragmentManager,
            arrayListOf<Fragment>(
                MakePaymentFragment(),
                MerchantCodePayFragment()
            )
        )

        tab.setViewPager(
            vp,
            arrayOf(
                getString(R.string.bar_code),
                getString(R.string.merchant_code)
            )
        )
    }

    override fun initListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    leftBg -> {
                        //finish();
                        ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                            .withInt(CommonConstants.Transmit.POSITION, 0)
                            .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                            .navigation()
                    }
                }
            }

        }.apply {
            leftBg.setOnClickListener(this)
        }
    }

}