package om.rrtx.mobile.paymentmodule.view;

import om.rrtx.mobile.paymentmodule.bean.MakePaymentCodeBean;
import om.rrtx.mobile.rrtxcommon1.bean.CodeOrderBean;

/**
 * <AUTHOR>
 * AA转账的p层
 */
public interface MakePayementView {

    /**
     * 请求失败
     *
     * @param sResMsg 失败信息
     */
    void requestFail(String sResMsg);

    /**
     * 获取付款二维码
     *
     * @param makePaymentCodeBean 实体类
     */
    void makePaymentCodeSuccess(MakePaymentCodeBean makePaymentCodeBean);

    /**
     * 二维码是否被扫描
     *
     * @param payCheckBean 扫描实体类
     */
    void payCheckSuccess(CodeOrderBean payCheckBean);

    /**
     * 轮训失败
     * @param sResMsg
     */
    void requestLoopFail(String sResMsg);
}
