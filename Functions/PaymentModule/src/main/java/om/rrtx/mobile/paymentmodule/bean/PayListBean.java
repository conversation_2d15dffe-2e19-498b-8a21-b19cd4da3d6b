package om.rrtx.mobile.paymentmodule.bean;

import java.util.List;

/**
 * <AUTHOR>
 * 收到的aa列表
 */
public class PayListBean {


    /**
     * records : [{"detailOrderNo":"8110011878396133703","recCstName":"maoguang<PERSON><PERSON>","transAmt":"1.00","remark":"","tradeTime":"2020-02-29 00:06:44","orderStatus":"10"}]
     * total : 1
     * size : 10
     * current : 1
     * orders : []
     * searchCount : true
     * pages : 1
     */

    private int total;
    private int size;
    private int current;
    private boolean searchCount;
    private int pages;
    private List<RecordsBean> records;
    private List<?> orders;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getCurrent() {
        return current;
    }

    public void setCurrent(int current) {
        this.current = current;
    }

    public boolean isSearchCount() {
        return searchCount;
    }

    public void setSearchCount(boolean searchCount) {
        this.searchCount = searchCount;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    public List<RecordsBean> getRecords() {
        return records;
    }

    public void setRecords(List<RecordsBean> records) {
        this.records = records;
    }

    public List<?> getOrders() {
        return orders;
    }

    public void setOrders(List<?> orders) {
        this.orders = orders;
    }

    public static class RecordsBean {
        /**
         * detailOrderNo : 8110011878396133703
         * recCstName : maoguangyao
         * transAmt : 1.00
         * remark :
         * tradeTime : 2020-02-29 00:06:44
         * orderStatus : 10
         */

        private String detailOrderNo;
        private String recCstName;
        private String transAmt;
        private String remark;
        private String tradeTime;
        private String orderStatus;
        private String currency;

        public String getDetailOrderNo() {
            return detailOrderNo;
        }

        public void setDetailOrderNo(String detailOrderNo) {
            this.detailOrderNo = detailOrderNo;
        }

        public String getRecCstName() {
            return recCstName;
        }

        public void setRecCstName(String recCstName) {
            this.recCstName = recCstName;
        }

        public String getTransAmt() {
            return transAmt;
        }

        public void setTransAmt(String transAmt) {
            this.transAmt = transAmt;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getTradeTime() {
            return tradeTime;
        }

        public void setTradeTime(String tradeTime) {
            this.tradeTime = tradeTime;
        }

        public String getOrderStatus() {
            return orderStatus;
        }

        public void setOrderStatus(String orderStatus) {
            this.orderStatus = orderStatus;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }
    }
}
