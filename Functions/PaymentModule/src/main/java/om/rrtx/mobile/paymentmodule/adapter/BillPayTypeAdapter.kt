package om.rrtx.mobile.paymentmodule.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.functioncommon.bean.BillCodeBean
import om.rrtx.mobile.paymentmodule.R
import om.rrtx.mobile.paymentmodule.activity.BillCodePayToCityActivity
import om.rrtx.mobile.paymentmodule.activity.BillCodePayToHouseActivity
import om.rrtx.mobile.paymentmodule.activity.BillCodeSearchActivity
import om.rrtx.mobile.paymentmodule.activity.ZesaSearchActivity
import om.rrtx.mobile.paymentmodule.databinding.ItemBillPayTypeBinding
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper

data class BillPayItemBean(val iconId: Int, val title: String, val type: String)
class BillPayTypeAdapter() :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    lateinit var context: Context

    private var data = arrayListOf<BillPayItemBean>()

    init {
        data.apply {
            add(
                BillPayItemBean(
                    R.drawable.ic_zesa,
                    ResourceHelper.getString(R.string.zESA),
                    CommonConstants.BillPayType.ELE_FEES
                )
            )
            add(
                BillPayItemBean(
                    R.drawable.billcode_city,
                    ResourceHelper.getString(R.string.city_of_Harare),
                    CommonConstants.BillPayType.HARARE
                )
            )
            add(
                BillPayItemBean(
                    R.drawable.billcode_dang,
                    ResourceHelper.getString(R.string.ZANU_PF_Membership_Payments),
                    CommonConstants.BillPayType.PARTY_FEES
                )
            )
            add(
                BillPayItemBean(
                    R.drawable.billcode_house,
                    ResourceHelper.getString(R.string.Ministry_of_National_Housing_and_Social_Amenities),
                    CommonConstants.BillPayType.SOCIAL_WELFARE
                )
            )
            add(
                BillPayItemBean(
                    R.drawable.billcode_school,
                    ResourceHelper.getString(R.string.school_Fees),
                    CommonConstants.BillPayType.SCHOOL_FEES
                )
            )
            add(
                BillPayItemBean(
                    R.drawable.billcode_others,
                    ResourceHelper.getString(R.string.others),
                    CommonConstants.BillPayType.OTHER
                )
            )
        }
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        context = recyclerView.context
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(context)
        val binding = ItemBillPayTypeBinding.inflate(
            inflater,
            parent,
            false
        )
        return object : RecyclerView.ViewHolder(binding.root) {}
    }

    override fun getItemCount() = data.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val binding =
            DataBindingUtil.getBinding<ItemBillPayTypeBinding>(holder.itemView) ?: return
        val bean = data[position]
        binding.iconIv.setImageResource(bean.iconId)
        binding.titleTv.text = bean.title
        binding.root.setOnClickListener(object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                val curBean = data[position]
                when (curBean.type) {
                    CommonConstants.BillPayType.ELE_FEES -> {
                        ZesaSearchActivity.jump(context)
                    }

                    CommonConstants.BillPayType.HARARE -> {
                        val mBean= BillCodeBean(CommonConstants.BillPayType.HARARE,ResourceHelper.getString(R.string.city_of_Harare),"","")
                        BillCodePayToCityActivity.jump(context, mBean)
                    }
                    CommonConstants.BillPayType.PARTY_FEES -> {
                        val mBean= BillCodeBean(CommonConstants.BillPayType.PARTY_FEES,ResourceHelper.getString(R.string.ZANU_PF_Membership_Payments),"","")
                        BillCodePayToCityActivity.jump(context, mBean)
                    }
                    CommonConstants.BillPayType.SOCIAL_WELFARE -> {
                        val mBean= BillCodeBean(CommonConstants.BillPayType.SOCIAL_WELFARE,ResourceHelper.getString(R.string.Ministry_of_National_Housing_and_Social_Amenities),"","")
                        BillCodePayToHouseActivity.jump(context, mBean)
                    }
                    CommonConstants.BillPayType.SCHOOL_FEES -> {
                        BillCodeSearchActivity.jump(context,"03")
                    }
                    CommonConstants.BillPayType.OTHER -> {
                        BillCodeSearchActivity.jump(context,"06")
                    }
                }
            }
        })
    }

//    interface OnClickListener {
//        fun onClick(bean: BalanceBean)
//    }
}