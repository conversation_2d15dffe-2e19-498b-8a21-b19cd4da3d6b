package om.rrtx.mobile.paymentmodule.presenter;

import android.content.Context;
import android.text.TextUtils;

import om.rrtx.mobile.paymentmodule.bean.AAPayDetailBean;
import om.rrtx.mobile.paymentmodule.bean.AARecDetailBean;
import om.rrtx.mobile.paymentmodule.bean.PubBean;
import om.rrtx.mobile.paymentmodule.model.PaymentModel;
import om.rrtx.mobile.paymentmodule.view.SplitBillView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * AA订单页面的P层
 */
public class SplitBillPresenter extends BasePresenter<SplitBillView> {

    private final PaymentModel mPaymentModel;
    private Context mContext;

    public SplitBillPresenter(Context context) {
        mPaymentModel = new PaymentModel();
        mContext = context;
    }

    /**
     * Pending发起订单详情
     *
     * @param orderNo 订单编号
     */
    public void requestRecDetail(String orderNo) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestRecDetail(userId,orderNo, new BaseObserver<AARecDetailBean>(mContext) {
                @Override
                public void requestSuccess(AARecDetailBean aaRecDetailBean) {
                    if (getView() != null) {
                        getView().recDetailSuccess(aaRecDetailBean);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        }else {
            mPaymentModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mPaymentModel.requestRecDetail(userId,orderNo, new BaseObserver<AARecDetailBean>(mContext) {
                        @Override
                        public void requestSuccess(AARecDetailBean aaRecDetailBean) {
                            if (getView() != null) {
                                getView().recDetailSuccess(aaRecDetailBean);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    /**
     * Pending发起订单详情
     *
     * @param orderNo 订单编号
     */
    public void requestPayDetail(String orderNo) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestPayDetail(userId,orderNo, new BaseObserver<AAPayDetailBean>(mContext) {
                @Override
                public void requestSuccess(AAPayDetailBean aaRecDetailBean) {
                    if (getView() != null) {
                        getView().payDetailSuccess(aaRecDetailBean);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        }else {
            mPaymentModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mPaymentModel.requestPayDetail(userId,orderNo, new BaseObserver<AAPayDetailBean>(mContext) {
                        @Override
                        public void requestSuccess(AAPayDetailBean aaRecDetailBean) {
                            if (getView() != null) {
                                getView().payDetailSuccess(aaRecDetailBean);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    /**
     * 关闭订单
     *
     * @param orderNo 订单编号
     */
    public void requestCancel(String orderNo) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestCancelOrder(userId,orderNo, new BaseObserver<Object>(mContext) {
                @Override
                public void requestSuccess(Object object) {
                    if (getView() != null) {
                        getView().cancelOrderSuccess();
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        }else {
            mPaymentModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mPaymentModel.requestCancelOrder(userId,orderNo, new BaseObserver<Object>(mContext) {
                        @Override
                        public void requestSuccess(Object object) {
                            if (getView() != null) {
                                getView().cancelOrderSuccess();
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    /**
     * 支付AA订单
     *
     * @param detailOrderNo   支付订单编号
     * @param paymentPassword 支付密码
     */
    public void requestPayOrder(String detailOrderNo, String paymentPassword) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestPayOrder(detailOrderNo, paymentPassword, new BaseObserver<Object>(mContext) {
                @Override
                public void requestSuccess(Object object) {
                    if (getView() != null) {
                        getView().payOrderSuccess();
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        }else {
            mPaymentModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mPaymentModel.requestPayOrder(detailOrderNo, paymentPassword, new BaseObserver<Object>(mContext) {
                        @Override
                        public void requestSuccess(Object object) {
                            if (getView() != null) {
                                getView().payOrderSuccess();
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }
}
