package om.rrtx.mobile.paymentmodule.activity

import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.InputFilter
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.Gson
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.payment_activity_zesa_pay.currency_tv
import kotlinx.android.synthetic.main.payment_activity_zesa_pay.inputHint_tv
import kotlinx.android.synthetic.main.payment_activity_zesa_pay.moneyTv
import kotlinx.android.synthetic.main.payment_activity_zesa_pay.name_tv
import kotlinx.android.synthetic.main.payment_activity_zesa_pay.nextTv
import kotlinx.android.synthetic.main.payment_activity_zesa_pay.number_tv
import kotlinx.android.synthetic.main.payment_base_title.backIv
import kotlinx.android.synthetic.main.payment_base_title.leftBg
import kotlinx.android.synthetic.main.payment_base_title.statusView
import kotlinx.android.synthetic.main.payment_base_title.titleTv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.functioncommon.bean.ZesaMerBean
import om.rrtx.mobile.functioncommon.dialog.CommonCurrencyDialog
import om.rrtx.mobile.paymentmodule.R
import om.rrtx.mobile.paymentmodule.databinding.PaymentActivityZesaPayBinding
import om.rrtx.mobile.paymentmodule.viewModel.BillPayWM
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.EditInputFilter
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity
import om.rrtx.mobile.rrtxcommon1.bean.QueryOrderBean
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.MoneyUtil
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.TextChangeEndListener

class ZesaPayActivity : BaseActivity<PaymentActivityZesaPayBinding>() {

    lateinit var mVM: BillPayWM
    lateinit var mBean: ZesaMerBean
    var curCurrency: String = "ZWG"

    private var minZWL = 10000.00F
    private var minUSD = 5.00F
    override fun createContentView() = R.layout.payment_activity_zesa_pay

    override fun doGetExtra() {
        val json = intent.getStringExtra(CommonConstants.Parameter.JSON_BEAN)
        if (StringUtils.isValidString(json)) {
            mBean = Gson().fromJson(json, ZesaMerBean::class.java)
        }
    }

    override fun initWorkspaceAction() {
        mVM = ViewModelProvider(this).get(BillPayWM::class.java)
        mVM.getLimitAmountSuccess.observe(this) {
            minZWL = it.zwgMinLimt.toFloat()
            minUSD = it.usdMinLimt.toFloat()
            setMoneyHint()
        }
    }

    override fun initView() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true, 0.2f)
            .init()
        dataBinding.includeTitle.apply {
            titleTv.setText(R.string.zESA)
            titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
            backIv.setBackgroundResource(R.drawable.common_ic_back_black)
            statusView.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
            titleTv.setBackgroundColor(resources.getColor(R.color.color_FFFFFF))
        }

        number_tv.text = mBean.meterNo
        name_tv.text = mBean.customerName

        curCurrency =
            if (StringUtils.isValidString(mBean.transCurrency)) mBean.transCurrency else CurrencyUtils.setCurrency(
                mContext,
                "ZWG"
            )
        currency_tv.text = curCurrency
        mVM.getLimitAmount()
        val filters = arrayOf<InputFilter>(EditInputFilter(mContext))
        moneyTv.filters = filters

    }

    private fun setMoneyHint() {
        when (curCurrency) {
            CommonConstants.Currency.ZWL -> {
                currency_tv.text = CurrencyUtils.setCurrency(mContext, "ZWG")
                val drawableLeft =
                    ResourceHelper.getDrawable(R.drawable.ic_extend)
                drawableLeft?.setBounds(0, 0, 14.pt2px(), 8.pt2px())
                currency_tv.setCompoundDrawables(
                    null,
                    null,
                    drawableLeft,
                    null
                )
                currency_tv.compoundDrawablePadding = 10.pt2px()
                inputHint_tv.visibility = View.VISIBLE
                inputHint_tv.text = getString(
                    R.string.min_amount_is,
                    "ZWG",
                    MoneyUtil.getTwoDecimal(minZWL.toString())
                )
            }

            CommonConstants.Currency.USD -> {
                currency_tv.setCompoundDrawables(
                    null,
                    null,
                    null,
                    null
                )
                inputHint_tv.visibility = View.VISIBLE
                inputHint_tv.text = getString(
                    R.string.min_amount_is,
                    "USD",
                    MoneyUtil.getTwoDecimal(minUSD.toString())
                )
            }

            else -> {
                currency_tv.setCompoundDrawables(
                    null,
                    null,
                    null,
                    null
                )
                inputHint_tv.visibility = View.GONE
            }
        }
    }

    override fun initListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    currency_tv -> {
                        if (getString(R.string.zwl) == curCurrency) {
                            val mCommonCurrencyDialog =
                                CommonCurrencyDialog(
                                    mContext,
                                    "1"
                                )
                            mCommonCurrencyDialog.setOnCurrencyClickListener(CommonCurrencyDialog.OnCurrencyClickListener { currency -> // 赋值货币符号
                                curCurrency = currency
                                currency_tv.text = curCurrency
                                moneyTv.text.clear()
                                setMoneyHint()
                            })
                            mCommonCurrencyDialog.show()
                        }
                    }

                    leftBg -> {
                        onBackPressed()
                    }

                    nextTv -> {
                        val queryOrderBean = QueryOrderBean(
                            "",
                            "",
                            "",
                            "",
                            mBean.meterNo,
                            mBean.customerName,
                            "",
                            moneyTv.text.toString(),
                            "",
                            curCurrency,
                            CommonConstants.PaymentProduct.ORDER_ZESA
                        )

                        ARouter.getInstance().build(ARouterPath.Cashier.CashierPayActivity)
                            .withString(BaseConstants.Transmit.JSON, Gson().toJson(queryOrderBean))
                            .navigation()
                    }
                }
            }

        }.apply {
            currency_tv.setOnClickListener(this)
            nextTv.setOnClickListener(this)
            leftBg.setOnClickListener(this)
        }

        moneyTv.addTextChangedListener(object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                if (editable.isNotEmpty()) {
                    val money = moneyTv.text.toString().toDouble()
                    when (curCurrency) {
                        CommonConstants.Currency.ZWL -> {
                            nextTv.isEnabled = money >= minZWL
                        }

                        CommonConstants.Currency.USD -> {
                            nextTv.isEnabled = money >= minUSD
                        }

                        else -> {
                            nextTv.isEnabled = true
                        }
                    }
                } else {
                    nextTv.isEnabled = false
                }
            }
        })
    }

    companion object {
        fun jump(context: Activity, bean: ZesaMerBean) {
            val intent = Intent(context, ZesaPayActivity::class.java)
            intent.putExtra(CommonConstants.Parameter.JSON_BEAN, Gson().toJson(bean))
            context.startActivity(intent)
        }
    }
}