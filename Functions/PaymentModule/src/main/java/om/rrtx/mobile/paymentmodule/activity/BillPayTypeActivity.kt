package om.rrtx.mobile.paymentmodule.activity

import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.payment_activity_bill_pay.rv_account
import kotlinx.android.synthetic.main.payment_base_title.backIv
import kotlinx.android.synthetic.main.payment_base_title.leftBg
import kotlinx.android.synthetic.main.payment_base_title.statusView
import kotlinx.android.synthetic.main.payment_base_title.titleTv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.paymentmodule.R
import om.rrtx.mobile.paymentmodule.adapter.BillPayTypeAdapter
import om.rrtx.mobile.paymentmodule.databinding.PaymentActivityBillPayBinding
import om.rrtx.mobile.paymentmodule.viewModel.BillPayWM
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.widget.SpacesItemDecoration

@Route(path = ARouterPath.Payment.BillPayTypeActivity)
class BillPayTypeActivity : BaseActivity<PaymentActivityBillPayBinding>() {

    lateinit var mVM: BillPayWM
    lateinit var maAdapter: BillPayTypeAdapter
    override fun createContentView() = R.layout.payment_activity_bill_pay

    override fun initWorkspaceAction() {
        mVM = ViewModelProvider(this).get(BillPayWM::class.java)
    }

    override fun initView() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .init()
        dataBinding.includeTitle.apply {
            titleTv.setText(R.string.bill_Payment)
            backIv.setBackgroundResource(R.drawable.ic_back_w)
            statusView.setBackgroundColor(ResourceHelper.getColor(R.color.common_ye_F3881E))
            titleTv.setBackgroundColor(resources.getColor(R.color.common_ye_F3881E))
        }

        rv_account.apply {
            layoutManager = LinearLayoutManager(mContext)
            maAdapter = BillPayTypeAdapter()
            adapter = maAdapter
            addItemDecoration(SpacesItemDecoration(24.pt2px()))
        }
    }

    override fun initListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    leftBg -> {
                        onBackPressed()
                    }
                }
            }

        }.apply {
            leftBg.setOnClickListener(this)
        }
    }
}
