package om.rrtx.mobile.paymentmodule.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide
import om.rrtx.mobile.paymentmodule.bean.AppBundleBean
import om.rrtx.mobile.paymentmodule.databinding.ItemAppOptionBinding
import om.rrtx.mobile.paymentmodule.databinding.ItemBundleOptionBinding
import om.rrtx.mobile.rrtxcommon1.base.BaseAdapterCallback
import om.rrtx.mobile.rrtxcommon1.base.BaseAdapterDataCallback
import om.rrtx.mobile.rrtxcommon1.bean.InfoItemBean
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener

class AppOptionAdapter(var callback: BaseAdapterDataCallback<AppBundleBean>) :
    RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    lateinit var context: Context

    private var data = arrayListOf<AppBundleBean>()

    private var selectPosition = 0

    fun setNewData(list: ArrayList<AppBundleBean>) {
        data = list
        notifyDataSetChanged()
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        context = recyclerView.context
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(context)
        val binding = ItemAppOptionBinding.inflate(
            inflater,
            parent,
            false
        )
        return object : RecyclerView.ViewHolder(binding.root) {}
    }

    override fun getItemCount() = data.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val binding =
            DataBindingUtil.getBinding<ItemAppOptionBinding>(holder.itemView) ?: return
        val bean = data[position]

        binding.root.isSelected = selectPosition == position
        Glide.with(context).load(bean.appIcon).into(binding.iconIv)
        if (binding.root.isSelected){
            binding.iconSan.visibility = View.VISIBLE
        }else{
            binding.iconSan.visibility = View.GONE
        }

        binding.root.setOnClickListener(object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                selectPosition = position
                notifyDataSetChanged()
                callback.callBack(position, bean)
            }
        })

    }

}