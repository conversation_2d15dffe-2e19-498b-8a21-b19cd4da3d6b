package om.rrtx.mobile.paymentmodule.activity

import android.content.Context
import android.content.Intent
import android.text.Editable
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.payment_activity_select_contacts.rightBg
import kotlinx.android.synthetic.main.payment_activity_zesa_search.next_Tv
import kotlinx.android.synthetic.main.payment_activity_zesa_search.number_ed
import kotlinx.android.synthetic.main.payment_base_title.leftBg
import kotlinx.android.synthetic.main.payment_base_title.view.backIv
import kotlinx.android.synthetic.main.payment_base_title.view.rightIv
import kotlinx.android.synthetic.main.payment_base_title.view.statusView
import kotlinx.android.synthetic.main.payment_base_title.view.titleTv
import om.rrtx.mobile.paymentmodule.R
import om.rrtx.mobile.paymentmodule.databinding.PaymentActivityZesaSearchBinding
import om.rrtx.mobile.paymentmodule.viewModel.BillPayWM
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.TextChangeEndListener

class ZesaSearchActivity : BaseActivity<PaymentActivityZesaSearchBinding>() {

    companion object {
        @JvmStatic
        fun jump(context: Context) {
            val intent = Intent(context, ZesaSearchActivity::class.java)
            context.startActivity(intent)
        }
    }

    lateinit var mVM: BillPayWM
    override fun createContentView() = R.layout.payment_activity_zesa_search

    override fun initWorkspaceAction() {
        mVM = ViewModelProvider(this).get(BillPayWM::class.java)
        mVM.checkZesaNumberSuccess.observe(this) {
            ZesaPayActivity.jump(mContext, it)
        }
    }

    override fun initView() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true, 0.2f)
            .init()
        dataBinding.includeTitle.apply {
            titleTv.setText(R.string.zESA)
            titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
            backIv.setBackgroundResource(R.drawable.common_ic_back_black)
            rightIv.setBackgroundResource(R.drawable.ic_re_token)
            rightIv.visibility = View.VISIBLE
            statusView.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
            titleTv.setBackgroundColor(resources.getColor(R.color.color_FFFFFF))
        }

        // 自动拉起键盘
        number_ed.isFocusable = true
        number_ed.isFocusableInTouchMode = true
        number_ed.requestFocus()
        val inputMethodManager = this.getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        inputMethodManager.showSoftInput(number_ed, 0)
    }

    override fun initListener() {
        object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                if (editable.length >= 11) {
                    next_Tv.isEnabled = true
                    next_Tv.setBackgroundResource(R.drawable.common_usable_btn)
                } else {
                    next_Tv.isEnabled = false
                    next_Tv.setBackgroundResource(R.drawable.common_unusable_btn)
                }
            }

        }.apply {
            number_ed.addTextChangedListener(this)
        }


        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    rightBg -> {
                        ReSendTokenActivity.jump(mContext)
                    }

                    leftBg -> {
                        onBackPressed()
                    }

                    next_Tv -> {
                        mVM.checkZesaNumber(number_ed.text.toString())
                    }
                }
            }

        }.apply {
            rightBg.setOnClickListener(this)
            leftBg.setOnClickListener(this)
            next_Tv.setOnClickListener(this)
        }
    }
}