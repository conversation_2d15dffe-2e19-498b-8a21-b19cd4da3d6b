package om.rrtx.mobile.paymentmodule.utils;

import android.text.TextUtils;
import android.widget.TextView;

import com.google.gson.Gson;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.paymentmodule.bean.ByMobileBean;
import om.rrtx.mobile.paymentmodule.bean.PayerInfosBean;

/**
 * <AUTHOR>
 * 联系人页面的帮助类
 */
public class SelectContactHelper {

    /**
     * <AUTHOR>
     * 获取搜索的集合
     * 这里搜索是搜索的是真是姓名和手机号
     */
    public static List<ByMobileBean> getSearchList(String search, List<ByMobileBean> list) {
        List<ByMobileBean> searchList = new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            ByMobileBean mobileBean = list.get(i);
            if (mobileBean.getRealName().toLowerCase().contains(search.toLowerCase()) || mobileBean.getMobile().toLowerCase().contains(search.toLowerCase())) {
                searchList.add(mobileBean);
            }
        }

        return searchList;
    }


    /**
     * 请求索引的角标
     *
     * @return 索引的角标
     */
    public static int reasonIndexNum(String search, List<ByMobileBean> byMobileBeans) {
        if (byMobileBeans == null || byMobileBeans.size() == 0) {
            return -1;
        }
        if (TextUtils.equals(search, "#")) {
            for (int i = 0; i < byMobileBeans.size(); i++) {
                ByMobileBean mobileBean = byMobileBeans.get(i);
                boolean upperCase = Character.isUpperCase(mobileBean.getRealName().toUpperCase().charAt(0));
                if (!upperCase) {
                    return i;
                }
            }
        } else {
            for (int i = 0; i < byMobileBeans.size(); i++) {
                ByMobileBean mobileBean = byMobileBeans.get(i);
                String s1 = String.valueOf(mobileBean.getRealName().toUpperCase().charAt(0));
                if (TextUtils.equals(search.toUpperCase(), s1)) {
                    return i;
                }
            }
        }
        return -1;
    }

    /**
     * 处理集合
     * 这里因为要把选择的结合和联系人列表的集合关联起来
     *
     * @param selectList  之前选择的结合
     * @param contactList 请求接口返回的结合
     * @return 处理之后的集合
     */
    public static List<ByMobileBean> handlerList(List<ByMobileBean> selectList, List<ByMobileBean> contactList) {
        for (int i = 0; i < contactList.size(); i++) {
            for (int j = 0; j < selectList.size(); j++) {
                ByMobileBean byMobileBean = contactList.get(i);
                ByMobileBean selectByMobileBean = selectList.get(j);
                if (TextUtils.equals(byMobileBean.getMobile(), selectByMobileBean.getMobile())) {
                    byMobileBean.setSelect(selectByMobileBean.isSelect());
                }
            }
        }

        return contactList;
    }

    /**
     * 移除条目
     *
     * @param selectList     选择的结合
     * @param selectByMobile 需要移除的对象
     */
    public static void removeItem(List<ByMobileBean> selectList, ByMobileBean selectByMobile) {
        for (int i = 0; i < selectList.size(); i++) {
            ByMobileBean byMobileBean = selectList.get(i);
            if (TextUtils.equals(selectByMobile.getMobile(), byMobileBean.getMobile())) {
                selectList.remove(i);
                return;
            }
        }
    }

    /**
     * 选择后的数据瓶装
     *
     * @param selectList 选择的联系人
     * @param amt        每个人的金额
     */
    public static List<PayerInfosBean> handlerSelectPayer(List<ByMobileBean> selectList, String amt) {
        List<PayerInfosBean> list = new ArrayList<>();
        if (!TextUtils.isEmpty(amt)) {
            //等额
            for (int i = 0; i < selectList.size(); i++) {
                ByMobileBean byMobileBean = selectList.get(i);
                PayerInfosBean payerInfosBean = new PayerInfosBean();
                payerInfosBean.setMobile(byMobileBean.getMobile());
                payerInfosBean.setTransAmt(amt);
                list.add(payerInfosBean);
            }
        } else {
            for (int i = 0; i < selectList.size(); i++) {
                ByMobileBean byMobileBean = selectList.get(i);
                PayerInfosBean payerInfosBean = new PayerInfosBean();
                payerInfosBean.setMobile(byMobileBean.getMobile());
                payerInfosBean.setTransAmt(byMobileBean.getAmount());
                list.add(payerInfosBean);
            }
        }

        return list;
    }

    /**
     * 获取总金额
     *
     * @param selectList 选择的集合
     * @return 总金额
     */
    public static String getTotalAmt(List<ByMobileBean> selectList) {
        String amountTotal = "0";

        for (int i = 0; i < selectList.size(); i++) {
            ByMobileBean byMobileBean = selectList.get(i);
            String amount = byMobileBean.getAmount();

            BigDecimal totalBd = new BigDecimal(amountTotal);
            if (TextUtils.isEmpty(amount)) {
                amount = "0";
            }
            BigDecimal currentBd = new BigDecimal(amount);

            amountTotal = totalBd.add(currentBd).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        }

        return amountTotal;
    }

    /**
     * 处理选择之后的集合
     * 这里主要是处理 selectList中的数据不包含在总集合中
     *
     * @param selectList  选择的集合
     * @param contactList 未包含的集合
     */
    public static List<ByMobileBean> handlerSelectInTotalList(List<ByMobileBean> selectList, List<ByMobileBean> contactList) {


        for (int i = 0; i < selectList.size(); i++) {
            ByMobileBean byMobileBean = selectList.get(i);

            boolean isContain = false;
            for (int j = 0; j < contactList.size(); j++) {
                ByMobileBean totalBymobileBean = contactList.get(j);
                if (TextUtils.equals(totalBymobileBean.getMobile(), byMobileBean.getMobile())) {
                    isContain = true;
                    break;
                }
            }

            if (!isContain) {
                selectList.remove(i);
                i--;
            }
        }

        return selectList;
    }
}
