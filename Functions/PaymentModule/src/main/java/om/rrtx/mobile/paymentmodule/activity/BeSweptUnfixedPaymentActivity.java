package om.rrtx.mobile.paymentmodule.activity;

import android.content.Context;
import android.content.Intent;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import java.math.BigDecimal;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean;
import om.rrtx.mobile.functioncommon.callback.CashierCallBack;
import om.rrtx.mobile.functioncommon.dialog.CommonCurrencyDialog;
import om.rrtx.mobile.functioncommon.utils.CashierManager;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.functioncommon.bean.QrCodeCreateOrderBean;
import om.rrtx.mobile.paymentmodule.presenter.BeSweptPaymentPresenter;
import om.rrtx.mobile.paymentmodule.utils.EditInputFilter;
import om.rrtx.mobile.paymentmodule.view.BeSweptPaymentView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.bean.QueryOrderBean;
import om.rrtx.mobile.rrtxcommon1.dialog.DoubleDialog;
import om.rrtx.mobile.rrtxcommon1.utils.BigDecimalUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.LogUtil;
import om.rrtx.mobile.rrtxcommon1.utils.SoftKeyBoardListener;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 主扫非固定金额
 */
@Route(path = ARouterPath.Payment.BeSweptUnfixedPaymentActivity)
public class BeSweptUnfixedPaymentActivity extends BaseSuperActivity<BeSweptPaymentView, BeSweptPaymentPresenter>
        implements BeSweptPaymentView, TextWatcher {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.iconIv)
    ImageView mIconIv;
    @BindView(R2.id.merName)
    TextView mMerName;
    @BindView(R2.id.moneyTypeTv)
    TextView mMoneyTypeTv;
    @BindView(R2.id.moneyEt)
    EditText mMoneyEt;
    @BindView(R2.id.confirmTv)
    TextView mConfirmTv;
    @BindView(R2.id.icon)
    ImageView mIcon;
    @BindView(R2.id.line)
    View mLine;
    private DoubleDialog mDoubleDialog;
    private String mQrCode;
    private QueryOrderBean mQueryOrderBean;
    private String mPayCheckBeanStr;
    private String mAmountStr;
    private CashierManager mCashierManager;
    CommonCurrencyDialog mCommonCurrencyDialog;

    public static void jumpBeSweptUnfixedPayment(Context context, String payCheckBeanStr, String qrCode) {
        Intent intent = new Intent(context, BeSweptUnfixedPaymentActivity.class);
        intent.putExtra(PaymentConstants.Transmit.PAYCHECKDETAILS, payCheckBeanStr);
        intent.putExtra(PaymentConstants.Transmit.QRCODE, qrCode);
        context.startActivity(intent);
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        mPayCheckBeanStr = getIntent().getStringExtra(BaseConstants.Transmit.JSON);
        LogUtil.e("TAG", "doGetExtra: " + mPayCheckBeanStr);
        mQueryOrderBean = new Gson().fromJson(mPayCheckBeanStr, QueryOrderBean.class);
        mQrCode = getIntent().getStringExtra(PaymentConstants.Transmit.QRCODE);
    }

    @Override
    protected int createContentView() {
        return R.layout.payment_activity_be_swept_unfixed_payment;
    }

    @Override
    protected BeSweptPaymentPresenter createPresenter() {
        return new BeSweptPaymentPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.payment_ic_back);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setText(R.string.payment_title_payment);
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));

        mMoneyTypeTv.setText(CurrencyUtils.setCurrency(mContext, mQueryOrderBean.getCurrency()));

        if (TextUtils.isEmpty(mQueryOrderBean.getCurrency())) {
            mIcon.setVisibility(View.VISIBLE);
            mLine.setVisibility(View.VISIBLE);
        }

    }

    @Override
    public void initDate() {
        super.initDate();

        if (mQueryOrderBean != null) {
            String merStr = getString(R.string.payment_label_pay_to) + " " + mQueryOrderBean.getMerName();
            mMerName.setText(merStr);
        }

        //设置EditText的过滤
        InputFilter[] filters = {new EditInputFilter(mContext, 9999999999.99)};
        mMoneyEt.setFilters(filters);

        mMoneyEt.requestFocus();
    }

    @Override
    public void initListener() {
        super.initListener();
        SoftKeyBoardListener.setListener(mContext, new SoftKeyBoardListener.OnSoftKeyBoardChangeListener() {
            @Override
            public void keyBoardShow(int height) {
                //键盘显示
                LogUtil.e("done", "keyBoardShow: ");
            }

            @Override
            public void keyBoardHide(int height) {
                LogUtil.e("done", "keyBoardHide: ");
                //键盘隐藏的时候设置相应的金额
                mAmountStr = mMoneyEt.getText().toString();
                if (!TextUtils.isEmpty(mAmountStr)) {
                    mAmountStr = BigDecimalUtils.getStandardAmount(mAmountStr, 2, BigDecimal.ROUND_HALF_UP);
                    mMoneyEt.setText(mAmountStr);
                    mMoneyEt.setSelection(mAmountStr.length());
                }
            }
        });

        mConfirmTv.setOnClickListener(mCustomClickListener);
        mLeftBg.setOnClickListener(mCustomClickListener);
        mMoneyEt.addTextChangedListener(this);
        if (TextUtils.isEmpty(mQueryOrderBean.getCurrency())) {
            mIcon.setOnClickListener(mCustomClickListener);
            mMoneyTypeTv.setOnClickListener(mCustomClickListener);
        }
    }

    private CustomClickListener mCustomClickListener = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.confirmTv) {
                if (!TextUtils.isEmpty(mAmountStr)) {
                    mAmountStr = BigDecimalUtils.getStandardAmount(mAmountStr, 2, BigDecimal.ROUND_HALF_UP);
                    mMoneyEt.setText(mAmountStr);
                    mMoneyEt.setSelection(mAmountStr.length());

                    if (TextUtils.equals(mAmountStr, "0") || TextUtils.equals(mAmountStr, "0.00")) {
                        String hint = getResources().getString(R.string.payment_tip_amount_must_greater) + mAmountStr;
                        ToastUtil.show(mContext, hint);
                        return;
                    }

                    mQueryOrderBean.setAmt(mAmountStr);
                    mQueryOrderBean.setCurrency(mMoneyTypeTv.getText().toString());
                    ARouter.getInstance().build(ARouterPath.Cashier.CashierPayActivity)
                            .withString(BaseConstants.Transmit.JSON, new Gson().toJson(mQueryOrderBean))
                            .withString(UserConstants.Transmit.QRCODE, mQrCode).navigation();

//                    mPresenter.requestQrCodePayment(PaymentConstants.PaymentProduct.QRCODE, mPayCheckBean.getMerNo(),
//                            mPayCheckBean.getMerName(), mPayCheckBean.getCheckstandNo(), mAmountStr,
//                            PaymentConstants.PaymentPasswordType.PSD, mQrCode,mMoneyTypeTv.getText().toString());
                }
            } else if (view.getId() == R.id.leftBg) {
                if (mDoubleDialog == null) {
                    mDoubleDialog = new DoubleDialog(mContext);
                    mDoubleDialog.setDoubleCallback(new DoubleDialog.DoubleCallback() {
                        @Override
                        public void leftCallback() {
                            mDoubleDialog.dismiss();
                            finish();
                        }

                        @Override
                        public void rightCallback() {
                            mDoubleDialog.dismiss();
                        }
                    });
                }
                mDoubleDialog.show();
                mDoubleDialog.show();
                mDoubleDialog.setMyTitle(getString(R.string.common_alert_prompt))
                        .setContentStr(getString(R.string.payment_alert_cancal_pay_tip))
                        .setLeftStr(getString(R.string.common_alert_continue))
                        .setLeftColor(getResources().getColor(R.color.color_F85A40))
                        .setRightStr(getString(R.string.common_alert_cancel))
                        .setRightColor(getResources().getColor(R.color.common_ye_F3881E));
            } else if (view.getId() == R.id.icon || view.getId() == R.id.moneyTypeTv) {
                mCommonCurrencyDialog = new CommonCurrencyDialog(BeSweptUnfixedPaymentActivity.this);
                mCommonCurrencyDialog.setOnCurrencyClickListener(new CommonCurrencyDialog.OnCurrencyClickListener() {
                    @Override
                    public void onCurrencyClick(String currency) {
                        // 赋值货币符号
                        mMoneyTypeTv.setText(currency);
                        if (mCommonCurrencyDialog != null) {
                            mCommonCurrencyDialog = null;
                        }
                    }
                });
                if (mCommonCurrencyDialog != null) {
                    mCommonCurrencyDialog.show();
                }
            }
        }
    };

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    public void qrCodePaymentSuccess(QrCodeCreateOrderBean qrCodeCreateOrderBean) {
        mQueryOrderBean.setAmt(mAmountStr);

        CashierOrderInfoBean cashierOrderInfoBean = new CashierOrderInfoBean.Builder()
                .setPayType(CommonConstants.CashierPaymentType.Cashier_Mer)
                .setPaymentProduct(CommonConstants.PaymentProduct.QRCODE)
                .setMerNo(mQueryOrderBean.getMerNo())
                .setMerName(mQueryOrderBean.getMerName())
                .setOrderSource(BaseConstants.OrderSourceType.INSIDE)
                .setQrCode(mQrCode)
                .setOrderNo(qrCodeCreateOrderBean.getOrderNo())
                .setOrderAmt(mQueryOrderBean.getAmt())
                .setTransType(CommonConstants.TransType.Payment)
                .setCurrency(CurrencyUtils.setCurrency(mContext, qrCodeCreateOrderBean.getCurrency()))
                .setTransferToken(qrCodeCreateOrderBean.getOrderNo())
                .setOrderInfo(getResources().getString(R.string.checkout_label_default_commodity))
                .builder();

        mCashierManager = new CashierManager(this, new Gson().toJson(cashierOrderInfoBean), new CashierCallBack() {
            @Override
            public void paymentFailed(@NonNull String message) {
            }

            @Override
            public void cancelOrderPay() {
                mCashierManager.dismiss();
            }

            @Override
            public void forgotCallBack() {
                ARouter.getInstance()
                        .build(ARouterPath.SecurityPath.PayPinSendActivity)
                        .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.BESWEPTUNFIXEDPAYMENTACTIVITY)
                        .navigation();
            }

            @Override
            public void paymentSuccess(String dataJson) {
                PaymentSuccessActivity.jumpPaymentSuccess(mContext, dataJson);
            }
        });
        mCashierManager.showCashierDialog();
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        String str = mMoneyEt.getText().toString();
        if (!TextUtils.isEmpty(str)) {
            String money = BigDecimalUtils.getStandardAmount(str, 2, BigDecimal.ROUND_HALF_UP);
            if (str.length() > 0 && Double.valueOf(money) > 0) {
                mConfirmTv.setEnabled(true);
            } else {
                mConfirmTv.setEnabled(false);
            }
        } else {
            mConfirmTv.setEnabled(false);
        }
    }

    @Override
    public void onBackPressed() {
        if (mDoubleDialog == null) {
            mDoubleDialog = new DoubleDialog(mContext);
            mDoubleDialog.setDoubleCallback(new DoubleDialog.DoubleCallback() {
                @Override
                public void leftCallback() {
                    mDoubleDialog.dismiss();
                    finish();
                }

                @Override
                public void rightCallback() {
                    mDoubleDialog.dismiss();
                }
            });
        }
        mDoubleDialog.show();
        mDoubleDialog.show();
        mDoubleDialog.setMyTitle(getString(R.string.common_alert_prompt))
                .setContentStr(getString(R.string.payment_alert_cancal_pay_tip))
                .setLeftStr(getString(R.string.common_alert_continue))
                .setLeftColor(getResources().getColor(R.color.color_F85A40))
                .setRightStr(getString(R.string.common_alert_cancel))
                .setRightColor(getResources().getColor(R.color.common_ye_F3881E));

    }
}
