package om.rrtx.mobile.paymentmodule.activity

import android.content.Context
import android.content.Intent
import android.text.Editable
import android.view.View
import android.view.inputmethod.InputMethodManager
import androidx.lifecycle.ViewModelProvider
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.payment_activity_billcode_search.meterNumber_tv
import kotlinx.android.synthetic.main.payment_activity_billcode_search.next_Tv
import kotlinx.android.synthetic.main.payment_activity_billcode_search.number_ed
import kotlinx.android.synthetic.main.payment_base_title.leftBg
import kotlinx.android.synthetic.main.payment_base_title.view.backIv
import kotlinx.android.synthetic.main.payment_base_title.view.statusView
import kotlinx.android.synthetic.main.payment_base_title.view.titleTv
import om.rrtx.mobile.paymentmodule.R
import om.rrtx.mobile.paymentmodule.databinding.PaymentActivityBillcodeSearchBinding
import om.rrtx.mobile.paymentmodule.viewModel.BillPayWM
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.TextChangeEndListener

class BillCodeSearchActivity : BaseActivity<PaymentActivityBillcodeSearchBinding>() {

    companion object {
        @JvmStatic
        fun jump(context: Context,billerCategory:String) {
            val intent = Intent(context, BillCodeSearchActivity::class.java)
            intent.putExtra("billerCategory",billerCategory)
            context.startActivity(intent)
        }
    }

    private var billerCategory = ""
    lateinit var mVM: BillPayWM
    override fun createContentView() = R.layout.payment_activity_billcode_search

    override fun initWorkspaceAction() {
        mVM = ViewModelProvider(this).get(BillPayWM::class.java)
        mVM.checkMerchantCodeSuccess.observe(this) {
            /**
             * 缴费类型 billerCategory
             * 01-City of Harare
             * 03-School Fees
             * 04-ZANU PF Membership Payments
             * 05-Ministry of National Housing and Social Amenities
             * 06-Others
             * **/
            it.billerCode = number_ed.text.toString()
            when (it.billerCategory) {
                /*"01", "04" -> {
                    BillCodePayToCityActivity.jump(mContext, it)
                }*/
                "03" -> {
                    BillCodePayToSchoolActivity.jump(mContext, it)
                }
                /*"05" -> {
                    BillCodePayToHouseActivity.jump(mContext, it)
                }*/
                else -> {
                    BillCodePayToOtherActivity.jump(mContext, it)
                }
            }

        }
    }

    override fun initView() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true, 0.2f)
            .init()
        dataBinding.includeTitle.apply {
            titleTv.setText(R.string.bill_Payment)
            titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
            backIv.setBackgroundResource(R.drawable.common_ic_back_black)
            statusView.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
            titleTv.setBackgroundColor(resources.getColor(R.color.color_FFFFFF))
        }

        billerCategory  = intent.getStringExtra("billerCategory").toString();
        if ("03" == billerCategory){
            meterNumber_tv.text = getString(R.string.school_bill_code)
        }
        // 自动拉起键盘
        number_ed.isFocusable = true
        number_ed.isFocusableInTouchMode = true
        number_ed.requestFocus()
        val inputMethodManager = this.getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager
        inputMethodManager.showSoftInput(number_ed, 0)
    }

    override fun initListener() {
        object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                if (editable.length > 4) {
                    next_Tv.isEnabled = true
                    next_Tv.setBackgroundResource(R.drawable.common_usable_btn)
                } else {
                    next_Tv.isEnabled = false
                    next_Tv.setBackgroundResource(R.drawable.common_unusable_btn)
                }
            }

        }.apply {
            number_ed.addTextChangedListener(this)
        }


        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    leftBg -> {
                        onBackPressed()
                    }

                    next_Tv -> {
                        mVM.billerCodeCheck(billerCategory,number_ed.text.toString())
                    }
                }
            }

        }.apply {
            leftBg.setOnClickListener(this)
            next_Tv.setOnClickListener(this)
        }
    }
}