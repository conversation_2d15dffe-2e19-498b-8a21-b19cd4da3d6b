package om.rrtx.mobile.paymentmodule.presenter;

import android.content.Context;

import om.rrtx.mobile.paymentmodule.model.PaymentModel;
import om.rrtx.mobile.paymentmodule.view.BeSweptPaymentView;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;

/**
 * <AUTHOR>
 * AA订单页面的P层
 */
public class BeSweptPaymentPresenter extends BasePresenter<BeSweptPaymentView> {

    private final PaymentModel mPaymentModel;
    private Context mContext;

    public BeSweptPaymentPresenter(Context context) {
        mPaymentModel = new PaymentModel();
        mContext = context;
    }
}
