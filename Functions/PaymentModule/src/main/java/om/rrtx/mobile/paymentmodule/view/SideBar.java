package om.rrtx.mobile.paymentmodule.view;

import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.drawable.ColorDrawable;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;

import om.rrtx.mobile.paymentmodule.R;


/**
 * <AUTHOR> Angle
 * 创建时间 : 2019/4/8 11:55
 * 描述 :
 */
public class SideBar extends View {

    /**
     * 触摸事件回调
     */
    private OnTouchingLetterChangedListener onTouchingLetterChangedListener;

    /**
     * 26个字母
     */
    public static String[] b = {"A", "B", "C", "D", "E", "F", "G",
            "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q",
            "R", "S", "T", "U", "V", "W", "X", "Y", "Z", "#"};
    /**
     * 选中的标识
     */
    private int choose = -1;
    /**
     * 画笔
     */
    private Paint paint = new Paint();

    /**
     * 每一个字母的高度
     */
    private float singleHeight;

    private TextView mSideBarTv;
    private Context mContext;

    public SideBar(Context context) {
        this(context, null);
    }

    public SideBar(Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public SideBar(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        mContext = context;
    }


    @Override
    protected void onDraw(Canvas canvas) {
        super.onDraw(canvas);
        Log.e("ondraw", "onDraw: ");
        // 获取焦点改变背景颜色.
        // 获取对应高度
        int height = getHeight();
        // 获取对应宽度
        int width = getWidth();

        // 获取每一个字母的高度
        singleHeight = (height * 1f) / b.length;
        // 中间点绘制的卫位置
        singleHeight = (height * 1f - singleHeight / 2) / b.length;
        for (int i = 0; i < b.length; i++) {
            paint.setColor(mContext.getResources().getColor(R.color.common_ye_F3881E));
            paint.setAntiAlias(true);
            paint.setTextSize(25);
            // 选中的状态
            if (i == choose) {
                paint.setColor(Color.parseColor("#c60000"));
                paint.setFakeBoldText(true);
            }
            // x坐标等于中间-字符串宽度的一半.
            float xPos = width / 2 - paint.measureText(b[i]) / 2;
            float yPos = singleHeight * i + singleHeight;
            canvas.drawText(b[i], xPos, yPos, paint);
            paint.reset();// 重置画笔
        }
    }

    @Override
    public boolean dispatchTouchEvent(MotionEvent event) {
        final int action = event.getAction();
        // 点击y坐标
        final float y = event.getY();
        final int oldChoose = choose;
        final OnTouchingLetterChangedListener listener = onTouchingLetterChangedListener;
        // 点击y坐标所占总高度的比例*b数组的长度就等于点击b中的个数.
        final int c = (int) (y / getHeight() * b.length);

        switch (action) {
            case MotionEvent.ACTION_UP:
                if (mSideBarTv != null) {
                    mSideBarTv.setVisibility(View.GONE);
                }
                setBackground(new ColorDrawable(0x00000000));
                choose = -1;
                invalidate();
                break;
            // 除开松开事件的任何触摸事件
            default:
                setBackground(new ColorDrawable(0x50000000));
                if (oldChoose != c) {
                    if (c >= 0 && c < b.length) {
                        if (mSideBarTv != null) {
                            mSideBarTv.setVisibility(View.VISIBLE);
                            mSideBarTv.setText(b[c]);
                        }
                        if (listener != null) {
                            listener.onTouchingLetterChanged(b[c]);
                        }
                        choose = c;
                        invalidate();
                    }
                }
                break;
        }
        return true;
    }

    /**
     * 向外公开的方法
     *
     * @param onTouchingLetterChangedListener
     */
    public void setOnTouchingLetterChangedListener(OnTouchingLetterChangedListener onTouchingLetterChangedListener) {
        this.onTouchingLetterChangedListener = onTouchingLetterChangedListener;
    }

    public void setTextView(TextView sideIndexTv) {
        mSideBarTv = sideIndexTv;
    }

    /**
     * 接口
     *
     * <AUTHOR>
     */
    public interface OnTouchingLetterChangedListener {
        void onTouchingLetterChanged(String s);
    }
}
