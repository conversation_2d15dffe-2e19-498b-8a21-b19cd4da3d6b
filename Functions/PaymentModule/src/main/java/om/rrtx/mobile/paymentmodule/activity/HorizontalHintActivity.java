package om.rrtx.mobile.paymentmodule.activity;

import android.content.Context;
import android.content.Intent;
import android.content.res.Resources;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import butterknife.ButterKnife;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.AdaptScreenUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;

/**
 * <AUTHOR>
 * 横屏说明页面
 */
public class HorizontalHintActivity extends BaseSuperActivity {

    @BindView(R2.id.doneTv)
    TextView mDoneTv;
    private String mQrCode;

    public static void jumpHorizontalHint(Context context, String qrCode) {
        Intent intent = new Intent(context, HorizontalHintActivity.class);
        intent.putExtra(PaymentConstants.Transmit.QRCODE, qrCode);
        context.startActivity(intent);
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        mQrCode = getIntent().getStringExtra(PaymentConstants.Transmit.QRCODE);
    }

    @Override
    protected int createContentView() {
        return R.layout.payment_activity_horizontal_hint;
    }

    @Override
    public Resources getResources() {
        return AdaptScreenUtils.adaptHeight(super.getResources(), designWidthInpx);
    }


    @Override
    protected BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarColor(R.color.color_F5F7F8)
                .statusBarDarkFont(true, 0.2f)
                .init();
    }

    @Override
    public void initListener() {
        super.initListener();
        mDoneTv.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                if (!TextUtils.isEmpty(mQrCode)) {
                    BarCodeActivity.jumpBarCode(mContext, mQrCode);
                }
            }
        });
    }
}
