package om.rrtx.mobile.paymentmodule.bean;

import java.util.List;

/**
 * <AUTHOR>
 * Pending付款订单详情实体类
 */
public class AAPayDetailBean {
    /**
     * detailOrderNo : 8110011878396133703
     * payAmt : 1.00
     * orderStatus : 10
     * totalAmt : 3.00
     * recAmt : 2.00
     * actRecAmt : 0.00
     * totalOrderNumber : 2
     * recOrderNumber : 0
     * unpaidOrderNumber : 2
     * tradeTime : 2020-02-29 00:06:44
     * currencyType : USD
     * splitType :
     * remark :
     * userAvatar :
     * paidPayList : []
     * unpaidPayList : [{"payCstNo":"8097569625803337731","payCstName":"hejinlong","payMobile":"+60121261619616","transAmt":"1.00","detailOrderStatus":"10","userAvatar":""},{"payCstNo":"8097575848523087903","payCstName":"hongdou","payMobile":"+93123456789001","transAmt":"1.00","detailOrderStatus":"10","userAvatar":""}]
     */

    private String detailOrderNo;
    private String payAmt;
    private String orderStatus;
    private String totalAmt;
    private String recAmt;
    private String actRecAmt;
    private int totalOrderNumber;
    private int recOrderNumber;
    private int unpaidOrderNumber;
    private String tradeTime;
    private String currencyType;
    private String splitType;
    private String remark;
    private String userAvatar;
    private List<AARecDetailBean.PayListBean> paidPayList;
    private List<AARecDetailBean.PayListBean> unpaidPayList;

    public String getDetailOrderNo() {
        return detailOrderNo;
    }

    public void setDetailOrderNo(String detailOrderNo) {
        this.detailOrderNo = detailOrderNo;
    }

    public String getPayAmt() {
        return payAmt;
    }

    public void setPayAmt(String payAmt) {
        this.payAmt = payAmt;
    }

    public String getOrderStatus() {
        return orderStatus;
    }

    public void setOrderStatus(String orderStatus) {
        this.orderStatus = orderStatus;
    }

    public String getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(String totalAmt) {
        this.totalAmt = totalAmt;
    }

    public String getRecAmt() {
        return recAmt;
    }

    public void setRecAmt(String recAmt) {
        this.recAmt = recAmt;
    }

    public String getActRecAmt() {
        return actRecAmt;
    }

    public void setActRecAmt(String actRecAmt) {
        this.actRecAmt = actRecAmt;
    }

    public int getTotalOrderNumber() {
        return totalOrderNumber;
    }

    public void setTotalOrderNumber(int totalOrderNumber) {
        this.totalOrderNumber = totalOrderNumber;
    }

    public int getRecOrderNumber() {
        return recOrderNumber;
    }

    public void setRecOrderNumber(int recOrderNumber) {
        this.recOrderNumber = recOrderNumber;
    }

    public int getUnpaidOrderNumber() {
        return unpaidOrderNumber;
    }

    public void setUnpaidOrderNumber(int unpaidOrderNumber) {
        this.unpaidOrderNumber = unpaidOrderNumber;
    }

    public String getTradeTime() {
        return tradeTime;
    }

    public void setTradeTime(String tradeTime) {
        this.tradeTime = tradeTime;
    }

    public String getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(String currencyType) {
        this.currencyType = currencyType;
    }

    public String getSplitType() {
        return splitType;
    }

    public void setSplitType(String splitType) {
        this.splitType = splitType;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getUserAvatar() {
        return userAvatar;
    }

    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }

    public List<AARecDetailBean.PayListBean> getPaidPayList() {
        return paidPayList;
    }

    public void setPaidPayList(List<AARecDetailBean.PayListBean> paidPayList) {
        this.paidPayList = paidPayList;
    }

    public List<AARecDetailBean.PayListBean> getUnpaidPayList() {
        return unpaidPayList;
    }

    public void setUnpaidPayList(List<AARecDetailBean.PayListBean> unpaidPayList) {
        this.unpaidPayList = unpaidPayList;
    }
}
