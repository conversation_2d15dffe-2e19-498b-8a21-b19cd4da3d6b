package om.rrtx.mobile.paymentmodule.bean

class AppBundleListBean(val appList: ArrayList<AppBundleBean>)
class AppBundleBean(val appCode: String,
    val appName: String,
    val appIcon: String)

class BundleTypeListBean(
    val bundleTypeList: ArrayList<BundleTypeBean>
)

class BundleTypeBean(
    val bundleType: String,
    val bundleName: String,
)


data class BundleListBean(
    val countId: String,
    val current: Int,
    val maxLimit: String,
    val optimizeCountSql: Boolean,
    val orders: List<Any>,
    val pages: Int,
    val records: ArrayList<BundleBean>,
    val searchCount: Boolean,
    val size: Int,
    val total: Int
)

data class BundleBean(
    val bundleType: String,
    val bundlePlan: String,
    val appName: String = "",
    val bundleId: String = "",
    val bundlePlanDesc: String = "",
    val bundleTypeDesc: String = "",
    val currency: String = "",
    val price: String = "",
    val pricePlanCode: String = "",
)


data class AirtimeListBean(
    val airtimeList: ArrayList<AirtimeBean>
)

data class AirtimeBean(
    val airtimePlan: String,
    val airtimePlanCode: String,
    val currency: String,
    val price: String
)