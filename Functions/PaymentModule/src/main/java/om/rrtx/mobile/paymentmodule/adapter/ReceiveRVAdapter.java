package om.rrtx.mobile.paymentmodule.adapter;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.bean.PayListBean;
import om.rrtx.mobile.rrtxcommon1.base.BaseHolder;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;

/**
 * <AUTHOR>
 * 收到AA的适配器
 */
public class ReceiveRVAdapter extends RecyclerView.Adapter<BaseHolder> {

    private Context mContext;
    private RVAdapterItemClickListener<PayListBean.RecordsBean> mClickListener;
    private List<PayListBean.RecordsBean> mBeanLists;

    public ReceiveRVAdapter(Context context) {
        mContext = context;
        mBeanLists = new ArrayList<>();
    }

    @NonNull
    @Override
    public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.payment_item_receive, parent, false);
        return new BaseHolder(rootView);
    }

    @Override
    public void onBindViewHolder(@NonNull BaseHolder holder, int position) {
        PayListBean.RecordsBean recordsBean = mBeanLists.get(position);

        //总金额
        TextView totalMoney = holder.getView(R.id.totalMoney);
        totalMoney.setText(StringUtils.formatAmount(recordsBean.getTransAmt()));

        //币种
        TextView usd = holder.getView(R.id.usd);
        usd.setText(CurrencyUtils.setCurrency(mContext, recordsBean.getCurrency()));

        //转账人
        TextView spitFrom = holder.getView(R.id.spitFrom);
        String spitFromStr = mContext.getString(R.string.aa_label_bill_from) + ":";
        spitFrom.setText(spitFromStr);
        TextView spitFromTv = holder.getView(R.id.spitFromTv);
        spitFromTv.setText(recordsBean.getRecCstName());


        //留言
        TextView remark = holder.getView(R.id.remark);
        String remarkStr = mContext.getString(R.string.common_label_remark) + ":";
        remark.setText(remarkStr);
        TextView remarkTv = holder.getView(R.id.remarkTv);
        remarkTv.setText(recordsBean.getRemark());

        //时间
        TextView date = holder.getView(R.id.data);
        String dateStr = mContext.getString(R.string.aa_label_time) + ":";
        date.setText(dateStr);
        TextView dateTime = holder.getView(R.id.dataTv);
        dateTime.setText(recordsBean.getTradeTime());

        //状态
        TextView statusTv = holder.getView(R.id.statusTv);

        //背景
        ImageView bgIv = holder.getView(R.id.bgIv);

        String orderStatus = recordsBean.getOrderStatus();
        Bitmap bitmap = null;
        if (TextUtils.equals(PaymentConstants.AAOrderStatus.PENDING, orderStatus) ||
                TextUtils.equals(PaymentConstants.AAOrderStatus.PENDINGOR, orderStatus)) {
            statusTv.setBackgroundResource(R.drawable.payment_item_top_yellow);

            totalMoney.setTextColor(mContext.getResources().getColor(R.color.color_E9C527));
            usd.setTextColor(mContext.getResources().getColor(R.color.color_E9C527));

            statusTv.setText(R.string.aa_label_pending);
            statusTv.setTextColor(mContext.getResources().getColor(R.color.color_212121));

            bitmap = readBitMap(mContext, R.drawable.payment_bg_receive_pending);
            bgIv.setImageBitmap(bitmap);
        } else if (TextUtils.equals(PaymentConstants.AAOrderStatus.COMPLETE, orderStatus)) {
            statusTv.setBackgroundResource(R.drawable.payment_item_top_blue);

            totalMoney.setTextColor(mContext.getResources().getColor(R.color.common_ye_F3881E));
            usd.setTextColor(mContext.getResources().getColor(R.color.common_ye_F3881E));

            statusTv.setText(R.string.aa_label_paid);
            statusTv.setTextColor(mContext.getResources().getColor(R.color.color_FFFFFF));

            bitmap = readBitMap(mContext, R.drawable.payment_bg_receive_paid);
            bgIv.setImageBitmap(bitmap);
        } else if (TextUtils.equals(PaymentConstants.AAOrderStatus.CANCELLED, orderStatus)) {
            statusTv.setBackgroundResource(R.drawable.payment_item_top_gray);

            totalMoney.setTextColor(mContext.getResources().getColor(R.color.color_999999));
            usd.setTextColor(mContext.getResources().getColor(R.color.color_999999));

            statusTv.setText(R.string.aa_label_cancelled);
            statusTv.setTextColor(mContext.getResources().getColor(R.color.color_212121));

            bitmap = readBitMap(mContext, R.drawable.payment_bg_receive_cancelled);
            bgIv.setImageBitmap(bitmap);
        }

        holder.itemView.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                if (mClickListener != null) {
                    mClickListener.itemClickListener(recordsBean, position);
                }
            }
        });
    }


    /**
     * 以最省内存的方式读取本地资源的图片
     *
     * @param context 上下文
     * @param resId   资源Id
     * @return 返回bitmap
     */
    public static Bitmap readBitMap(Context context, int resId) {
        BitmapFactory.Options opt = new BitmapFactory.Options();
        //压缩编码`
        opt.inPreferredConfig = Bitmap.Config.RGB_565;
        //下面两个过时了，但没影响
        opt.inPurgeable = true;
        opt.inInputShareable = true;

        InputStream is = context.getResources().openRawResource(resId);
        return BitmapFactory.decodeStream(is, null, opt);
    }

    @Override
    public int getItemCount() {
        return mBeanLists != null ? mBeanLists.size() : 0;
    }

    public void setClickListener(RVAdapterItemClickListener<PayListBean.RecordsBean> clickListener) {
        mClickListener = clickListener;
    }

    public void setData(List<PayListBean.RecordsBean> records) {
        mBeanLists = records;
        notifyDataSetChanged();
    }

    public void addData(List<PayListBean.RecordsBean> recordsBeans) {
        mBeanLists.addAll(recordsBeans);
        notifyDataSetChanged();
    }
}
