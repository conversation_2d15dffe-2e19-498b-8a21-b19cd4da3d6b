package om.rrtx.mobile.paymentmodule.activity

import android.content.Context
import android.content.Intent
import android.text.Editable
import android.text.InputFilter
import android.text.TextUtils
import android.util.Log
import android.view.View
import androidx.lifecycle.ViewModelProvider
import com.google.gson.Gson
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.payment_activity_billcode_house.address_ed
import kotlinx.android.synthetic.main.payment_activity_billcode_house.currency_tv
import kotlinx.android.synthetic.main.payment_activity_billcode_house.first_ed
import kotlinx.android.synthetic.main.payment_activity_billcode_house.last_ed
import kotlinx.android.synthetic.main.payment_activity_billcode_house.locationBg_view
import kotlinx.android.synthetic.main.payment_activity_billcode_house.locationTv
import kotlinx.android.synthetic.main.payment_activity_billcode_house.middle_ed
import kotlinx.android.synthetic.main.payment_activity_billcode_house.moneyTv
import kotlinx.android.synthetic.main.payment_activity_billcode_house.nextTv
import kotlinx.android.synthetic.main.payment_activity_billcode_house.reason_ed
import kotlinx.android.synthetic.main.payment_base_title.backIv
import kotlinx.android.synthetic.main.payment_base_title.leftBg
import kotlinx.android.synthetic.main.payment_base_title.statusView
import kotlinx.android.synthetic.main.payment_base_title.titleTv
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.functioncommon.activity.ComResultActivity
import om.rrtx.mobile.functioncommon.bean.BillCodeBean
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean
import om.rrtx.mobile.functioncommon.bean.ComResultBean
import om.rrtx.mobile.functioncommon.bean.OrderInfoBean
import om.rrtx.mobile.functioncommon.bean.PaymentSuccessBean
import om.rrtx.mobile.functioncommon.callback.CashierCallBack1
import om.rrtx.mobile.functioncommon.dialog.CommonMoreDataDialog
import om.rrtx.mobile.functioncommon.dialog.CommonCurrencyDialog
import om.rrtx.mobile.functioncommon.utils.CashierManager1
import om.rrtx.mobile.paymentmodule.R
import om.rrtx.mobile.paymentmodule.databinding.PaymentActivityBillcodeHouseBinding
import om.rrtx.mobile.paymentmodule.viewModel.BillPayWM
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.EditInputFilter
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity
import om.rrtx.mobile.rrtxcommon1.bean.AppDictListBean
import om.rrtx.mobile.rrtxcommon1.utils.BigDecimalUtils
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.SoftKeyBoardListener
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.TextChangeEndListener
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil
import java.math.BigDecimal

/**房产**/
class BillCodePayToHouseActivity : BaseActivity<PaymentActivityBillcodeHouseBinding>() {

    lateinit var mVM: BillPayWM
    lateinit var mBean: BillCodeBean
    var curCurrency: String = "ZWG"
    var termNumber: String = ""
    private lateinit var appDictCodeList: List<AppDictListBean.AppDictBean>
    override fun createContentView() = R.layout.payment_activity_billcode_house

    override fun doGetExtra() {
        val json = intent.getStringExtra(CommonConstants.Parameter.JSON_BEAN)
        if (StringUtils.isValidString(json)) {
            mBean = Gson().fromJson(json, BillCodeBean::class.java)
        }
    }

    override fun initWorkspaceAction() {
        mVM = ViewModelProvider(this).get(BillPayWM::class.java)
        mVM.orderInfoBeanSuccess.observe(this) {
            showCashier(it)
        }
        mVM.dictCodeListResult.observe(this) {
            appDictCodeList = it.appDictCodeList
        }
    }

    private fun showCashier(mOrderInfoBean: OrderInfoBean) {
        var payType = CommonConstants.CashierPaymentType.MERCHANT_CODE
        var transType = CommonConstants.TransType.Bill_PAY
        var orderInfo = mOrderInfoBean.commodityRemark
        var amt = moneyTv.text.toString()
        var owenName =
            first_ed.text.toString() + " " + middle_ed.text.toString() + " " + last_ed.text.toString()

        val cashierOrderInfoBean = CashierOrderInfoBean.Builder()
            .setPayType(payType)
            .setTransType(transType)
            .setBillSubType(mBean.billerCategory)
            .setOrderSource(CommonConstants.OrderSource.EXTERNALORDER)
            .setOrderInfo(orderInfo)
            .setMerNo(mBean.recMerNo)
            .setMerName(address_ed.text.toString())
            .setBankNo(reason_ed.text.toString())
            .setBankName(owenName)
            .setCurrency(mOrderInfoBean.currency)
            .setOrderAmt(amt)
            .setOrderNo(mOrderInfoBean.trxOrderNo)
            .setAfterDisCountAmt("")
            .setIsFixMethod(true)
            .builder()
        CashierManager1(this, Gson().toJson(cashierOrderInfoBean), object : CashierCallBack1 {
            override fun paymentSuccess(dataJson: String) {
                //支付成功页面
                Log.e("TAG", "paymentSuccess: $dataJson")
                val fromJson = Gson().fromJson(dataJson, PaymentSuccessBean::class.java)
                val comResultBean = ComResultBean(
                    getString(R.string.bill_Payment),
                    s2 = fromJson.currency + " " + mOrderInfoBean.amt,//fromJson.paymentAmt,
                    flag = BaseConstants.JumpFlag.PAY_MERCHANT_CODE
                )

                ComResultActivity.jump(
                    mContext,
                    comResultBean
                )

            }

            override fun paymentFailed(message: String) {

            }

            override fun leftClick() {
                //finish()
            }

            override fun rightClick() {
                finish()
            }

        }).showCashierDialog()
    }

    override fun initView() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true, 0.2f)
            .init()
        dataBinding.includeTitle.apply {
            titleTv.setText(R.string.bill_Payment)
            titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
            backIv.setBackgroundResource(R.drawable.common_ic_back_black)
            statusView.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
            titleTv.setBackgroundColor(resources.getColor(R.color.color_FFFFFF))
        }
        curCurrency = CurrencyUtils.setCurrency(mContext,"ZWG")
        currency_tv.text = curCurrency

        mVM.requestDictCodeList()

        val filters = arrayOf<InputFilter>(EditInputFilter(mContext))
        moneyTv.filters = filters
        //键盘的监听
        SoftKeyBoardListener.setListener(
            this,
            object : SoftKeyBoardListener.OnSoftKeyBoardChangeListener {
                override fun keyBoardShow(height: Int) {
                    //键盘显示
                    Log.e("done", "keyBoardShow: ")
                }

                override fun keyBoardHide(height: Int) {
                    Log.e("done", "keyBoardHide: ")
                    //键盘隐藏的时候设置相应的金额
                    var mAmountStr = dataBinding.moneyTv.text.toString();
                    if (!TextUtils.isEmpty(mAmountStr)) {
                        mAmountStr = BigDecimalUtils.getStandardAmount(
                            mAmountStr,
                            2,
                            BigDecimal.ROUND_HALF_UP
                        );
                        moneyTv.setText(mAmountStr);
                        moneyTv.setSelection(mAmountStr.length);
                    }
                }
            })
    }

    override fun initListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    currency_tv -> {
                        val mCommonCurrencyDialog =
                            CommonCurrencyDialog(
                                this@BillCodePayToHouseActivity
                            )
                        mCommonCurrencyDialog.setOnCurrencyClickListener(CommonCurrencyDialog.OnCurrencyClickListener { currency -> // 赋值货币符号
                            curCurrency = currency
                            currency_tv.text = curCurrency
                            moneyTv.setText("")
                        })
                        mCommonCurrencyDialog.show()
                    }

                    locationBg_view -> {
                        val entries = arrayListOf<String>()
                        for (bean in appDictCodeList) {
                            entries.add(bean.dictLabel)
                        }
                        val mOldCurrencyDialog =
                            CommonMoreDataDialog(
                                this@BillCodePayToHouseActivity, "1", entries
                            )
                        mOldCurrencyDialog.setOnCurrencyClickListener(CommonMoreDataDialog.OnCurrencyClickListener { position, value ->
                            locationTv.text = value
                            termNumber = appDictCodeList[position].dictValue
                        })
                        mOldCurrencyDialog.show()
                    }

                    leftBg -> {
                        onBackPressed()
                    }

                    nextTv -> {
                        var mAmountStr = moneyTv.text.toString()
                        mAmountStr = BigDecimalUtils.getStandardAmount(
                            mAmountStr,
                            2,
                            BigDecimal.ROUND_HALF_UP
                        )
                        moneyTv.setText(mAmountStr)
                        moneyTv.setSelection(mAmountStr.length)
                        if (TextUtils.equals(mAmountStr, "0") || TextUtils.equals(
                                mAmountStr,
                                "0.00"
                            )
                        ) {
                            ToastUtil.show(
                                mContext,
                                resources.getString(R.string.payment_tip_amount_must_greater) + mAmountStr
                            )
                            return
                        }

                        mVM.requestMerchantOrder(
                            mBean.billerCategory,
                            mBean.recMerNo,
                            curCurrency,
                            moneyTv.text.toString(),
                            "",
                            first_ed.text.toString(),
                            middle_ed.text.toString(),
                            last_ed.text.toString(),
                            "",
                            "",
                            termNumber,
                            address_ed.text.toString(),
                            reason_ed.text.toString()
                        )
                    }
                }
            }

        }.apply {
            locationBg_view.setOnClickListener(this)
            //currency_tv.setOnClickListener(this)
            nextTv.setOnClickListener(this)
            leftBg.setOnClickListener(this)
        }

        moneyTv.onFocusChangeListener = View.OnFocusChangeListener { view, b ->
            if (!b) {
                var mAmountStr = dataBinding.moneyTv.text.toString()
                if (!TextUtils.isEmpty(mAmountStr)) {
                    mAmountStr =
                        BigDecimalUtils.getStandardAmount(mAmountStr, 2, BigDecimal.ROUND_HALF_UP)
                    moneyTv.setText(mAmountStr)
                    moneyTv.setSelection(mAmountStr.length)
                }
            }
        }

        moneyTv.addTextChangedListener(object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                orderIsFull()
            }
        })
        first_ed.addTextChangedListener(object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                orderIsFull()
            }
        })
        middle_ed.addTextChangedListener(object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                orderIsFull()
            }
        })
        last_ed.addTextChangedListener(object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                orderIsFull()
            }
        })
        address_ed.addTextChangedListener(object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                orderIsFull()
            }
        })
        reason_ed.addTextChangedListener(object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                orderIsFull()
            }
        })
    }

    fun orderIsFull() {
        var isMoney = moneyTv.text.trim()
            .isNotEmpty() && (moneyTv.text.toString() != "0.00" || moneyTv.text.toString() != "0")
        if (isMoney && first_ed.text.isNotEmpty() && last_ed.text.isNotEmpty() && locationTv.text.isNotEmpty() && address_ed.text.isNotEmpty() && reason_ed.text.isNotEmpty()
        ) {
            nextTv.isEnabled = true
            nextTv.setBackgroundResource(R.drawable.common_usable_btn)
        } else {
            nextTv.isEnabled = false
            nextTv.setBackgroundResource(R.drawable.common_unusable_btn)
        }
    }

    companion object {
        fun jump(context: Context, bean: BillCodeBean) {
            val intent = Intent(context, BillCodePayToHouseActivity::class.java)
            intent.putExtra(CommonConstants.Parameter.JSON_BEAN, Gson().toJson(bean))
            context.startActivity(intent)
        }
    }
}