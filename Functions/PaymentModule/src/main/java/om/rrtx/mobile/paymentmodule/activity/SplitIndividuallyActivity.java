package om.rrtx.mobile.paymentmodule.activity;

import android.content.Context;
import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import java.math.BigDecimal;
import java.util.List;

import butterknife.BindView;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.dialog.CommonCurrencyDialog;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.paymentmodule.adapter.AddAmtConRVAdapter;
import om.rrtx.mobile.paymentmodule.bean.ByMobileBean;
import om.rrtx.mobile.paymentmodule.bean.ContactBean;
import om.rrtx.mobile.paymentmodule.utils.LogUtil;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.BigDecimalUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SoftKeyBoardListener;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;

/**
 * <AUTHOR>
 * aa非等额账单
 */
public class SplitIndividuallyActivity extends BaseSuperActivity
        implements AddAmtConRVAdapter.AAItemCallBack {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.contentRv)
    RecyclerView mContentRv;
    @BindView(R2.id.totalTv)
    TextView mTotalTv;
    @BindView(R2.id.reset)
    TextView mReset;
    @BindView(R2.id.confirmTv)
    TextView mConfirmTv;
    @BindView(R2.id.currencyTv)
    TextView mCurrencyTv;
    @BindView(R2.id.usdTv)
    TextView mUsdTv;
    @BindView(R2.id.currencyLl)
    LinearLayout mCurrencyLl;
    private String mSelectJson;
    private ContactBean mContactBean;
    private AddAmtConRVAdapter mAddAmtConRVAdapter;
    private List<ByMobileBean> mSelectList;
    private int selectIndex = -1;

    CommonCurrencyDialog mCommonCurrencyDialog;
    private String mCurrencyStr = "USD";

    public static void jumpSplitIndividually(Context context, String selectJson, String currency) {
        Intent intent = new Intent(context, SplitIndividuallyActivity.class);
        intent.putExtra(PaymentConstants.Transmit.DETAILSBEAN, selectJson);
        intent.putExtra(CommonConstants.Transmit.CURRENCY, currency);
        context.startActivity(intent);
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        mSelectJson = getIntent().getStringExtra(PaymentConstants.Transmit.DETAILSBEAN);
        mCurrencyStr = getIntent().getStringExtra(CommonConstants.Transmit.CURRENCY);
        Log.e("SplitIndividuallyActivity>>>", "zfw doGetExtra>>> mCurrencyStr:" + mCurrencyStr);
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    protected int createContentView() {
        return R.layout.payment_activity_split_individually;
    }

    @Override
    protected BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.payment_ic_back);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setText(R.string.bill_title_split_individually);
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));

        mTotalTv.setText(getResources().getString(R.string.common_label_total_amt) + ":" +CurrencyUtils.setCurrency(mContext, mCurrencyStr)+" "+ getResources().getString(R.string.common_label_0_00));
        mUsdTv.setText(CurrencyUtils.setCurrency(mContext, mCurrencyStr));
        mCurrencyTv.setText(CurrencyUtils.setCurrency(mContext, mCurrencyStr));
    }

    @Override
    public void initDate() {
        super.initDate();
        mContactBean = new Gson().fromJson(mSelectJson, ContactBean.class);
        mSelectList = mContactBean.getContactList();

        mAddAmtConRVAdapter = new AddAmtConRVAdapter(mContext);
        mContentRv.setLayoutManager(new LinearLayoutManager(mContext));
        mContentRv.setAdapter(mAddAmtConRVAdapter);
        mAddAmtConRVAdapter.setData(mSelectList);

        //设置总金额
        String totalAmt = calculationAmt(mSelectList);
        String totalStr = getResources().getString(R.string.common_label_total_amt) + ":" +CurrencyUtils.setCurrency(mContext, mCurrencyStr)+" "+ StringUtils.formatAmount(totalAmt);
        mTotalTv.setText(totalStr);

        //是否显示底部按钮
        if (isShowBottom(mSelectList)) {
            mConfirmTv.setBackgroundResource(R.drawable.payment_drawable_btn_select);
            mConfirmTv.setEnabled(true);
        } else {
            mConfirmTv.setBackgroundResource(R.drawable.payment_drawable_btn_unselect);
            mConfirmTv.setEnabled(false);
        }
    }

    @Override
    public void initListener() {
        super.initListener();

        mAddAmtConRVAdapter.setItemCallBack(this);

        SoftKeyBoardListener.setListener(this, new SoftKeyBoardListener.OnSoftKeyBoardChangeListener() {
            @Override
            public void keyBoardShow(int height) {

            }

            @Override
            public void keyBoardHide(int height) {
                LogUtil.e("done", "keyBoardHide: ");
                //键盘隐藏的时候设置相应的金额
                ByMobileBean byMobileBean = mSelectList.get(selectIndex);
                String amount = byMobileBean.getAmount();
                if (!TextUtils.isEmpty(amount)) {
                    amount = BigDecimalUtils.getStandardAmount(amount, 2, BigDecimal.ROUND_HALF_UP);
                    byMobileBean.setAmount(amount);
                    mAddAmtConRVAdapter.notifyItemChanged(selectIndex);
                }
            }
        });

        mReset.setOnClickListener(splitCustom);
        mLeftBg.setOnClickListener(splitCustom);
        mConfirmTv.setOnClickListener(splitCustom);
        mCurrencyLl.setOnClickListener(splitCustom);
    }

    private CustomClickListener splitCustom = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.reset) {
                //清除金额
                clearAmt(mSelectList);
                mAddAmtConRVAdapter.notifyDataSetChanged();
                String totalStr = getResources().getString(R.string.common_label_total_amt) + ":"+CurrencyUtils.setCurrency(mContext, mCurrencyStr)+" " + getResources().getString(R.string.common_label_0_00);
                mTotalTv.setText(totalStr);
                mConfirmTv.setBackgroundResource(R.drawable.payment_drawable_btn_unselect);
                mConfirmTv.setEnabled(false);
            } else if (view.getId() == R.id.leftBg) {
                finish();
            } else if (view.getId() == R.id.confirmTv) {
                Intent intent = new Intent(mContext, AASpiltBillActivity.class);
                ContactBean contactBean = new ContactBean();
                contactBean.setContactList(mSelectList);
                intent.putExtra(PaymentConstants.Transmit.DETAILSBEAN, new Gson().toJson(contactBean));
                intent.putExtra(CommonConstants.Transmit.CURRENCY, mCurrencyStr);
                intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                startActivity(intent);
            } else if (view.getId() == R.id.currencyLl) {
                mCommonCurrencyDialog = new CommonCurrencyDialog(SplitIndividuallyActivity.this);
                mCommonCurrencyDialog.setOnCurrencyClickListener(new CommonCurrencyDialog.OnCurrencyClickListener() {
                    @Override
                    public void onCurrencyClick(String currency) {
                        // 赋值货币符号
                        mCurrencyStr = currency;
                        mUsdTv.setText(currency);
                        mCurrencyTv.setText(currency);
                        if (mCommonCurrencyDialog != null) {
                            mCommonCurrencyDialog = null;
                        }
                    }
                });
                if (mCommonCurrencyDialog != null) {
                    mCommonCurrencyDialog.show();
                }
            }
        }
    };

    @Override
    public void afterTextChanged(ByMobileBean byMobileBean, int position, Editable s) {
        selectIndex = position;
        byMobileBean.setAmount(s.toString());

        //设置总金额
        String totalAmt = calculationAmt(mSelectList);
        String totalStr = getResources().getString(R.string.common_label_total_amt) + ":" +CurrencyUtils.setCurrency(mContext, mCurrencyStr)+" "+ StringUtils.formatAmount(totalAmt);
        mTotalTv.setText(totalStr);

        //设置底部按钮是否显示
        if (isShowBottom(mSelectList)) {
            mConfirmTv.setBackgroundResource(R.drawable.payment_drawable_btn_select);
            mConfirmTv.setEnabled(true);
        } else {
            mConfirmTv.setBackgroundResource(R.drawable.payment_drawable_btn_unselect);
            mConfirmTv.setEnabled(false);
        }
    }

    @Override
    public void focusChange(boolean hasFocus, int position) {
        if (hasFocus) {
            selectIndex = position;
        }
    }

    private boolean isShowBottom(List<ByMobileBean> selectList) {
        for (int i = 0; i < selectList.size(); i++) {
            ByMobileBean byMobileBean = selectList.get(i);
            String amount = byMobileBean.getAmount();
            if (TextUtils.isEmpty(amount)) {
                return false;
            }

            BigDecimal amtBg = new BigDecimal(amount);
            if (amtBg.compareTo(BigDecimal.ZERO) == 0) {
                return false;
            }
        }
        return true;
    }

    /**
     * 计算总金额
     *
     * @param list 列表数据
     * @return 总金额
     */
    public String calculationAmt(List<ByMobileBean> list) {
        String amountTotal = "0";

        for (int i = 0; i < list.size(); i++) {
            ByMobileBean byMobileBean = list.get(i);
            String amount = byMobileBean.getAmount();

            BigDecimal totalBd = new BigDecimal(amountTotal);
            if (TextUtils.isEmpty(amount)) {
                amount = "0";
            }
            BigDecimal currentBd = new BigDecimal(amount);

            amountTotal = totalBd.add(currentBd).setScale(2, BigDecimal.ROUND_HALF_UP).toString();
        }

        return amountTotal;
    }

    public void clearAmt(List<ByMobileBean> list) {
        for (int i = 0; i < list.size(); i++) {
            ByMobileBean byMobileBean = list.get(i);
            byMobileBean.setAmount("");
        }
    }
}
