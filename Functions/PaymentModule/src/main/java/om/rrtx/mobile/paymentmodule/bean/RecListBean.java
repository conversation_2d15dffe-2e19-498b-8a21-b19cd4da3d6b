package om.rrtx.mobile.paymentmodule.bean;

import java.util.List;

/**
 * <AUTHOR>
 * 发布AA列表
 */
public class RecListBean {


    /**
     * records : [{"orderNo":"8109332787812646984","totalAmt":"3.00","recAmt":"2.00","actRecAmt":"0.00","totalOrderNumber":2,"recOrderNumber":0,"tradeTime":" 28 Feb 2020","currencyType":"USD","splitType":"","orderStatus":"10"},{"orderNo":"8109332498237898816","totalAmt":"3.00","recAmt":"2.00","actRecAmt":"0.00","totalOrderNumber":2,"recOrderNumber":0,"tradeTime":" 28 Feb 2020","currencyType":"USD","splitType":"","orderStatus":"10"},{"orderNo":"8109330289701961782","totalAmt":"3.00","recAmt":"2.00","actRecAmt":"0.00","totalOrderNumber":2,"recOrderNumber":0,"tradeTime":" 28 Feb 2020","currencyType":"USD","splitType":"","orderStatus":"10"},{"orderNo":"8109324577194127402","totalAmt":"3.00","recAmt":"2.00","actRecAmt":"0.00","totalOrderNumber":2,"recOrderNumber":0,"tradeTime":" 28 Feb 2020","currencyType":"USD","splitType":"","orderStatus":"10"},{"orderNo":"8109322739468218400","totalAmt":"3.00","recAmt":"2.00","actRecAmt":"0.00","totalOrderNumber":2,"recOrderNumber":0,"tradeTime":" 28 Feb 2020","currencyType":"USD","splitType":"","orderStatus":"10"},{"orderNo":"8109320169819475990","totalAmt":"3.00","recAmt":"2.00","actRecAmt":"0.00","totalOrderNumber":2,"recOrderNumber":0,"tradeTime":" 28 Feb 2020","currencyType":"USD","splitType":"","orderStatus":"10"},{"orderNo":"8109316938561228812","totalAmt":"3.00","recAmt":"2.00","actRecAmt":"0.00","totalOrderNumber":2,"recOrderNumber":0,"tradeTime":" 28 Feb 2020","currencyType":"USD","splitType":"","orderStatus":"10"},{"orderNo":"8109311267778330626","totalAmt":"3.00","recAmt":"2.00","actRecAmt":"0.00","totalOrderNumber":2,"recOrderNumber":0,"tradeTime":" 28 Feb 2020","currencyType":"USD","splitType":"","orderStatus":"10"},{"orderNo":"8109220540251640359","totalAmt":"3.00","recAmt":"2.00","actRecAmt":"0.00","totalOrderNumber":2,"recOrderNumber":0,"tradeTime":" 28 Feb 2020","currencyType":"USD","splitType":"","orderStatus":"10"},{"orderNo":"8108779717005410713","totalAmt":"3.00","recAmt":"2.00","actRecAmt":"0.00","totalOrderNumber":2,"recOrderNumber":0,"tradeTime":" 28 Feb 2020","currencyType":"USD","splitType":"","orderStatus":"10"}]
     * total : 12
     * size : 10
     * current : 1
     * orders : []
     * searchCount : true
     * pages : 2
     */

    private int total;
    private int size;
    private int current;
    private boolean searchCount;
    private int pages;
    private List<RecordsBean> records;

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getCurrent() {
        return current;
    }

    public void setCurrent(int current) {
        this.current = current;
    }

    public boolean isSearchCount() {
        return searchCount;
    }

    public void setSearchCount(boolean searchCount) {
        this.searchCount = searchCount;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    public List<RecordsBean> getRecords() {
        return records;
    }

    public void setRecords(List<RecordsBean> records) {
        this.records = records;
    }

    public static class RecordsBean {
        /**
         * orderNo : 8109332787812646984
         * totalAmt : 3.00
         * recAmt : 2.00
         * actRecAmt : 0.00
         * totalOrderNumber : 2
         * recOrderNumber : 0
         * tradeTime :  28 Feb 2020
         * currencyType : USD
         * splitType :
         * orderStatus : 10
         */

        private String orderNo;
        private String totalAmt;
        private String recAmt;
        private String actRecAmt;
        private int totalOrderNumber;
        private int recOrderNumber;
        private String tradeTime;
        private String currencyType;
        private String splitType;
        private String orderStatus;

        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public String getTotalAmt() {
            return totalAmt;
        }

        public void setTotalAmt(String totalAmt) {
            this.totalAmt = totalAmt;
        }

        public String getRecAmt() {
            return recAmt;
        }

        public void setRecAmt(String recAmt) {
            this.recAmt = recAmt;
        }

        public String getActRecAmt() {
            return actRecAmt;
        }

        public void setActRecAmt(String actRecAmt) {
            this.actRecAmt = actRecAmt;
        }

        public int getTotalOrderNumber() {
            return totalOrderNumber;
        }

        public void setTotalOrderNumber(int totalOrderNumber) {
            this.totalOrderNumber = totalOrderNumber;
        }

        public int getRecOrderNumber() {
            return recOrderNumber;
        }

        public void setRecOrderNumber(int recOrderNumber) {
            this.recOrderNumber = recOrderNumber;
        }

        public String getTradeTime() {
            return tradeTime;
        }

        public void setTradeTime(String tradeTime) {
            this.tradeTime = tradeTime;
        }

        public String getCurrencyType() {
            return currencyType;
        }

        public void setCurrencyType(String currencyType) {
            this.currencyType = currencyType;
        }

        public String getSplitType() {
            return splitType;
        }

        public void setSplitType(String splitType) {
            this.splitType = splitType;
        }

        public String getOrderStatus() {
            return orderStatus;
        }

        public void setOrderStatus(String orderStatus) {
            this.orderStatus = orderStatus;
        }
    }
}
