package om.rrtx.mobile.paymentmodule.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import om.rrtx.mobile.paymentmodule.bean.BundleBean
import om.rrtx.mobile.paymentmodule.databinding.ItemDatabundleOptionBinding
import om.rrtx.mobile.rrtxcommon1.base.BaseAdapterCallback
import om.rrtx.mobile.rrtxcommon1.base.BaseAdapterDataCallback
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener

class DataBundleOptionAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    lateinit var callback: BaseAdapterCallback
    lateinit var dataCallback: BaseAdapterDataCallback<BundleBean>

    lateinit var context: Context

    private var data = arrayListOf<BundleBean>()

    var selectPosition = -1

    fun setNewData(list: ArrayList<BundleBean>) {
        data = list
        notifyDataSetChanged()
    }
    fun addData(list: ArrayList<BundleBean>) {
        data.addAll(list)
        notifyDataSetChanged()
    }

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        context = recyclerView.context
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(context)
        val binding = ItemDatabundleOptionBinding.inflate(
            inflater,
            parent,
            false
        )
        return object : RecyclerView.ViewHolder(binding.root) {}
    }

    override fun getItemCount() = data.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val binding =
            DataBindingUtil.getBinding<ItemDatabundleOptionBinding>(holder.itemView) ?: return
        val bean = data[position]
        binding.titleTv.text = bean.bundlePlan
        binding.textTv.text = bean.currency + " " + bean.price

        val isSelected = position == selectPosition
        binding.root.isSelected = isSelected
        binding.titleTv.isSelected = isSelected

        binding.root.setOnClickListener(object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                selectPosition = position
                notifyDataSetChanged()
                if (this@DataBundleOptionAdapter::callback.isInitialized) {
                    callback.callBack(position)
                }
                if (this@DataBundleOptionAdapter::dataCallback.isInitialized) {
                    dataCallback.callBack(position, bean)
                }
            }
        })

    }

}