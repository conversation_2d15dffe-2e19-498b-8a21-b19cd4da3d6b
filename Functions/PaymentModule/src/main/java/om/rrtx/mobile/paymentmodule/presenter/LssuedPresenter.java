package om.rrtx.mobile.paymentmodule.presenter;

import android.content.Context;
import android.text.TextUtils;

import java.util.List;

import om.rrtx.mobile.paymentmodule.bean.PayerInfosBean;
import om.rrtx.mobile.paymentmodule.bean.PubBean;
import om.rrtx.mobile.paymentmodule.bean.RecListBean;
import om.rrtx.mobile.paymentmodule.model.PaymentModel;
import om.rrtx.mobile.paymentmodule.view.AASplitBillView;
import om.rrtx.mobile.paymentmodule.view.LssuedView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * AA订单页面的P层
 */
public class LssuedPresenter extends BasePresenter<LssuedView> {

    private final PaymentModel mPaymentModel;
    private Context mContext;

    public LssuedPresenter(Context context) {
        mPaymentModel = new PaymentModel();
        mContext = context;
    }

    /**
     * 发起的AA列表
     */
    public void requestRecList(int pagerNum, int pagerSize) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestRecList(userId,pagerNum, pagerSize,
                    new BaseObserver<RecListBean>(mContext) {
                        @Override
                        public void requestSuccess(RecListBean sResData) {
                            if (getView() != null) {
                                if (getView() != null) {
                                    getView().recListSuccess(sResData);
                                }
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
        }else {
            mPaymentModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mPaymentModel.requestRecList(userId,pagerNum, pagerSize,
                            new BaseObserver<RecListBean>(mContext) {
                                @Override
                                public void requestSuccess(RecListBean sResData) {
                                    if (getView() != null) {
                                        if (getView() != null) {
                                            getView().recListSuccess(sResData);
                                        }
                                    }
                                }

                                @Override
                                public void requestFail(String sResMsg) {
                                    if (getView() != null) {
                                        getView().requestFail(sResMsg);
                                    }
                                }
                            });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    /**
     * 发起的AA列表
     */
    public void requestMoreRecList(int pagerNum, int pagerSize) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestRecList(userId,pagerNum, pagerSize,
                    new BaseObserver<RecListBean>(mContext) {
                        @Override
                        public void requestSuccess(RecListBean sResData) {
                            if (getView() != null) {
                                if (getView() != null) {
                                    getView().recListMoreSuccess(sResData);
                                }
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
        }else {
            mPaymentModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mPaymentModel.requestRecList(userId,pagerNum, pagerSize,
                            new BaseObserver<RecListBean>(mContext) {
                                @Override
                                public void requestSuccess(RecListBean sResData) {
                                    if (getView() != null) {
                                        if (getView() != null) {
                                            getView().recListMoreSuccess(sResData);
                                        }
                                    }
                                }

                                @Override
                                public void requestFail(String sResMsg) {
                                    if (getView() != null) {
                                        getView().requestFail(sResMsg);
                                    }
                                }
                            });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }
}
