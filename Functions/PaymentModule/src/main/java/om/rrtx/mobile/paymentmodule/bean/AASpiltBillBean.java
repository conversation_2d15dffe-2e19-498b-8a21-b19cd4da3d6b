package om.rrtx.mobile.paymentmodule.bean;

import java.util.List;

/**
 * <AUTHOR>
 */
public class AASpiltBillBean {

    /**
     * appVersion : 1
     * payerInfos : [{"mobile":"+6017731140281","transAmt":"1.00"},{"mobile":"+60123456789000","transAmt":"1.00"}]
     * deviceOS : 1
     * language : en
     * splitNumber : 2
     * systemVersion : 9
     * splitType : 0
     * platform : 1
     * timeStamp : 1582798992421
     * random : 273ced3af04a99c7be32ca8b8f0d49b51582798992422
     * totalAmt : 3.00
     * series : Android SDK built for x86
     * singleCode : 273ced3af04a99c7be32ca8b8f0d49b5
     * recAmt : 2.00
     * brand : google
     * signMethod : 01
     */

    private String splitNumber;
    private String splitType;
    private String totalAmt;
    private String recAmt;
    private String remark;
    private String userId;
    private String currency;
    private List<PayerInfosBean> payerInfos;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getSplitNumber() {
        return splitNumber;
    }

    public void setSplitNumber(String splitNumber) {
        this.splitNumber = splitNumber;
    }


    public String getSplitType() {
        return splitType;
    }

    public void setSplitType(String splitType) {
        this.splitType = splitType;
    }


    public String getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(String totalAmt) {
        this.totalAmt = totalAmt;
    }


    public String getRecAmt() {
        return recAmt;
    }

    public void setRecAmt(String recAmt) {
        this.recAmt = recAmt;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public List<PayerInfosBean> getPayerInfos() {
        return payerInfos;
    }

    public void setPayerInfos(List<PayerInfosBean> payerInfos) {
        this.payerInfos = payerInfos;
    }
}
