package om.rrtx.mobile.paymentmodule.fragment

import android.text.Editable
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.launcher.ARouter
import kotlinx.android.synthetic.main.payment_fragment_buy_bundle.next_tv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.paymentmodule.R
import om.rrtx.mobile.paymentmodule.activity.DataBundleActivity
import om.rrtx.mobile.paymentmodule.activity.DesignatedBundleActivity
import om.rrtx.mobile.paymentmodule.adapter.BundleOptionAdapter
import om.rrtx.mobile.paymentmodule.bean.BundleBean
import om.rrtx.mobile.paymentmodule.databinding.PaymentFragmentBuyBundleBinding
import om.rrtx.mobile.paymentmodule.viewModel.BillPayWM
import om.rrtx.mobile.rrtxcommon.utils.toast.ToastUtil
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseAdapterDataCallback
import om.rrtx.mobile.rrtxcommon1.base.BaseFragment
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.TextChangeEndListener
import om.rrtx.mobile.rrtxcommon1.widget.SpacesItemDecoration

class BuyBundleFragment : BaseFragment<BillPayWM, PaymentFragmentBuyBundleBinding>() {

    lateinit var mBundleOptionAdapter: BundleOptionAdapter
    lateinit var mBundleTypeBean: BundleBean
    override fun createViewLayoutId() = R.layout.payment_fragment_buy_bundle

    private lateinit var mobile_ed: EditText
    private lateinit var addressBock_iv: ImageView
    override fun initView() {

        dataBinding.apply {
            includeMobile.apply {
                // 跨model找不到id，手动处理
                mobile_ed = mobileEd
                addressBock_iv = addressBockIv
                addressBock_iv.setBackgroundResource(R.drawable.common_default_head)
                areaTv.setTextColor(resources.getColor(R.color.common_text_1d2129))
            }
            includeMobile.mobileEd.setText(viewModel.mobile)

            bundleTypeRv.apply {
                layoutManager = LinearLayoutManager(mContext)
                mBundleOptionAdapter = BundleOptionAdapter()
                mBundleOptionAdapter.dataCallback = object : BaseAdapterDataCallback<BundleBean> {
                    override fun callBack(position: Int, bean: BundleBean) {
                        mBundleTypeBean = bean
                        nextTv.isEnabled = true
                    }
                }
                adapter = mBundleOptionAdapter
                addItemDecoration(SpacesItemDecoration(24.pt2px()))
            }
        }
    }


    override fun initListener() {
        initClickListener()
        initVMListener()
        initTextChangListener()
    }

    override fun initDate() {
        viewModel.getBundleTypeList()
    }

    private fun initTextChangListener() {
        mobile_ed.addTextChangedListener(object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                viewModel.mobile = editable.toString()
            }
        })
    }

    private fun initClickListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    next_tv -> {
                        if (this@BuyBundleFragment::mBundleTypeBean.isInitialized)
                            buyBundle()
                    }

                    addressBock_iv -> {
                        ARouter.getInstance().build(ARouterPath.TransferPath.ContactListActivity)
                            .withString(BaseConstants.Transmit.JUMPFLAG,
                                BaseConstants.JumpFlag.BUY_AIRTIME_BUNDLE)
                            .navigation(activity, CommonConstants.ResultCode.Request)
                    }
                }
            }

        }.apply {
            next_tv.setOnClickListener(this)
            addressBock_iv.setOnClickListener(this)
        }
    }

    private fun buyBundle() {
        val mobile = mobile_ed.text.toString()

        if (!StringUtils.isValidMobile(mobile)) {
            ToastUtil.show(mContext, getString(R.string.mobile_error))
            return
        }

        when (mBundleTypeBean.bundleType) {
            CommonConstants.BundleType.Data_Bundles,
            CommonConstants.BundleType.Khuluma,
            CommonConstants.BundleType.Gigs,
            -> DataBundleActivity.jump(
                mContext,
                mobile_ed.text.toString(),
                mBundleTypeBean)

            CommonConstants.BundleType.Designated,
            CommonConstants.BundleType.OneMoney_Exclusive_Bundles-> DesignatedBundleActivity.jump(
                mContext,
                mobile_ed.text.toString(),
                mBundleTypeBean)

            else -> DataBundleActivity.jump(
                mContext,
                mobile_ed.text.toString(),
                mBundleTypeBean)
        }
    }

    private fun initVMListener() {
        viewModel.checkBuySuccessLD.observe(this) {
//            executeBuy(it)
        }
        viewModel.bundleTypeListLD.observe(this) {
            val list = arrayListOf<BundleBean>()
            for (bean in it.bundleTypeList) {
                list.add(BundleBean(bean.bundleType, bean.bundleName))
            }
            mBundleOptionAdapter.setNewData(list)
        }
    }

    override fun onResume() {
        super.onResume()
        resetUI()
    }

    private fun resetUI() {
        if (!this::mobile_ed.isInitialized) return
        mobile_ed.setText(viewModel.mobile)
        mBundleOptionAdapter.selectPosition = -1
        mBundleOptionAdapter.notifyDataSetChanged()
        next_tv.isEnabled = false
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        if (isVisibleToUser) {
            resetUI()
        }
    }


}