package om.rrtx.mobile.paymentmodule.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import java.util.List;

import butterknife.BindView;
import de.hdodenhof.circleimageview.CircleImageView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean;
import om.rrtx.mobile.functioncommon.callback.CashierCallBack;
import om.rrtx.mobile.functioncommon.utils.CashierManager;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.paymentmodule.adapter.SpiltRVAdapter;
import om.rrtx.mobile.paymentmodule.bean.AAPayDetailBean;
import om.rrtx.mobile.paymentmodule.bean.AARecDetailBean;
import om.rrtx.mobile.paymentmodule.bean.SendMessageBean;
import om.rrtx.mobile.paymentmodule.presenter.SplitBillPresenter;
import om.rrtx.mobile.paymentmodule.view.SplitBillView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.dialog.DoubleDialog;
import om.rrtx.mobile.rrtxcommon1.image.ImageLoaderManager;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 账单页面
 */
@Route(path = ARouterPath.Payment.SpiltBillActivity)
public class SpiltBillActivity extends BaseSuperActivity<SplitBillView, SplitBillPresenter>
        implements SplitBillView {

    @BindView(R2.id.unpaidRv)
    RecyclerView mUnpaidRv;
    @BindView(R2.id.paidRv)
    RecyclerView mPaidRv;
    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.heardIv)
    CircleImageView mHeardIv;
    @BindView(R2.id.totalTv)
    TextView mTotalTv;
    @BindView(R2.id.receiveTv)
    TextView mReceiveTv;
    @BindView(R2.id.remarkTv)
    TextView mRemarkTv;
    @BindView(R2.id.total)
    TextView mTotal;
    @BindView(R2.id.usd)
    TextView mUsd;
    @BindView(R2.id.unpaidTv)
    TextView mUnpaidTv;
    @BindView(R2.id.paidTv)
    TextView mPaidTv;
    @BindView(R2.id.hintTv)
    TextView mHintTv;
    @BindView(R2.id.payNowTv)
    TextView mPayNowTv;
    @BindView(R2.id.cancelL)
    LinearLayout mCancelL;
    @BindView(R2.id.cancelIv)
    ImageView mCancelIv;
    @BindView(R2.id.cancelTv)
    TextView mCancelTv;
    private String mJumpFlag;
    private SpiltRVAdapter mUnpaidRVAdapter;
    private String mOrderNo;
    private SpiltRVAdapter mPaidRVAdapter;
    private CashierManager mCashierManager;
    private DoubleDialog mDoubleDialog;
    private AAPayDetailBean mAaPayDetailBean;

    /**
     * 跳转页面带标识
     *
     * @param context 上下文
     * @param flag    标识
     *                leeued 发起的aa
     *                receive 收到的aa
     */
    public static void jumpSpiltBill(Context context, String orderNo, String flag) {
        Intent intent = new Intent(context, SpiltBillActivity.class);
        intent.putExtra(PaymentConstants.Transmit.JUMPFLAG, flag);
        intent.putExtra(PaymentConstants.Transmit.ORDERNO, orderNo);
        context.startActivity(intent);
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        mJumpFlag = getIntent().getStringExtra(PaymentConstants.Transmit.JUMPFLAG);
        mOrderNo = getIntent().getStringExtra(PaymentConstants.Transmit.ORDERNO);
    }

    @Override
    protected int createContentView() {
        return R.layout.payment_activity_spilt_bill;
    }

    @Override
    protected SplitBillPresenter createPresenter() {
        return new SplitBillPresenter(mContext);
    }

    @Override
    protected void initView() {

        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.payment_ic_back);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setText(R.string.bill_title_split_bill);
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));


        if (TextUtils.equals(mJumpFlag, PaymentConstants.Transmit.LSSUED)) {
            //发起的aa
            mPayNowTv.setVisibility(View.GONE);
            mCancelL.setVisibility(View.GONE);
            mPresenter.requestRecDetail(mOrderNo);
        } else {
            //收到的aa
            mHintTv.setText(R.string.receive_label_have_pay);
            mPayNowTv.setVisibility(View.VISIBLE);
            mCancelL.setVisibility(View.GONE);
            mPresenter.requestPayDetail(mOrderNo);
        }

        mUnpaidRv.setLayoutManager(new LinearLayoutManager(mContext) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });
        mUnpaidRVAdapter = new SpiltRVAdapter(mContext);
        mUnpaidRv.setAdapter(mUnpaidRVAdapter);

        mPaidRv.setLayoutManager(new LinearLayoutManager(mContext) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });
        mPaidRVAdapter = new SpiltRVAdapter(mContext);
        mPaidRv.setAdapter(mPaidRVAdapter);
    }

    @Override
    public void initDate() {
        super.initDate();
    }

    @Override
    public void initListener() {
        super.initListener();
        mCancelL.setOnClickListener(mSpiltCustom);
        mLeftBg.setOnClickListener(mSpiltCustom);
        mPayNowTv.setOnClickListener(mSpiltCustom);
    }

    private CustomClickListener mSpiltCustom = new CustomClickListener() {


        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.cancelL) {
                //关闭订单

                if (mDoubleDialog == null) {
                    mDoubleDialog = new DoubleDialog(mContext);
                    mDoubleDialog.setDoubleCallback(new DoubleDialog.DoubleCallback() {
                        @Override
                        public void leftCallback() {
                            mDoubleDialog.dismiss();
                            //退出登录
                            mPresenter.requestCancel(mOrderNo);
                        }

                        @Override
                        public void rightCallback() {
                            mDoubleDialog.dismiss();
                        }
                    });
                }
                mDoubleDialog.show();
                mDoubleDialog.setLeftStr(getString(R.string.common_alert_continue))
                        .setLeftColor(getResources().getColor(R.color.color_F85A40))
                        .setRightStr(getString(R.string.common_alert_cancel))
                        .setRightColor(getResources().getColor(R.color.common_ye_F3881E))
                        .setContentStr(getString(R.string.lssued_alert_collect_stop_tip))
                        .setMyTitle(getString(R.string.common_alert_prompt));

            } else if (view.getId() == R.id.leftBg) {
                finish();
            } else if (view.getId() == R.id.payNowTv) {
                CashierOrderInfoBean cashierOrderInfoBean = new CashierOrderInfoBean.Builder()
                        .setOrderInfo(getString(R.string.home_btn_split_bill))
                        .setPaymentProduct(CommonConstants.PaymentProduct.APP)
                        .setTransType(CommonConstants.TransType.AAPayment)
                        .setOrderSource(BaseConstants.OrderSourceType.INSIDE)
                        .setOrderAmt(mAaPayDetailBean.getPayAmt())
                        .setCurrency(CurrencyUtils.setCurrency(mContext, mAaPayDetailBean.getCurrencyType()))
                        .setPayType(CommonConstants.CashierPaymentType.Cashier_AASplit)
                        .setPaymentToken(mOrderNo)
                        .builder();

                mCashierManager = new CashierManager(SpiltBillActivity.this, new Gson().toJson(cashierOrderInfoBean), new CashierCallBack() {
                    @Override
                    public void paymentFailed(@NonNull String message) {
                    }

                    @Override
                    public void cancelOrderPay() {
                        mCashierManager.dismiss();
                    }

                    @Override
                    public void forgotCallBack() {

                        //忘记密码
                        String mobileAreaCode = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.MOBILEAREACODE, "");
                        String userMobile = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERMOBILE, "");
                        SendMessageBean sendMessageBean = new SendMessageBean.Builder()
                                .setMessageTemplateType(BaseConstants.MessageTemplateType.ForgetPaymentPassword)
                                .setJumpFlag(BaseConstants.SendMessageJump.SPILTBILLACTIVITY)
                                .setMobile(userMobile)
                                .setMobileAreaCode(mobileAreaCode)
                                .build();

                        ARouter.getInstance()
                                .build(ARouterPath.SecurityPath.PayPinSendActivity)
                                .withString(BaseConstants.Transmit.SENDMESSAGEJSON, new Gson().toJson(sendMessageBean))
                                .navigation();
                    }

                    @Override
                    public void paymentSuccess(String dataJson) {
                        finish();
                    }
                });

                mCashierManager.showCashierDialog();
            }
        }
    };

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    public void recDetailSuccess(AARecDetailBean aaRecDetailBean) {

        //头像
        ImageLoaderManager.getInstance().disPlayImage(mContext, aaRecDetailBean.getUserAvatar(), R.drawable.payment_ic_head_contact_round, mHeardIv);

        //总金额
        String totalAmt = getString(R.string.common_label_total_amt) + ": " + aaRecDetailBean.getCurrencyType() +" "+ StringUtils.formatAmount(aaRecDetailBean.getTotalAmt());
        mTotalTv.setText(totalAmt);

        //收到的金额
        String receiveAmt = getString(R.string.bill_label_amt_receivable) + ": " + aaRecDetailBean.getCurrencyType() +" "+ StringUtils.formatAmount(aaRecDetailBean.getRecAmt());
        mReceiveTv.setText(receiveAmt);

        //留言
        String remarkStr = getString(R.string.common_btn_remark) + ": " + aaRecDetailBean.getRemark();
        mRemarkTv.setText(remarkStr);

        //未收到的总金额
        mTotal.setText(StringUtils.formatAmount(aaRecDetailBean.getActRecAmt()));
        mUsd.setText(aaRecDetailBean.getCurrencyType());

        //未收到的列表
        String unPaidStr = getString(R.string.aa_label_unpaid) + ": " + aaRecDetailBean.getUnpaidOrderNumber() + " " + getString(R.string.aa_label_perople);
        mUnpaidTv.setText(unPaidStr);

        String paidStr = getString(R.string.aa_label_paid) + ": " + aaRecDetailBean.getRecOrderNumber() + " " + getString(R.string.aa_label_perople);
        mPaidTv.setText(paidStr);

        //订单状态
        String orderStatus = aaRecDetailBean.getOrderStatus();
        if (TextUtils.equals(orderStatus, PaymentConstants.AAOrderStatus.PENDINGOR) || TextUtils.equals(orderStatus, PaymentConstants.AAOrderStatus.PENDING)) {
            mCancelL.setVisibility(View.VISIBLE);
            mCancelTv.setTextColor(getResources().getColor(R.color.color_F85A40));
            mCancelL.setEnabled(true);
        } else if (TextUtils.equals(orderStatus, PaymentConstants.AAOrderStatus.CANCELLED)) {
            mCancelIv.setVisibility(View.GONE);
            mCancelL.setVisibility(View.VISIBLE);
            mCancelTv.setTextColor(getResources().getColor(R.color.color_999999));
            mCancelTv.setText(R.string.aa_label_cancelled);
            mCancelL.setEnabled(false);
        } else {
            mCancelL.setVisibility(View.GONE);
        }

        List<AARecDetailBean.PayListBean> unpaidPayList = aaRecDetailBean.getUnpaidPayList();
        if (unpaidPayList.size() > 0) {
            mUnpaidRv.setVisibility(View.VISIBLE);
            mUnpaidTv.setVisibility(View.VISIBLE);
            mUnpaidRVAdapter.setData(unpaidPayList);
        }

        List<AARecDetailBean.PayListBean> paidPayList = aaRecDetailBean.getPaidPayList();
        if (paidPayList.size() > 0) {
            mPaidRv.setVisibility(View.VISIBLE);
            mPaidTv.setVisibility(View.VISIBLE);
            mPaidRVAdapter.setData(paidPayList);
            mHintTv.setText(R.string.lssued_label_has_received);
        } else {
            mHintTv.setText(R.string.lssued_label_not_receive_tip);
        }
    }

    @Override
    public void cancelOrderSuccess() {
        finish();
    }

    @Override
    public void payDetailSuccess(AAPayDetailBean aaRecDetailBean) {
        mAaPayDetailBean = aaRecDetailBean;

        //头像
        ImageLoaderManager.getInstance().disPlayImage(mContext, aaRecDetailBean.getUserAvatar(), R.drawable.payment_ic_head_contact_round, mHeardIv);

        //总金额
        String totalAmt = getString(R.string.common_label_total_amt) + ": " + aaRecDetailBean.getCurrencyType() +" "+ StringUtils.formatAmount(aaRecDetailBean.getTotalAmt());
        mTotalTv.setText(totalAmt);

        //收到的金额
        mReceiveTv.setVisibility(View.GONE);

        //留言
        String remarkStr = getString(R.string.common_btn_remark) + ": " + aaRecDetailBean.getRemark();
        mRemarkTv.setText(remarkStr);

        //应付金额
        mTotal.setText(StringUtils.formatAmount(aaRecDetailBean.getPayAmt()));
        mUsd.setText(aaRecDetailBean.getCurrencyType());

        //订单状态
        String orderStatus = aaRecDetailBean.getOrderStatus();
        if (TextUtils.equals(orderStatus, PaymentConstants.AAOrderStatus.COMPLETE)) {
            mPayNowTv.setBackgroundColor(getResources().getColor(R.color.color_F5F7F8));
            mPayNowTv.setEnabled(false);
            mPayNowTv.setTextColor(getResources().getColor(R.color.common_ye_F3881E));
            mPayNowTv.setText(R.string.aa_label_paid);
        } else if (TextUtils.equals(orderStatus, PaymentConstants.AAOrderStatus.CANCELLED)) {
            mPayNowTv.setBackgroundColor(getResources().getColor(R.color.color_F5F7F8));
            mPayNowTv.setEnabled(false);
            mPayNowTv.setTextColor(getResources().getColor(R.color.common_ye_F3881E));
            mPayNowTv.setText(R.string.aa_label_cancelled);
        } else {
            mPayNowTv.setBackgroundResource(R.drawable.payment_drawable_btn_select);
            mPayNowTv.setEnabled(true);
        }

        //未收到的列表
        String unPaidStr = getString(R.string.aa_label_unpaid) + ": " + aaRecDetailBean.getUnpaidOrderNumber() + " " + getString(R.string.aa_label_perople);
        mUnpaidTv.setText(unPaidStr);

        String paidStr = getString(R.string.aa_label_paid) + ": " + aaRecDetailBean.getRecOrderNumber() + " " + getString(R.string.aa_label_perople);
        mPaidTv.setText(paidStr);

        List<AARecDetailBean.PayListBean> unpaidPayList = aaRecDetailBean.getUnpaidPayList();
        if (unpaidPayList.size() > 0) {
            mUnpaidRv.setVisibility(View.VISIBLE);
            mUnpaidTv.setVisibility(View.VISIBLE);
            mUnpaidRVAdapter.setData(unpaidPayList);
        }

        List<AARecDetailBean.PayListBean> paidPayList = aaRecDetailBean.getPaidPayList();
        if (paidPayList.size() > 0) {
            mPaidRv.setVisibility(View.VISIBLE);
            mPaidTv.setVisibility(View.VISIBLE);
            mPaidRVAdapter.setData(paidPayList);
        }
    }

    @Override
    public void payOrderSuccess() {
        finish();
    }
}
