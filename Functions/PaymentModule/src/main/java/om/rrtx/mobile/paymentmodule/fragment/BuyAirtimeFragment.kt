package om.rrtx.mobile.paymentmodule.fragment

import android.app.Activity
import android.text.Editable
import android.view.View
import android.widget.EditText
import android.widget.ImageView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.Gson
import kotlinx.android.synthetic.main.payment_fragment_buy_airtime.airtimeOption_rv
import kotlinx.android.synthetic.main.payment_fragment_buy_airtime.amount_ed
import kotlinx.android.synthetic.main.payment_fragment_buy_airtime.currency_bg
import kotlinx.android.synthetic.main.payment_fragment_buy_airtime.currency_tv
import kotlinx.android.synthetic.main.payment_fragment_buy_airtime.next_tv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean
import om.rrtx.mobile.functioncommon.bean.PaymentBean
import om.rrtx.mobile.functioncommon.dialog.CommonCurrencyDialog
import om.rrtx.mobile.paymentmodule.PaymentConstants
import om.rrtx.mobile.paymentmodule.R
import om.rrtx.mobile.paymentmodule.adapter.AirtimeOptionAdapter
import om.rrtx.mobile.paymentmodule.databinding.PaymentFragmentBuyAirtimeBinding
import om.rrtx.mobile.paymentmodule.utils.BuyAirtimeHelper
import om.rrtx.mobile.paymentmodule.viewModel.BillPayWM
import om.rrtx.mobile.rrtxcommon.utils.toast.ToastUtil
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseAdapterCallback
import om.rrtx.mobile.rrtxcommon1.base.BaseFragment
import om.rrtx.mobile.rrtxcommon1.bean.QueryOrderBean
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.MoneyUtil
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.TextChangeEndListener
import om.rrtx.mobile.rrtxcommon1.widget.GridSpaceItemDecoration

class BuyAirtimeFragment : BaseFragment<BillPayWM, PaymentFragmentBuyAirtimeBinding>() {

    private var curCurrency = ""
    private var curAmount = ""
    private var airtimePlanCode = ""
    lateinit var mAirtimeOptionAdapter: AirtimeOptionAdapter

    override fun createViewLayoutId() = R.layout.payment_fragment_buy_airtime

    private lateinit var mobile_ed: EditText
    private lateinit var addressBock_iv: ImageView
    override fun initView() {
        dataBinding.apply {
            includeMobile.apply {
                // 跨model找不到id，手动处理
                mobile_ed = mobileEd
                addressBock_iv = addressBockIv
                addressBock_iv.setBackgroundResource(R.drawable.common_default_head)
                areaTv.setTextColor(resources.getColor(R.color.common_text_1d2129))
                mobile_ed.setText(viewModel.mobile)
            }

            airtimeOptionRv.apply {
                layoutManager = GridLayoutManager(mContext, 3)
                mAirtimeOptionAdapter = AirtimeOptionAdapter(object : BaseAdapterCallback {
                    override fun callBack(position: Int) {
                        setNextEnable()
                    }
                })
                adapter = mAirtimeOptionAdapter
                addItemDecoration(GridSpaceItemDecoration(3, 24.pt2px(), 21.pt2px()))
            }
        }
    }

    override fun initDate() {
        viewModel.getAirtimeList(getString(R.string.usd))
    }

    override fun initListener() {
        initClickListener()
        initVMListener()
        initTextChangListener()
    }

    private fun initTextChangListener() {
        object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                setNextEnable()
            }
        }.apply {
            mobile_ed.addTextChangedListener(this)
            amount_ed.addTextChangedListener(this)
        }

        MoneyUtil.setInputEndListener(activity as Activity, amount_ed)
    }

    private fun initClickListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    currency_bg -> {
                        val mCommonCurrencyDialog =
                            CommonCurrencyDialog(
                                mContext,
                                "1"
                            )
                        mCommonCurrencyDialog.setOnCurrencyClickListener(CommonCurrencyDialog.OnCurrencyClickListener { currency -> // 赋值货币符号
                            setCurCurrency(currency)
                        })
                        mCommonCurrencyDialog.show()
                    }

                    next_tv -> {
                        buyAirtime()
                    }

                    addressBock_iv -> {
                        ARouter.getInstance().build(ARouterPath.TransferPath.ContactListActivity)
                            .withString(
                                BaseConstants.Transmit.JUMPFLAG,
                                BaseConstants.JumpFlag.BUY_AIRTIME_BUNDLE
                            )
                            .navigation(activity, CommonConstants.ResultCode.Request)
                    }
                }
            }
        }.apply {
            currency_bg.setOnClickListener(this)
            next_tv.setOnClickListener(this)
            addressBock_iv.setOnClickListener(this)
        }
    }

    private fun initVMListener() {

        viewModel.airtimeListLD.observe(this) {
            mAirtimeOptionAdapter.setNewData(it.airtimeList)
        }
    }


    /**
     * 1、电话输入
     * 2、金额输入
     * 3、套餐选择
     */
    private fun setNextEnable() {
        if (mobile_ed.text.isNotEmpty()) {
            when (curCurrency) {
                CommonConstants.Currency.ZWL -> {
                    // zwl 币种判断是否输入金额，金额格式点击时校验
                    next_tv.isEnabled = amount_ed.text.isNotEmpty()
                    return
                }

                CommonConstants.Currency.USD -> {
                    // usd 币种判断列表是否选中
                    if (mAirtimeOptionAdapter.isSelect()) {
                        next_tv.isEnabled = true
                        curAmount = mAirtimeOptionAdapter.getSelectAmount()
                        airtimePlanCode = mAirtimeOptionAdapter.getAirtimePlanCode()
                        return
                    }
                }

                else -> {
                    // zwl 币种判断是否输入金额，金额格式点击时校验
                    next_tv.isEnabled = amount_ed.text.isNotEmpty()
                    return
                }
            }
        }
        next_tv.isEnabled = false
    }

    fun buyAirtime() {
        val mobile = mobile_ed.text.toString()

        if (!StringUtils.isValidMobile(mobile)) {
            ToastUtil.show(mContext, getString(R.string.mobile_error))
            return
        }
        curAmount = amount_ed.text.toString()

        when (curCurrency) {
            CommonConstants.Currency.ZWL -> {
                if (!MoneyUtil.isValidMoney(curAmount)) {
                    ToastUtil.show(mContext, getString(R.string.common_tip_amount_error_tip))
                    return
                }
            }

            CommonConstants.Currency.USD -> {
                curAmount = mAirtimeOptionAdapter.getSelectAmount()
                airtimePlanCode = mAirtimeOptionAdapter.getAirtimePlanCode()
            }

            else -> {
                if (!MoneyUtil.isValidMoney(curAmount)) {
                    ToastUtil.show(mContext, getString(R.string.common_tip_amount_error_tip))
                    return
                }
            }

        }

        val queryOrderBean = QueryOrderBean(
            "",
            "",
            PaymentConstants.BillSubType.Airtime,
            "",
            mobile_ed.text.toString(),
            getString(R.string.buy_airtime),
            "",
            curAmount,
            airtimePlanCode,
            curCurrency,
            CommonConstants.PaymentProduct.ORDER_BUND
        )

        ARouter.getInstance().build(ARouterPath.Cashier.CashierPayActivity)
            .withString(BaseConstants.Transmit.JSON, Gson().toJson(queryOrderBean))
            .navigation()
    }

    private fun setCurCurrency(currency: String) {
        curCurrency = currency
        currency_tv.text = curCurrency

        when (curCurrency) {
            CommonConstants.Currency.USD -> {
                airtimeOption_rv.visibility = View.VISIBLE
                amount_ed.setText("")
                amount_ed.visibility = View.GONE
            }

            else -> {
                airtimeOption_rv.visibility = View.GONE
                amount_ed.visibility = View.VISIBLE
                mAirtimeOptionAdapter.setSelectPosition(-1)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        resetUI()
    }

    private fun resetUI() {
        if (!this::mobile_ed.isInitialized) return
        mobile_ed.setText(viewModel.mobile)
        setCurCurrency(CurrencyUtils.setCurrency(mContext,"ZWG"))
        amount_ed.setText("")
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        if (isVisibleToUser) {
            resetUI()
        }
    }
}