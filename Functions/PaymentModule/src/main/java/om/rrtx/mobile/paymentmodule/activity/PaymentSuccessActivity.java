package om.rrtx.mobile.paymentmodule.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functioncommon.bean.PaymentSuccessBean;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;

/**
 * <AUTHOR>
 * 支付成功页面
 */
public class PaymentSuccessActivity extends BaseSuperActivity {


    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.moneyTv)
    TextView mMoneyTv;
    @BindView(R2.id.merNameTv)
    TextView mMerNameTv;
    @BindView(R2.id.doneTv)
    TextView mDoneTv;
    @BindView(R2.id.amtTv)
    TextView mAmtTv;
    @BindView(R2.id.currencyTv)
    TextView mCurrencyTv;
    @BindView(R2.id.fee)
    TextView mFee;
    @BindView(R2.id.feeTv)
    TextView mFeeTv;
    private PaymentSuccessBean mPayCheckBean;

    public static void jumpPaymentSuccess(Context context, String payCheckBeanStr) {
        Intent intent = new Intent(context, PaymentSuccessActivity.class);
        intent.putExtra(PaymentConstants.Transmit.PAYCHECKDETAILS, payCheckBeanStr);
        context.startActivity(intent);
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        String payCheckBeanStr = getIntent().getStringExtra(PaymentConstants.Transmit.PAYCHECKDETAILS);
        mPayCheckBean = new Gson().fromJson(payCheckBeanStr, PaymentSuccessBean.class);
    }

    @Override
    protected int createContentView() {
        return R.layout.payment_activity_payment_success;
    }

    @Override
    protected BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setVisibility(View.GONE);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setText(R.string.payment_title_payment);
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));
    }

    @Override
    public void initDate() {
        super.initDate();

        if (mPayCheckBean != null) {
            mMoneyTv.setText(StringUtils.formatAmount(mPayCheckBean.getActAmount()));
            String currencyStr = CurrencyUtils.setCurrency(mContext, mPayCheckBean.getCurrency());
            String showAmt = currencyStr + " " + StringUtils.formatAmount(mPayCheckBean.getOrderAmount());
            mAmtTv.setText(showAmt);
            mCurrencyTv.setText(currencyStr);
            mMerNameTv.setText(mPayCheckBean.getMerName());
            if (!TextUtils.isEmpty(mPayCheckBean.getPayFeeAmount())&&!TextUtils.equals("0.00",mPayCheckBean.getPayFeeAmount())) {
                mFee.setVisibility(View.VISIBLE);
                mFeeTv.setVisibility(View.VISIBLE);
                String feeStr = CurrencyUtils.setCurrency(mContext, mPayCheckBean.getCurrency()) + " " + StringUtils.formatAmount(mPayCheckBean.getPayFeeAmount());
                mFeeTv.setText(feeStr);
            }
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mDoneTv.setOnClickListener(mCustomClickListener);
    }

    private CustomClickListener mCustomClickListener = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.doneTv) {
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .navigation();
            }
        }
    };

    @Override
    public void onBackPressed() {
        ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                .navigation();
    }
}
