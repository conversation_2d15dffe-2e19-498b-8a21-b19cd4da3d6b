package om.rrtx.mobile.paymentmodule.activity;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.gyf.immersionbar.ImmersionBar;

import java.math.BigDecimal;

import butterknife.BindView;
import butterknife.ButterKnife;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.paymentmodule.utils.EditInputFilter;
import om.rrtx.mobile.paymentmodule.utils.LogUtil;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.BigDecimalUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.SoftKeyBoardListener;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 设置金额页面
 */
public class SetPriceActivity extends BaseSuperActivity
        implements TextWatcher {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.moneyEt)
    EditText mMoneyEt;
    @BindView(R2.id.confirmTv)
    TextView mConfirmTv;
    @BindView(R2.id.remarkEt)
    EditText mRemarkEt;
    @BindView(R2.id.companyTv)
    TextView mCompanyTv;
    private String mMoneyStr;
    private String mCurrency;

    public static void jumpSetPrice(Context context) {
        Intent intent = new Intent(context, SetPriceActivity.class);
        context.startActivity(intent);
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    protected int createContentView() {
        return R.layout.payment_activity_set_price;
    }

    @Override
    protected BasePresenter createPresenter() {
        return null;
    }

    @Override
    public void doGetExtra() {
        mCurrency = getIntent().getStringExtra(PaymentConstants.Transmit.CURRENCY);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.payment_ic_back);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setText(R.string.price_title_set_price);
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));

        Log.e("SetPriceActivity>>>", "zfw initView>>> mCurrency:"+ mCurrency);
        mCompanyTv.setText(CurrencyUtils.setCurrency(mContext,mCurrency));


        mMoneyEt.setFocusable(true);
        mMoneyEt.setFocusableInTouchMode(true);
        mMoneyEt.requestFocus();
    }

    @Override
    public void initDate() {
        super.initDate();
        //设置EditText的过滤
        InputFilter[] filters = {new EditInputFilter(mContext, 9999999999.99)};
        mMoneyEt.setFilters(filters);
    }

    @Override
    public void initListener() {
        super.initListener();
        SoftKeyBoardListener.setListener(mContext, new SoftKeyBoardListener.OnSoftKeyBoardChangeListener() {
            @Override
            public void keyBoardShow(int height) {
                //键盘显示
                LogUtil.e("done", "keyBoardShow: ");
            }

            @Override
            public void keyBoardHide(int height) {
                LogUtil.e("done", "keyBoardHide: ");
                //键盘隐藏的时候设置相应的金额
                mMoneyStr = mMoneyEt.getText().toString();
                if (!TextUtils.isEmpty(mMoneyStr)) {
                    mMoneyStr = BigDecimalUtils.getStandardAmount(mMoneyStr, 2, BigDecimal.ROUND_HALF_UP);
                    mMoneyEt.setText(mMoneyStr);
                    mMoneyEt.setSelection(mMoneyStr.length());
                }
            }
        });

        mMoneyEt.addTextChangedListener(this);
        mLeftBg.setOnClickListener(mSetPriceCustom);
        mConfirmTv.setOnClickListener(mSetPriceCustom);
    }

    private CustomClickListener mSetPriceCustom = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.leftBg) {
                finish();
            } else if (view.getId() == R.id.confirmTv) {
                mMoneyStr = mMoneyEt.getText().toString();
                if (!TextUtils.isEmpty(mMoneyStr)) {
                    mMoneyStr = BigDecimalUtils.getStandardAmount(mMoneyStr, 2, BigDecimal.ROUND_HALF_UP);
                    mMoneyEt.setText(mMoneyStr);
                    mMoneyEt.setSelection(mMoneyStr.length());

                    if (TextUtils.equals(mMoneyStr, "0") || TextUtils.equals(mMoneyStr, "0.00")) {
                        String hint = getResources().getString(R.string.payment_tip_amount_must_greater) + mMoneyStr;
                        ToastUtil.show(mContext, hint);
                        return;
                    }

                    //回传金额
                    Intent intent = new Intent();
                    intent.putExtra(PaymentConstants.Transmit.PRICE, mMoneyStr);
                    intent.putExtra(PaymentConstants.Transmit.REMARK, mRemarkEt.getText().toString());
                    setResult(Activity.RESULT_OK, intent);
                    finish();
                }
            }
        }
    };

    @Override
    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void afterTextChanged(Editable editable) {
        String str = mMoneyEt.getText().toString();
        if (!TextUtils.isEmpty(str)) {
            String money = BigDecimalUtils.getStandardAmount(str, 2, BigDecimal.ROUND_HALF_UP);
            if (str.length() > 0 && Double.valueOf(money) > 0) {
                mConfirmTv.setEnabled(true);
            } else {
                mConfirmTv.setEnabled(false);
            }
        } else {
            mConfirmTv.setEnabled(false);
        }
    }
}
