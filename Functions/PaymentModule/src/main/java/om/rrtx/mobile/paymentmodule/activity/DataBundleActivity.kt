package om.rrtx.mobile.paymentmodule.activity

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.Gson
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.payment_activity_data_bundle.rv
import kotlinx.android.synthetic.main.payment_activity_data_bundle.title_tv
import kotlinx.android.synthetic.main.payment_activity_designated_bundle.srl
import kotlinx.android.synthetic.main.payment_activity_zesa_pay.moneyTv
import kotlinx.android.synthetic.main.payment_base_title.leftBg
import kotlinx.android.synthetic.main.payment_base_title.view.backIv
import kotlinx.android.synthetic.main.payment_base_title.view.statusView
import kotlinx.android.synthetic.main.payment_base_title.view.titleTv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean
import om.rrtx.mobile.functioncommon.bean.PaymentBean
import om.rrtx.mobile.functioncommon.utils.CashierManager
import om.rrtx.mobile.paymentmodule.PaymentConstants
import om.rrtx.mobile.paymentmodule.R
import om.rrtx.mobile.paymentmodule.adapter.BundleOptionAdapter
import om.rrtx.mobile.paymentmodule.adapter.DataBundleOptionAdapter
import om.rrtx.mobile.paymentmodule.bean.BundleBean
import om.rrtx.mobile.paymentmodule.databinding.PaymentActivityDataBundleBinding
import om.rrtx.mobile.paymentmodule.utils.BuyAirtimeHelper
import om.rrtx.mobile.paymentmodule.viewModel.BillPayWM
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseAdapterDataCallback
import om.rrtx.mobile.rrtxcommon1.base.BaseVVMActivity
import om.rrtx.mobile.rrtxcommon1.bean.QueryOrderBean
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.widget.SpacesItemDecoration

class DataBundleActivity : BaseVVMActivity<BillPayWM, PaymentActivityDataBundleBinding>() {

    lateinit var mBundleOptionAdapter: DataBundleOptionAdapter
    private lateinit var mCashierManager: CashierManager
    private lateinit var mBundleBean: BundleBean
    private var bundleType = ""
    private var bundleName = ""

    private var page = 1
    private var number = 10

    override fun createContentView() = R.layout.payment_activity_data_bundle

    override fun doGetExtra() {
        val json = intent.getStringExtra(CommonConstants.Transmit.JSON)
        val fromJson = Gson().fromJson(json, BundleBean::class.java)
        bundleType = fromJson.bundleType
        bundleName = fromJson.bundlePlan
    }

    override fun initView() {
        initTitle()

        title_tv.text = bundleName
        rv.apply {
            layoutManager = LinearLayoutManager(mContext)
            mBundleOptionAdapter = DataBundleOptionAdapter()
//            bundleOptionAdapter.setNewData(arrayListOf(
//                InfoItemBean(title = getString(R.string.khuluma_24_7_Bundle)),
//                InfoItemBean(title = getString(R.string.data_Mo_Gigs_Bundle)),
//                InfoItemBean(title = getString(R.string.data_Bundle)),
//                InfoItemBean(title = getString(R.string.designated_APP_Bundle)),
//            ))
            mBundleOptionAdapter.dataCallback = object : BaseAdapterDataCallback<BundleBean> {
                override fun callBack(position: Int, bean: BundleBean) {
                    mBundleBean = bean
                    //viewModel.checkBuyAirtimeOrBundle(bean.price, bean.currency)
                    val queryOrderBean = QueryOrderBean(
                        "",
                        title_tv.text.toString() + "-" + mBundleBean.bundlePlan,
                        PaymentConstants.BillSubType.Bundle,
                        "",
                        intent.getStringExtra(CommonConstants.Transmit.MOBILE).toString(),
                        getString(R.string.buy_Bundle),
                        mBundleBean.bundleId,
                        bean.price,
                        mBundleBean.pricePlanCode,
                        bean.currency,
                        CommonConstants.PaymentProduct.ORDER_BUND
                    )

                    ARouter.getInstance().build(ARouterPath.Cashier.CashierPayActivity)
                        .withString(BaseConstants.Transmit.JSON, Gson().toJson(queryOrderBean))
                        .navigation()
                }
            }
            adapter = mBundleOptionAdapter
            addItemDecoration(SpacesItemDecoration(31.pt2px()))
        }
    }

    private fun initTitle() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true, 0.2f)
            .init()
        dataBinding.includeTitle.apply {
            titleTv.setText(R.string.bundle_Plan)
            titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
            backIv.setBackgroundResource(R.drawable.common_ic_back_black)
            statusView.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
            titleTv.setBackgroundColor(resources.getColor(R.color.color_FFFFFF))
        }
    }

    override fun initData() {
        getBundleList()
    }

    override fun initVMListener() {
        viewModel.bundleListLD.observe(this) {
            if (page == 1) {
                srl.finishRefresh()
                mBundleOptionAdapter.setNewData(it.records)
            } else {
                srl.finishLoadMore()
                mBundleOptionAdapter.addData(it.records)
            }
        }

        viewModel.checkBuySuccessLD.observe(this) {
            executeBuy(it)
        }
    }

    override fun initRefreshListener() {
        srl.setOnLoadMoreListener {
            page++
            getBundleList()
        }

        srl.setOnRefreshListener {
            page = 1
            getBundleList()
        }
    }

    override fun initClickListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    leftBg -> onBackPressed()
                }
            }

        }.apply {
            leftBg.setOnClickListener(this)
        }
    }

    fun getBundleList() {
        viewModel.getBundleList(
            bundleType, "",
            "", page, number
        )
    }

    private fun executeBuy(it: PaymentBean?) {
        if (it != null) {
            val cashierOrderInfoBean = CashierOrderInfoBean.Builder()
                .setOrderInfo(getString(R.string.buy_Bundle))
                .setCurrency(mBundleBean.currency)
                .setOrderAmt(mBundleBean.price)
                .setTransType(CommonConstants.TransType.Mobile_Fee)
                .setSetmeal(title_tv.text.toString() + "-" + mBundleBean.bundlePlan)
                .setPayType(CommonConstants.CashierPaymentType.AIRTIME_BUNDLE)
                .setBillSubType(PaymentConstants.BillSubType.Bundle)
                .setBundleId(mBundleBean.bundleId)
                .setPlanCode(mBundleBean.pricePlanCode)
                .setMobile(intent.getStringExtra(CommonConstants.Transmit.MOBILE))
                .builder()

            BuyAirtimeHelper.buyAirtime(this, cashierOrderInfoBean)
        }
    }

    companion object {
        fun jump(mContext: Context, mobile: String, bean: BundleBean) {
            val intent = Intent(mContext, DataBundleActivity::class.java)
            intent.putExtra(CommonConstants.Transmit.MOBILE, mobile)
            intent.putExtra(CommonConstants.Transmit.JSON, Gson().toJson(bean))
            mContext.startActivity(intent)
        }
    }
}