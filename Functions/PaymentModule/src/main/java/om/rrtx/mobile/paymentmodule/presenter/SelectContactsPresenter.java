package om.rrtx.mobile.paymentmodule.presenter;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;

import om.rrtx.mobile.paymentmodule.bean.ChangeFlagBean;
import om.rrtx.mobile.paymentmodule.bean.ContactBean;
import om.rrtx.mobile.paymentmodule.bean.PubBean;
import om.rrtx.mobile.paymentmodule.bean.QrCodeBean;
import om.rrtx.mobile.paymentmodule.model.PaymentModel;
import om.rrtx.mobile.paymentmodule.utils.ContactListUtils;
import om.rrtx.mobile.paymentmodule.view.ReceivePaymentView;
import om.rrtx.mobile.paymentmodule.view.SelectContactsView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * 收付款页面Presenter层
 */
public class SelectContactsPresenter extends BasePresenter<SelectContactsView> {

    private final PaymentModel mPaymentModel;
    private Context mContext;

    public SelectContactsPresenter(Context context) {
        mPaymentModel = new PaymentModel();
        mContext = context;
    }

    /**
     * 查询联系人列表
     */
    public void requestContactList() {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestContactList(userId,new BaseObserver<ContactBean>(mContext) {
                @Override
                public void requestSuccess(ContactBean sResData) {
                    if (getView() != null) {
                        getView().contactListSuccess(sResData);
                        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.CONTACTLISTJSON, new Gson().toJson(sResData));
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        }else {
            mPaymentModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mPaymentModel.requestContactList(userId,new BaseObserver<ContactBean>(mContext) {
                        @Override
                        public void requestSuccess(ContactBean sResData) {
                            if (getView() != null) {
                                getView().contactListSuccess(sResData);
                                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.CONTACTLISTJSON, new Gson().toJson(sResData));
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    public void requestIsContactChange() {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        //1. 先判断本地是否存储,没有直接请求
        //2. 存储后先请求判断接口
        //3. 如果需要请求,继续请求
        String contactListJson = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.CONTACTLISTJSON, "");
        String md5Str = "";
        ContactBean contactBean = new Gson().fromJson(contactListJson, ContactBean.class);
        if (!TextUtils.isEmpty(contactListJson)) {
            md5Str = ContactListUtils.constactMd5(contactBean);
        }
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestIsContactChange(userId,md5Str, new BaseObserver<ChangeFlagBean>(mContext) {
                @Override
                public void requestSuccess(ChangeFlagBean sResData) {
                    if (TextUtils.equals(sResData.getChangeFlag(), "0")) {
                        getView().contactListSuccess(contactBean);
                    } else {
                        requestContactList();
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        }else {
            String finalMd5Str = md5Str;
            mPaymentModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mPaymentModel.requestIsContactChange(userId,finalMd5Str, new BaseObserver<ChangeFlagBean>(mContext) {
                        @Override
                        public void requestSuccess(ChangeFlagBean sResData) {
                            if (TextUtils.equals(sResData.getChangeFlag(), "0")) {
                                getView().contactListSuccess(contactBean);
                            } else {
                                requestContactList();
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

}
