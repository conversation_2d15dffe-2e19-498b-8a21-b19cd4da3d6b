package om.rrtx.mobile.paymentmodule.adapter;

import android.content.Context;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.EditText;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.bean.ByMobileBean;
import om.rrtx.mobile.paymentmodule.utils.EditInputFilter;
import om.rrtx.mobile.rrtxcommon1.base.BaseHolder;
import om.rrtx.mobile.rrtxcommon1.image.ImageLoaderManager;
import om.rrtx.mobile.rrtxcommon1.utils.BigDecimalUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.widget.SWImageView;

/**
 * <AUTHOR>
 * 联系人添加金额页面适配器
 */
public class AASpiltBillRVAdapter extends RecyclerView.Adapter<BaseHolder> {
    private Context mContext;
    private List<ByMobileBean> mBeanLists;

    public AASpiltBillRVAdapter(Context context) {
        mContext = context;
        mBeanLists = new ArrayList<>();
    }

    @NonNull
    @Override
    public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.payment_item_aa_spilt_bill, parent, false);
        return new BaseHolder(rootView);
    }

    @Override
    public void onBindViewHolder(@NonNull BaseHolder holder, int position) {
        ByMobileBean byMobileBean = mBeanLists.get(position);

        SWImageView swipeLayout = holder.getView(R.id.heardIv);
        ImageLoaderManager.getInstance().disPlayImage(mContext, byMobileBean.getUserAvatar(), R.drawable.payment_ic_head_contact_default, swipeLayout);

        TextView userName = holder.getView(R.id.nameTv);
        userName.setText(byMobileBean.getRealName());

        TextView phoneTv = holder.getView(R.id.phoneTv);
        phoneTv.setText(StringUtils.stringMask(byMobileBean.getMobileAreaCode(), byMobileBean.getMobile()));

        TextView amtEt = holder.getView(R.id.amtEt);
        String standardAmount = BigDecimalUtils.getStandardAmount(byMobileBean.getAmount(), 2, BigDecimal.ROUND_HALF_UP);
        amtEt.setText(StringUtils.formatAmount(standardAmount));
    }

    @Override
    public int getItemCount() {
        return mBeanLists != null ? mBeanLists.size() : 0;
    }

    public void setData(List<ByMobileBean> list) {
        mBeanLists = list;
        notifyDataSetChanged();
    }
}
