package om.rrtx.mobile.paymentmodule.utils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import cn.hutool.crypto.SecureUtil;
import om.rrtx.mobile.paymentmodule.bean.ByMobileBean;
import om.rrtx.mobile.paymentmodule.bean.ContactBean;

/**
 * <AUTHOR>
 * 联系人列表工具类
 */
public class ContactListUtils {

    /**
     * 获取联系人的md5值
     *
     * @param contactBean 联系人实体类
     * @return md5加密串
     */
    public static String constactMd5(ContactBean contactBean) {
        if (contactBean == null) {
            return "";
        }

        List<ByMobileBean> contactList = contactBean.getContactList();
        if (contactList == null) {
            return "";
        }

        List<String> constactMobile = new ArrayList<>();

        for (int i = 0; i < contactList.size(); i++) {
            ByMobileBean byMobileBean = contactList.get(i);
            constactMobile.add(byMobileBean.getMobile().replace("+", ""));
        }

        Collections.sort(constactMobile);

        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < constactMobile.size(); i++) {
            sb.append(constactMobile.get(i));
        }

        return SecureUtil.md5(sb.toString());
    }
}
