package om.rrtx.mobile.paymentmodule.utils

import androidx.appcompat.app.AppCompatActivity
import com.google.gson.Gson
import om.rrtx.mobile.functioncommon.activity.ComResultActivity
import om.rrtx.mobile.functioncommon.bean.AirtimeSuccessBean
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean
import om.rrtx.mobile.functioncommon.bean.ComResultBean
import om.rrtx.mobile.functioncommon.callback.CashierCallBack
import om.rrtx.mobile.functioncommon.utils.CashierManager
import om.rrtx.mobile.paymentmodule.R
import om.rrtx.mobile.rrtxcommon1.BaseConstants

object BuyAirtimeHelper {

    var mCashierManager: CashierManager? = null

    /**
     * 话费、流量 支付流程统一处理
     */
    fun buyAirtime(activity: AppCompatActivity, bean: CashierOrderInfoBean) {
        mCashierManager =
            CashierManager(activity,
                Gson().toJson(bean),
                object : CashierCallBack {

                    override fun paymentSuccess(dataJson: String) {
                        val fromJson = Gson().fromJson(dataJson, AirtimeSuccessBean::class.java)
                        val s2 = fromJson.currency + " " + fromJson.actAmount
                        val comResultBean =
                            ComResultBean(
                                activity.getString(R.string.buy_Airtime_Bundle),
                                s1 = "",
                                s2 = s2,
                                flag = BaseConstants.JumpFlag.BUY_AIRTIME_BUNDLE,
                                status = fromJson.transStatus
                            )
                        ComResultActivity.jump(activity, comResultBean)

                        mCashierManager!!.dismiss()
                        mCashierManager = null
                    }

                    override fun paymentFailed(message: String) {

                    }
                })
        mCashierManager!!.showCashierDialog()
    }
}