package om.rrtx.mobile.paymentmodule.fragment;

import android.graphics.Color;
import android.graphics.drawable.ColorDrawable;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.widget.PayPasswordView;
import om.rrtx.mobile.rrtxcommon1.base.BaseBottomFragment;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;

/**
 * <AUTHOR>
 * 底部PaymentPin对话框
 * 他的功能应该是把
 * - 键入之后返回
 * - 忘记密码回调
 * - 关闭对话框回调
 */
public class PaymentPinBottomFragment extends BaseBottomFragment
        implements PayPasswordView.Password {


    private TextView mTitleTv;
    private View mLeftBg;
    private ImageView mBackIv;
    private PayPasswordView mPayPasswordView;
    private PaymentPinCallback mPinCallback;

    public static PaymentPinBottomFragment newInstance(int titleRes, int backRes) {
        Bundle args = new Bundle();
        args.putInt(PaymentConstants.Transmit.TITLERES, titleRes);
        args.putInt(PaymentConstants.Transmit.BACKRES, backRes);
        PaymentPinBottomFragment fragment = new PaymentPinBottomFragment();
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected int createViewLayoutId() {
        return R.layout.payment_dialog_payment_pin;
    }

    @Override
    public BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView(View rootView) {
        mTitleTv = rootView.findViewById(R.id.titleTv);
        mLeftBg = rootView.findViewById(R.id.leftBg);
        mBackIv = rootView.findViewById(R.id.backIv);
        mPayPasswordView = rootView.findViewById(R.id.ppv_content);
    }

    @Override
    public void initDate() {
        super.initDate();
        Bundle arguments = getArguments();

        if (arguments != null) {
            int backRes = arguments.getInt(PaymentConstants.Transmit.BACKRES);
            mBackIv.setImageResource(backRes);
            int titleRes = arguments.getInt(PaymentConstants.Transmit.TITLERES);
            mTitleTv.setText(titleRes);
            mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));
        }
    }

    @Override
    public void initListener() {
        super.initListener();

        mPayPasswordView.setPassword(this);
        //关闭对话框
        mLeftBg.setOnClickListener(view -> {
            if (mPinCallback != null) {
                mPinCallback.closed();
            }
        });
    }

    /**
     * 取消对话框上面的文字
     */
    public void cancelPassword() {
        mPayPasswordView.cancelPassword();
    }

    @Override
    public void passwordCallBack(String password) {
        if (mPinCallback != null) {
            mPinCallback.completed(password);
        }
    }

    @Override
    public void forgotCallBack() {
        if (mPinCallback != null) {
            mPinCallback.forgetPin();
        }
    }

    public void setPinCallback(PaymentPinCallback pinCallback) {
        mPinCallback = pinCallback;
    }

    /**
     * 付款对话框的回调
     */
    public interface PaymentPinCallback {
        /**
         * 填写完成
         *
         * @param code code吗
         */
        void completed(String code);

        /**
         * 忘记密码
         */
        void forgetPin();

        /**
         * 关闭
         */
        void closed();
    }
}
