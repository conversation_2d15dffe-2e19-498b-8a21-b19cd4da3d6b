package om.rrtx.mobile.paymentmodule.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.alibaba.android.arouter.launcher.ARouter;
import com.gavin.com.library.StickyDecoration;
import com.gavin.com.library.listener.GroupListener;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.paymentmodule.adapter.SelectConRVAdapter;
import om.rrtx.mobile.paymentmodule.bean.ByMobileBean;
import om.rrtx.mobile.paymentmodule.bean.ContactBean;
import om.rrtx.mobile.paymentmodule.presenter.SelectContactsPresenter;
import om.rrtx.mobile.paymentmodule.utils.SelectContactHelper;
import om.rrtx.mobile.paymentmodule.view.SelectContactsView;
import om.rrtx.mobile.paymentmodule.view.SideBar;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;
import om.rrtx.mobile.rrtxcommon1.widget.EmptyRecycleView;

/**
 * <AUTHOR>
 * 选择联系人页面
 */
public class SelectContactsActivity extends BaseSuperActivity<SelectContactsView, SelectContactsPresenter>
        implements SelectContactsView, RVAdapterItemClickListener<ByMobileBean>, TextWatcher, SideBar.OnTouchingLetterChangedListener {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.contentRv)
    EmptyRecycleView mContentRv;
    @BindView(R2.id.sideBar)
    SideBar mSideBar;
    @BindView(R2.id.confirmTv)
    TextView mConfirmTv;
    @BindView(R2.id.searchBg)
    View mSearchBg;
    @BindView(R2.id.searchEt)
    EditText mSearchEt;
    @BindView(R2.id.rightBg)
    View mRightBg;
    private SelectConRVAdapter mSelectConRVAdapter;
    private List<ByMobileBean> mTotalList;
    private List<ByMobileBean> mBeanList;
    private List<ByMobileBean> mSelectList;
    private LinearLayoutManager mLinearLayoutManager;
    private ContactBean mSelectContactBean;
    private String mJumpFlag;
    private int mSelectCount = 0;
    private String mCurrencyStr = "USD";

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        String selectListJson = getIntent().getStringExtra(PaymentConstants.Transmit.SELECTLIST);
        mSelectContactBean = new Gson().fromJson(selectListJson, ContactBean.class);
        mJumpFlag = getIntent().getStringExtra(PaymentConstants.Transmit.JUMPFLAG);
        mCurrencyStr = getIntent().getStringExtra(CommonConstants.Transmit.CURRENCY);
    }

    @Override
    protected void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
//        outState.pu
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    protected int createContentView() {
        return R.layout.payment_activity_select_contacts;
    }

    @Override
    protected SelectContactsPresenter createPresenter() {
        return new SelectContactsPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        String confirmStr = getString(R.string.common_btn_confirm) + "(0)";
        mConfirmTv.setText(confirmStr);
    }

    @Override
    public void initDate() {
        super.initDate();
        mSelectList = new ArrayList<>();

        initRecyclerView();

        if (TextUtils.equals(mJumpFlag, PaymentConstants.JumpFlag.NoEqual)) {
            mSelectCount = 100;
        } else if (TextUtils.equals(mJumpFlag, PaymentConstants.JumpFlag.Equal)) {
            mSelectCount = 99;
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        mPresenter.requestIsContactChange();
    }

    private void initRecyclerView() {
        mBeanList = new ArrayList<>();

        //设置吸顶效果
        GroupListener groupListener = new GroupListener() {
            @Override
            public String getGroupName(int position) {
                ByMobileBean mobileBean = mBeanList.get(position);
                String realName = mobileBean.getRealName();
                char realChar = realName.toUpperCase().charAt(0);
                //获取分组名
                if (realChar < 65 || realChar > 90) {
                    return "#";
                } else {
                    return String.valueOf(realChar);
                }
            }
        };
        StickyDecoration decoration = StickyDecoration.Builder
                .init(groupListener)
                .setGroupBackground(getResources().getColor(R.color.color_F5F7F8))
                .setGroupHeight((int) getResources().getDimension(R.dimen.payment_item_decoration_title_height))
                .setGroupTextSize((int) getResources().getDimension(R.dimen.payment_item_decoration_title_fontsize))
                .setTextSideMargin((int) getResources().getDimension(R.dimen.payment_item_decoration_title_start_margin))
                .setGroupTextColor(getResources().getColor(R.color.color_131313))
                .build();

        mLinearLayoutManager = new LinearLayoutManager(mContext);
        mContentRv.setLayoutManager(mLinearLayoutManager);
        mSelectConRVAdapter = new SelectConRVAdapter(mContext);
        mSelectConRVAdapter.setListener(this);
        mContentRv.setAdapter(mSelectConRVAdapter);
        mContentRv.addItemDecoration(decoration);
    }

    @Override
    public void initListener() {
        super.initListener();

        mSearchEt.addTextChangedListener(this);
        mSideBar.setOnTouchingLetterChangedListener(this);
        mConfirmTv.setOnClickListener(selectCustom);
        mLeftBg.setOnClickListener(selectCustom);
        mRightBg.setOnClickListener(selectCustom);
    }

    private CustomClickListener selectCustom = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.confirmTv) {
                if (TextUtils.equals(mJumpFlag, PaymentConstants.JumpFlag.NoEqual)) {
                    Log.e("SelectContactsActivity>>>", "zfw onSingleClick>>> mCurrencyStr:"+ mCurrencyStr);
                    //非等额
                    mSelectContactBean = new ContactBean();
                    mSelectContactBean.setContactList(mSelectList);
                    SplitIndividuallyActivity.jumpSplitIndividually(mContext, new Gson().toJson(mSelectContactBean),mCurrencyStr);
                } else if (TextUtils.equals(mJumpFlag, PaymentConstants.JumpFlag.Equal)) {
                    Intent intent = new Intent();
                    mSelectContactBean = new ContactBean();
                    mSelectContactBean.setContactList(mSelectList);
                    intent.putExtra(PaymentConstants.Transmit.DETAILSBEAN, new Gson().toJson(mSelectContactBean));
                    setResult(RESULT_OK, intent);
                    finish();
                }
            } else if (view.getId() == R.id.leftBg) {
                finish();
            } else if (view.getId() == R.id.rightBg) {
                mSelectContactBean = new ContactBean();
                mSelectContactBean.setContactList(mSelectList);
                ARouter.getInstance().build(ARouterPath.TransferPath.AddContactsActivity)
                        .navigation();
            }
        }
    };

    @Override
    public void contactListSuccess(ContactBean contactBean) {
        List<ByMobileBean> contactList = contactBean.getContactList();
        if (contactList == null) {
            contactList = new ArrayList<>();
        }

        if (mSelectContactBean == null || mSelectContactBean.getContactList().size() == 0) {
            mBeanList = SelectContactHelper.handlerList(mBeanList, contactList);
            mTotalList = contactList;
        } else {
            mSelectList = SelectContactHelper.handlerSelectInTotalList(mSelectContactBean.getContactList(), contactList);

            mBeanList = SelectContactHelper.handlerList(mSelectList, contactList);
            mTotalList = SelectContactHelper.handlerList(mSelectList, contactList);

            mConfirmTv.setEnabled(true);
            mConfirmTv.setBackgroundResource(R.drawable.payment_drawable_btn_select);

            String confirmStr = getString(R.string.common_btn_confirm) + "(" + mSelectList.size() + ")";
            mConfirmTv.setText(confirmStr);
        }
        Collections.sort(mBeanList);
        Collections.sort(mTotalList);
        mSelectConRVAdapter.setData(contactList);
    }

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    public void itemClickListener(ByMobileBean byMobileBean, int position) {
        boolean select = byMobileBean.isSelect();
        if (select) {
            select = false;
            byMobileBean.setSelect(select);
            SelectContactHelper.removeItem(mSelectList, byMobileBean);
            mBeanList.set(position, byMobileBean);
            mSelectConRVAdapter.notifyDataSetChanged();
        } else {
            if (mSelectList.size() >= mSelectCount) {
                ToastUtil.show(mContext, getString(R.string.bill_tip_select_acount_tip));
                return;
            } else {
                select = true;
                byMobileBean.setSelect(select);
                mSelectList.add(byMobileBean);
                mSelectConRVAdapter.notifyDataSetChanged();
            }
        }

        if (mSelectList.size() > 0) {
            mConfirmTv.setEnabled(true);
            mConfirmTv.setBackgroundResource(R.drawable.payment_drawable_btn_select);
            String confirmStr = getString(R.string.common_btn_confirm) + "(" + mSelectList.size() + ")";
            mConfirmTv.setText(confirmStr);
        } else {
            String confirmStr = getString(R.string.common_btn_confirm) + "(0)";
            mConfirmTv.setText(confirmStr);
            mConfirmTv.setEnabled(false);
            mConfirmTv.setBackgroundResource(R.drawable.payment_drawable_btn_unselect);
        }
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable editable) {
        String searchStr = editable.toString();
        if (!TextUtils.isEmpty(searchStr) && mBeanList.size() > 0) {
            mBeanList = SelectContactHelper.getSearchList(searchStr, mBeanList);
            mSelectConRVAdapter.setData(mBeanList);
        } else if (TextUtils.isEmpty(searchStr)) {
            mBeanList = mTotalList;
            mSelectConRVAdapter.setData(mBeanList);
        }
    }

    @Override
    public void onTouchingLetterChanged(String s) {
        int index = SelectContactHelper.reasonIndexNum(s, mBeanList);
        if (-1 != index) {
            mLinearLayoutManager.scrollToPositionWithOffset(index, 0);
        }
    }
}
