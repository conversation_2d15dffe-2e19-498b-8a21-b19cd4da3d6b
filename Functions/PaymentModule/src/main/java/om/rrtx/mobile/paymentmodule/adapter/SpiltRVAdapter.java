package om.rrtx.mobile.paymentmodule.adapter;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.bean.AARecDetailBean;
import om.rrtx.mobile.rrtxcommon1.base.BaseHolder;
import om.rrtx.mobile.rrtxcommon1.image.ImageLoaderManager;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.widget.SWImageView;

/**
 * <AUTHOR>
 * aa收付款页面的适配器
 */
public class SpiltRVAdapter extends RecyclerView.Adapter<BaseHolder> {
    private Context mContext;
    private List<AARecDetailBean.PayListBean> mPayList;

    public SpiltRVAdapter(Context context) {
        mContext = context;
        mPayList = new ArrayList<>();
    }

    @NonNull
    @Override
    public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.payment_item_spilt, parent, false);
        return new BaseHolder(rootView);
    }

    @Override
    public void onBindViewHolder(@NonNull BaseHolder holder, int position) {

        AARecDetailBean.PayListBean payListBean = mPayList.get(position);

        //头像
        SWImageView heardIv = holder.getView(R.id.heardIv);
        ImageLoaderManager.getInstance().disPlayImage(mContext,
                payListBean.getUserAvatar(), R.drawable.payment_ic_head_contact, heardIv);

        //姓名
        TextView nameTv = holder.getView(R.id.nameTv);
        nameTv.setText(payListBean.getPayCstName());

        //金额
        TextView moneyTv = holder.getView(R.id.moneyTv);
        String moneyStr = CurrencyUtils.setCurrency(mContext,payListBean.getCurrency()) + " " + StringUtils.formatAmount(payListBean.getTransAmt());
        moneyTv.setText(moneyStr);

        TextView statusTv = holder.getView(R.id.statusTv);
        if (TextUtils.equals(payListBean.getDetailOrderStatus(), PaymentConstants.AAOrderStatus.COMPLETE)) {
            statusTv.setText(mContext.getString(R.string.aa_label_paid_amt));
            statusTv.setTextColor(mContext.getResources().getColor(R.color.color_17904B));
            moneyTv.setTextColor(mContext.getResources().getColor(R.color.color_17904B));
        } else {
            statusTv.setText(mContext.getString(R.string.aa_label_pending_amt));
            statusTv.setTextColor(mContext.getResources().getColor(R.color.color_999999));
            moneyTv.setTextColor(mContext.getResources().getColor(R.color.common_ye_F3881E));
        }

        TextView phoneTv = holder.getView(R.id.phoneTv);
        phoneTv.setText(StringUtils.stringMask(payListBean.getMobileAreaCode(),payListBean.getPayMobile()));
    }

    @Override
    public int getItemCount() {
        return mPayList != null ? mPayList.size() : 0;
    }

    public void setData(List<AARecDetailBean.PayListBean> unpaidPayList) {
        mPayList = unpaidPayList;
        notifyDataSetChanged();
    }
}
