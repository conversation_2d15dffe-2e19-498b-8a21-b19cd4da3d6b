package om.rrtx.mobile.paymentmodule.activity

import android.app.Activity
import android.content.Intent
import android.view.View
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.payment_activity_re_send_token.emptyCl
import kotlinx.android.synthetic.main.payment_activity_re_send_token.rv
import kotlinx.android.synthetic.main.payment_activity_re_send_token.srl
import kotlinx.android.synthetic.main.payment_activity_re_send_token.timeTv
import kotlinx.android.synthetic.main.payment_base_title.leftBg
import kotlinx.android.synthetic.main.payment_base_title.view.backIv
import kotlinx.android.synthetic.main.payment_base_title.view.statusView
import kotlinx.android.synthetic.main.payment_base_title.view.titleTv
import om.rrtx.mobile.functioncommon.PayMethodLiveData
import om.rrtx.mobile.functioncommon.bean.RecordBean
import om.rrtx.mobile.functioncommon.callback.PinDialogCallback
import om.rrtx.mobile.functioncommon.dialog.PayPsdBottomFragment
import om.rrtx.mobile.paymentmodule.R
import om.rrtx.mobile.paymentmodule.adapter.PayRecordAdapter
import om.rrtx.mobile.paymentmodule.databinding.PaymentActivityReSendTokenBinding
import om.rrtx.mobile.paymentmodule.viewModel.BillPayWM
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity
import om.rrtx.mobile.rrtxcommon1.base.BaseAdapterDataCallback
import om.rrtx.mobile.rrtxcommon1.dialog.DateSelectDialog
import om.rrtx.mobile.rrtxcommon1.dialog.DoubleDialog
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.TimeUtils
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil
import om.rrtx.mobile.rrtxcommon1.widget.SpacesItemDecoration
import java.util.Date

class ReSendTokenActivity : BaseActivity<PaymentActivityReSendTokenBinding>() {
    lateinit var mVM: BillPayWM
    var pageNum = 1
    var pageSize = 10
    var selectTime = ""
    lateinit var mAdapter: PayRecordAdapter
    lateinit var mPinDialog: PayPsdBottomFragment
    private lateinit var mPayMethodLiveData: PayMethodLiveData

    override fun createContentView() = R.layout.payment_activity_re_send_token

    override fun initWorkspaceAction() {
        mVM = ViewModelProvider(this).get(BillPayWM::class.java)
        mPayMethodLiveData = ViewModelProvider(this).get<PayMethodLiveData>(
            PayMethodLiveData::class.java)
        mVM.payRecordLD.observe(this) {
            if (pageNum == 1) {
                srl.finishRefresh()
                mAdapter.setNewData(it.records)
            } else {
                srl.finishLoadMore()
                mAdapter.addData(it.records)
            }
        }
        mVM.resultLd.observe(this) {
            ToastUtil.show(mContext, getString(R.string.successful))
            mPinDialog.dismiss()
        }
    }

    override fun initView() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true, 0.2f)
            .init()
        initTitle()
        timeTv.text = TimeUtils.longFormat(System.currentTimeMillis())
    }

    private fun initTitle() {
        dataBinding.includeTitle.apply {
            titleTv.setText(R.string.payment_Record)
            titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
            backIv.setBackgroundResource(R.drawable.common_ic_back_black)
            statusView.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
            titleTv.setBackgroundColor(resources.getColor(R.color.color_FFFFFF))
        }

        rv.apply {
            layoutManager = LinearLayoutManager(mContext)
            mAdapter = PayRecordAdapter(object : BaseAdapterDataCallback<RecordBean> {
                override fun callBack(position: Int, bean: RecordBean) {
                    showPinDialog(bean)
                }
            })
            adapter = mAdapter
            addItemDecoration(SpacesItemDecoration(1.pt2px()))
            setEmptyView(emptyCl)
        }
    }

    override fun initListener() {
        onClickListener()
        refreshListener()
    }

    override fun initData() {
        mVM.getPayRecord(pageNum, pageSize, selectTime)
    }


    private fun refreshListener() {
        srl.setOnLoadMoreListener {
            pageNum++
            initData()
        }
        srl.setOnRefreshListener {
            pageNum = 1
            initData()
        }
    }

    private fun onClickListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    leftBg -> {
                        onBackPressed()
                    }

                    timeTv -> {
                        val dateSelectDialog =
                            DateSelectDialog(mContext, "",object : DateSelectDialog.DialogResult {
                                override fun onConfirm(year: Int, monthO: Int, date: Int) {
                                    val date = Date(year - 1900, monthO - 1, date)
                                    selectTime = TimeUtils.date2StringDMY(date).toString()
                                    timeTv.text = TimeUtils.date2StringMY(date)
                                    pageNum = 1
                                    initData()
                                }
                            })
                        dateSelectDialog.show()
                        dateSelectDialog.setTitle("")
                        dateSelectDialog.setDateSpace()
                    }
                }
            }

        }.apply {
            leftBg.setOnClickListener(this)
            timeTv.setOnClickListener(this)
        }
    }

    fun showDialog(bean: RecordBean) {
        val mDoubleDialog = DoubleDialog(mContext)
        mDoubleDialog.setDoubleCallback(object : DoubleDialog.DoubleCallback {
            override fun leftCallback() {
                mDoubleDialog.dismiss()
            }

            override fun rightCallback() {
                mDoubleDialog.dismiss()
            }
        })
        val mobile = StringUtils.stringMask(getString(R.string.jia_263),
            SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERMOBILE, "")
                .toString())
        mDoubleDialog.show()
        mDoubleDialog.setMyTitle(getString(R.string.resent_Token))
            .setContentStr(getString(R.string.the_ZESA_bill_token_has_been_sent_to) + " " + mobile)
            .setLeftStr(getString(R.string.close))
            .setLeftColor(getResources().getColor(R.color.color_F85A40))
            .setRightStr(getString(R.string.resend))
            .setRightColor(getResources().getColor(R.color.common_ye_F3881E))
    }

    private fun showPinDialog(bean: RecordBean) {
        val mobile = SharedPreferencesUtils.getParam(mContext,
            BaseConstants.SaveParameter.USERMOBILE,
            "").toString()
        val hint =
            getString(R.string.the_ZESA_bill_token_will_be_resent_to) + "\n" + StringUtils.stringMask(getString(R.string.nommal_263), mobile)
        mPinDialog = PayPsdBottomFragment.newInstance(hint)
        mPinDialog.setPinDialogCallback(object : PinDialogCallback {
            override fun pinEnd(pin: String) {
                if (pin == null) return
                mVM.reZesaToken(bean.meterNo, bean.trxAmt, "", pin = pin)
            }

            override fun onClose() {
                mPinDialog.dismiss()
                mPayMethodLiveData.mDismiss.value = false
            }

        })
        mPinDialog.show(supportFragmentManager, "")

    }

    companion object {
        fun jump(context: Activity) {
            val intent = Intent(context, ReSendTokenActivity::class.java)
            context.startActivity(intent)
        }
    }
}