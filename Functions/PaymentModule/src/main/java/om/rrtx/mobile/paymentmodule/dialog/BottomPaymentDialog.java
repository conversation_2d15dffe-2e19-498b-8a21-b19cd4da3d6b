package om.rrtx.mobile.paymentmodule.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;

import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.widget.PayPasswordView;

/**
 * <AUTHOR>
 */
public class BottomPaymentDialog extends Dialog implements PayPasswordView.Password {

    private TextView mTitleTv;
    private View mLeftBg;
    private ImageView mBackIv;
    private PayPasswordView mPayPasswordView;
    private PaymentPinCallback mPinCallback;
    private Context mContext;

    public BottomPaymentDialog(@NonNull Context context) {
        super(context, R.style.TransBottomSheetDialogStyle);
        mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.payment_dialog_payment_pin);
        setCancelable(false);
        setCanceledOnTouchOutside(false);

        setLocation();
        initView();
        initListener();
    }

    private void setLocation() {
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams WL = window.getAttributes();
            WL.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            WL.width = ViewGroup.LayoutParams.MATCH_PARENT;
            window.setGravity(Gravity.BOTTOM);
            window.setAttributes(WL);
        }
    }

    private void initView() {
        mTitleTv = findViewById(R.id.titleTv);
        mTitleTv.setTextColor(mContext.getResources().getColor(R.color.color_131313));
        mLeftBg = findViewById(R.id.leftBg);
        mBackIv = findViewById(R.id.backIv);
        mPayPasswordView = findViewById(R.id.ppv_content);
    }

    public void initListener() {
        mPayPasswordView.setPassword(this);
        //关闭对话框
        mLeftBg.setOnClickListener(view -> {
            if (mPinCallback != null) {
                mPinCallback.closed();
            }
        });
    }

    @Override
    public void passwordCallBack(String password) {
        if (mPinCallback != null) {
            mPinCallback.completed(password);
        }
    }

    @Override
    public void forgotCallBack() {
        if (mPinCallback != null) {
            mPinCallback.forgetPin();
        }
    }

    /**
     * 取消对话框上面的文字
     */
    public void cancelPassword() {
        mPayPasswordView.cancelPassword();
    }

    /**
     * 设置标题
     *
     * @param title
     */
    public BottomPaymentDialog setTitleStr(String title) {
        if (mTitleTv != null) {
            mTitleTv.setText(title);
        }
        return this;
    }

    public BottomPaymentDialog setLeftRes(int backRes) {
        if (mBackIv != null) {
            mBackIv.setImageResource(backRes);
        }
        return this;
    }


    public void setPinCallback(PaymentPinCallback pinCallback) {
        mPinCallback = pinCallback;
    }

    /**
     * 付款对话框的回调
     */
    public interface PaymentPinCallback {
        /**
         * 填写完成
         *
         * @param code code吗
         */
        void completed(String code);

        /**
         * 忘记密码
         */
        void forgetPin();

        /**
         * 关闭
         */
        void closed();
    }
}
