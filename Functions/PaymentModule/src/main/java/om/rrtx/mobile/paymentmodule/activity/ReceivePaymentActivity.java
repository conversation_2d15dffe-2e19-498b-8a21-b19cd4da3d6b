package om.rrtx.mobile.paymentmodule.activity;

import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.bumptech.glide.Glide;
import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functioncommon.dialog.CommonCurrencyDialog;
import om.rrtx.mobile.paymentmodule.PaymentConstants;
import om.rrtx.mobile.paymentmodule.R;
import om.rrtx.mobile.paymentmodule.R2;
import om.rrtx.mobile.paymentmodule.bean.QrCodeBean;
import om.rrtx.mobile.paymentmodule.presenter.ReceivePaymentPresenter;
import om.rrtx.mobile.paymentmodule.utils.QRCodeEncoder;
import om.rrtx.mobile.paymentmodule.view.ReceivePaymentView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.BitmapUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.ViThreadPoolManager;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 收付款页面
 */
@Route(path = ARouterPath.Payment.ReceivePaymentActiity)
public class ReceivePaymentActivity extends BaseSuperActivity<ReceivePaymentView, ReceivePaymentPresenter>
        implements ReceivePaymentView {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.codeIv)
    ImageView mCodeIv;
    @BindView(R2.id.setPriceTv)
    TextView mSetPriceTv;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.moneyTv)
    TextView mMoneyTv;
    @BindView(R2.id.remarkTv)
    TextView mRemarkTv;
    @BindView(R2.id.statusView)
    View mStatusView;
    @BindView(R2.id.setCurrencyTv)
    TextView mSetCurrencyTv;
    @BindView(R2.id.setAmtLl)
    LinearLayout mSetAmtLl;
    @BindView(R2.id.currencyLl)
    LinearLayout mCurrencyLl;
    @BindView(R2.id.clearAmtLl)
    LinearLayout mClearAmtLl;

    public final int REQUESTCODE = 0x1;
    private CommonCurrencyDialog mCommonCurrencyDialog;
    private Handler mHandler = new Handler(Looper.getMainLooper());

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    protected int createContentView() {
        return R.layout.payment_activity_receive_payment;
    }

    @Override
    protected ReceivePaymentPresenter createPresenter() {
        return new ReceivePaymentPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .init();

        mBackIv.setImageResource(R.drawable.payment_ic_back_w);

        mStatusView.setBackgroundColor(getResources().getColor(R.color.common_ye_F3881E));
        mTitleTv.setBackgroundColor(getResources().getColor(R.color.common_ye_F3881E));
        mTitleTv.setText(getString(R.string.receive_Payment));

        mSetCurrencyTv.setText(CurrencyUtils.setCurrency(this, "ZWG"));
    }

    @Override
    public void initDate() {
        super.initDate();
        //获取二维码
        mPresenter.requestQrCode("", "", "");
    }

    @Override
    public void refreshPager() {
        //获取二维码
        mPresenter.requestQrCode("", "", "");
    }

    /**
     * 创建并显示二维码
     *
     * @param codeStr 相应二维码内容
     */
    public void createCode(String codeStr) {
        ViThreadPoolManager.getInstance().execute(new Runnable() {

            private Bitmap mQrCodeBitmap;

            @Override
            public void run() {
                String userHeard = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERHEARD, "");
                Bitmap bitmap;
                try {
                    bitmap = Glide.with(mContext).asBitmap().load(userHeard).centerCrop().into(
                                    (int) mContext.getResources().getDimension(R.dimen.payment_scan_qr_code_price_size),
                                    (int) mContext.getResources().getDimension(R.dimen.payment_scan_qr_code_price_size))
                            .get();
                } catch (Exception e) {
                    e.printStackTrace();
                    bitmap = BitmapUtils.svgDrawableToBitmap(getDrawable(R.drawable.ic_contact_head));
                }

                bitmap = BitmapUtils.getRoundedCornerBitmap(bitmap, mContext.getResources().getDimension(R.dimen.payment_scan_qr_code_price_size));

                mQrCodeBitmap = QRCodeEncoder.syncEncodeQRCode(codeStr,
                        (int) mContext.getResources().getDimension(R.dimen.payment_qr_code_size),
                        mContext.getResources().getColor(R.color.color_131313),
                        mContext.getResources().getColor(R.color.color_FFFFFF),
                        bitmap);

                mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        mCodeIv.setImageBitmap(mQrCodeBitmap);
                    }
                });
            }
        });


    }

    @Override
    public void initListener() {
        super.initListener();
        mLeftBg.setOnClickListener(mReceivePaymentCustom);
        mSetAmtLl.setOnClickListener(mReceivePaymentCustom);
        mCurrencyLl.setOnClickListener(mReceivePaymentCustom);
        mClearAmtLl.setOnClickListener(mReceivePaymentCustom);
    }

    private CustomClickListener mReceivePaymentCustom = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.setAmtLl) {
                Intent intent = new Intent(mContext, SetPriceActivity.class);
                intent.putExtra(PaymentConstants.Transmit.CURRENCY, mSetCurrencyTv.getText().toString());
                startActivityForResult(intent, REQUESTCODE);
            } else if (view.getId() == R.id.leftBg) {
                finish();
            } else if (view.getId() == R.id.currencyLl) {
                mCommonCurrencyDialog = new CommonCurrencyDialog(ReceivePaymentActivity.this);
                mCommonCurrencyDialog.setOnCurrencyClickListener(new CommonCurrencyDialog.OnCurrencyClickListener() {
                    @Override
                    public void onCurrencyClick(String currency) {
                        // 赋值货币符号 请求二维码接口
                        String price = mMoneyTv.getText().toString();
                        String remark = mRemarkTv.getText().toString();
                        //if (!TextUtils.isEmpty(price)) {
                            //请求
                            mPresenter.requestQrCode(price, remark, currency);
                        //}
                        mSetCurrencyTv.setText(currency);
                        if (mCommonCurrencyDialog != null) {
                            mCommonCurrencyDialog = null;
                        }
                    }
                });
                if (mCommonCurrencyDialog != null) {
                    mCommonCurrencyDialog.show();
                }
            } else if (view.getId() == R.id.clearAmtLl) {
                mPresenter.requestQrCode("", "", "");
                mMoneyTv.setText("");
                //设置显示
                mMoneyTv.setVisibility(View.GONE);
                mRemarkTv.setVisibility(View.GONE);
                mClearAmtLl.setVisibility(View.GONE);
            }
        }
    };

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUESTCODE && resultCode == Activity.RESULT_OK) {
            if (data != null) {
                String price = data.getStringExtra(PaymentConstants.Transmit.PRICE);
                String remark = data.getStringExtra(PaymentConstants.Transmit.REMARK);

                //设置文案
                mMoneyTv.setVisibility(View.VISIBLE);
                mRemarkTv.setVisibility(View.VISIBLE);
                String currencyStr = mSetCurrencyTv.getText().toString();
                mMoneyTv.setText(CurrencyUtils.setCurrency(mContext, currencyStr) + " " + price);
                mRemarkTv.setText(remark);
                mClearAmtLl.setVisibility(View.VISIBLE);
                //请求
                mPresenter.requestQrCode(price, remark, currencyStr);
            }
        }
    }

    @Override
    public void qrCodeSuccess(QrCodeBean qrCodeBean) {
        //获取二维码成功
        if (qrCodeBean != null && !TextUtils.isEmpty(qrCodeBean.getQrCode())) {
            createCode(qrCodeBean.getQrCode());
        }
    }

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }
}
