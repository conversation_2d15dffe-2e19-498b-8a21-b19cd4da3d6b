package om.rrtx.mobile.paymentmodule.viewModel

import android.app.Application
import android.text.TextUtils
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import om.rrtx.mobile.functioncommon.bean.BillCodeBean
import om.rrtx.mobile.functioncommon.bean.OrderInfoBean
import om.rrtx.mobile.functioncommon.model.CommonModel
import om.rrtx.mobile.paymentmodule.bean.MakePaymentCodeBean
import om.rrtx.mobile.paymentmodule.bean.PubBean
import om.rrtx.mobile.paymentmodule.model.PaymentModel
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.bean.CodeOrderBean
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver
import om.rrtx.mobile.rrtxcommon1.net.BaseObserverNoError
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil

class MakePaymentWM(application: Application) : AndroidViewModel(application) {
    val mPaymentModel = PaymentModel()
    val checkMerchantCodeSuccess = MutableLiveData<BillCodeBean>()
    val makePaySuccess = MutableLiveData<MakePaymentCodeBean>()
    val codeOrderSuccess = MutableLiveData<CodeOrderBean>()
    val failResult = MutableLiveData<String>()
    val loopFailResult = MutableLiveData<String>()
    val orderInfoBeanLD = MutableLiveData<OrderInfoBean>()

    /**
     * 获取请求付款二维码
     */
    fun requestMakePaymentCode() {
        val mContext = ActivityController.getInstance().currentActivity()
        val pubLick = SharedPreferencesUtils.getParam(
            mContext,
            BaseConstants.SaveParameter.PUBLICKEY,
            ""
        ) as String
        val userId = SharedPreferencesUtils.getParam(
            mContext,
            BaseConstants.SaveParameter.USERID,
            ""
        ) as String
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestMakePaymentCode(
                userId,
                object : BaseObserver<MakePaymentCodeBean>(mContext) {
                    override fun requestSuccess(sResData: MakePaymentCodeBean) {
                        makePaySuccess.value = sResData
                    }

                    override fun requestFail(sResMsg: String) {
                        failResult.value = sResMsg
                    }
                })
        } else {
            mPaymentModel.commonPub(object : BaseObserver<PubBean>(mContext) {
                override fun requestSuccess(sResData: PubBean) {
                    SharedPreferencesUtils.setParam(
                        mContext,
                        BaseConstants.SaveParameter.PUBLICKEY,
                        sResData.pubKeyBase64
                    )
                    mPaymentModel.requestMakePaymentCode(
                        userId,
                        object : BaseObserver<MakePaymentCodeBean>(mContext) {

                            override fun requestSuccess(sResData: MakePaymentCodeBean) {
                                makePaySuccess.value = sResData
                            }

                            override fun requestFail(sResMsg: String) {
                                failResult.value = sResMsg
                            }
                        })
                }

                override fun requestFail(sResMsg: String) {
                    failResult.value = sResMsg
                }
            })
        }
    }

    /**
     * 获取请求付款二维码
     */
    fun requestPayCheck(code: String) {
        val mContext = ActivityController.getInstance().currentActivity()
        val pubLick = SharedPreferencesUtils.getParam(
            mContext,
            BaseConstants.SaveParameter.PUBLICKEY,
            ""
        ) as String
        val userId = SharedPreferencesUtils.getParam(
            mContext,
            BaseConstants.SaveParameter.USERID,
            ""
        ) as String
        if (!TextUtils.isEmpty(pubLick)) {
            mPaymentModel.requestPayCheck(
                userId,
                code,
                object : BaseNoDialogObserver<CodeOrderBean>(mContext) {
                    override fun requestFail(sResMsg: String) {
                        loopFailResult.value = sResMsg
                    }

                    override fun requestSuccess(sResData: CodeOrderBean) {
                        codeOrderSuccess.value = sResData
                    }
                })
        } else {
            mPaymentModel.commonPub(object : BaseObserver<PubBean>(mContext) {
                override fun requestSuccess(sResData: PubBean) {
                    SharedPreferencesUtils.setParam(
                        mContext,
                        BaseConstants.SaveParameter.PUBLICKEY,
                        sResData.pubKeyBase64
                    )
                    mPaymentModel.requestPayCheck(
                        userId,
                        code,
                        object : BaseNoDialogObserver<CodeOrderBean>(mContext) {
                            override fun requestFail(sResMsg: String) {
                                loopFailResult.value = sResMsg
                            }

                            override fun requestSuccess(sResData: CodeOrderBean) {
                                codeOrderSuccess.value = sResData
                            }
                        })
                }

                override fun requestFail(sResMsg: String) {
                    failResult.value = sResMsg
                }
            })
        }
    }

    /**根据商户汇款码查询商户交易名称**/
    fun merchantCodeCheck(merchantCode: String) {
        val activity = ActivityController.getInstance().currentActivity()
        mPaymentModel.merchantCodeCheck(merchantCode, object : BaseNoDialogObserver<BillCodeBean>(activity) {
            override fun requestSuccess(bean: BillCodeBean) {
                checkMerchantCodeSuccess.value = bean
            }

            override fun requestFail(sResMsg: String) {
                ToastUtil.show(activity,sResMsg)
            }
        })
    }

    /**创建汇款吗付款订单**/
    fun checkMerCodePayOrder(merchantCode: String, amt: String, currency: String) {
        val currentActivity = ActivityController.getInstance().currentActivity()

        CommonModel().requestMerCodePayOrder(merchantCode,
            amt,
            currency,
            object : BaseObserverNoError<OrderInfoBean>(currentActivity) {
                override fun requestSuccess(bean: OrderInfoBean) {
                    orderInfoBeanLD.value = bean
                }
            })
    }
}