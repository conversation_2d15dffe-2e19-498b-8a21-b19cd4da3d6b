<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="authenticationOpt"
            type="com.rrtx.facelib.acticity.AuthenticationHintActivity.AuthenticationOpt" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/color_FFFFFF">

        <include
            android:id="@+id/include_title"
            layout="@layout/face_base_title"
            app:baseTitle="@{authenticationOpt}"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="50pt"
            android:text="@string/face_check_hint"
            android:textColor="@color/face_color_9d9d9d"
            android:textSize="26pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/include_title" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/standardIv"
            android:layout_width="261pt"
            android:layout_height="175pt"
            android:layout_marginTop="43pt"
            android:src="@drawable/face_ic_standard"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/incompleteIv"
            app:layout_constraintTop_toBottomOf="@id/hint" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/standardHintIv"
            android:layout_width="30pt"
            android:layout_height="30pt"
            android:layout_marginTop="19pt"
            android:src="@drawable/face_iv_right"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="@id/standardIv"
            app:layout_constraintRight_toLeftOf="@id/standardTv"
            app:layout_constraintTop_toBottomOf="@id/standardIv" />

        <TextView
            android:id="@+id/standardTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10pt"
            android:text="@string/standard"
            android:textColor="@color/color_131313"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/standardHintIv"
            app:layout_constraintLeft_toRightOf="@id/standardHintIv"
            app:layout_constraintRight_toRightOf="@id/standardIv"
            app:layout_constraintTop_toTopOf="@id/standardHintIv" />


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/incompleteIv"
            android:layout_width="261pt"
            android:layout_height="175pt"
            android:layout_marginTop="43pt"
            android:src="@drawable/face_ic_incomplete"
            app:layout_constraintLeft_toRightOf="@id/standardIv"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/hint" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/incompleteHintIv"
            android:layout_width="30pt"
            android:layout_height="30pt"
            android:layout_marginTop="19pt"
            android:src="@drawable/face_iv_error"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="@id/incompleteIv"
            app:layout_constraintRight_toLeftOf="@id/incompleteTv"
            app:layout_constraintTop_toBottomOf="@id/incompleteIv" />

        <TextView
            android:id="@+id/incompleteTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10pt"
            android:text="@string/incomplete"
            android:textColor="@color/color_131313"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/incompleteHintIv"
            app:layout_constraintLeft_toRightOf="@id/incompleteHintIv"
            app:layout_constraintRight_toRightOf="@id/incompleteIv"
            app:layout_constraintTop_toTopOf="@id/incompleteHintIv" />


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/indistinctIv"
            android:layout_width="261pt"
            android:layout_height="175pt"
            android:layout_marginTop="43pt"
            android:src="@drawable/face_indistinct"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/reflectiveIv"
            app:layout_constraintTop_toBottomOf="@id/standardTv" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/indistinctHintIv"
            android:layout_width="30pt"
            android:layout_height="30pt"
            android:layout_marginTop="19pt"
            android:src="@drawable/face_iv_error"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="@id/indistinctIv"
            app:layout_constraintRight_toLeftOf="@id/indistinctTv"
            app:layout_constraintTop_toBottomOf="@id/indistinctIv" />

        <TextView
            android:id="@+id/indistinctTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10pt"
            android:text="@string/indistinct"
            android:textColor="@color/color_131313"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/indistinctHintIv"
            app:layout_constraintLeft_toRightOf="@id/indistinctHintIv"
            app:layout_constraintRight_toRightOf="@id/indistinctIv"
            app:layout_constraintTop_toTopOf="@id/indistinctHintIv" />


        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/reflectiveIv"
            android:layout_width="261pt"
            android:layout_height="175pt"
            android:layout_marginTop="43pt"
            android:src="@drawable/face_reflective"
            app:layout_constraintLeft_toRightOf="@id/indistinctIv"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/standardTv" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/reflectiveHintIv"
            android:layout_width="30pt"
            android:layout_height="30pt"
            android:layout_marginTop="19pt"
            android:src="@drawable/face_iv_error"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintLeft_toLeftOf="@id/reflectiveIv"
            app:layout_constraintRight_toLeftOf="@id/reflectiveTv"
            app:layout_constraintTop_toBottomOf="@id/reflectiveIv" />

        <TextView
            android:id="@+id/reflectiveTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="10pt"
            android:text="@string/reflective"
            android:textColor="@color/color_131313"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/reflectiveHintIv"
            app:layout_constraintLeft_toRightOf="@id/reflectiveHintIv"
            app:layout_constraintRight_toRightOf="@id/reflectiveIv"
            app:layout_constraintTop_toTopOf="@id/reflectiveHintIv" />


        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="690pt"
            android:layout_height="80pt"
            android:layout_marginTop="200pt"
            android:background="@drawable/common_usable_btn"
            android:gravity="center"
            android:onClick="@{()->authenticationOpt.scanCertificate()}"
            android:text="@string/scan_certificate"
            android:textColor="@color/color_FFFFFF"
            android:textSize="32pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/indistinctTv" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>