<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/color_FFFFFF">

    <!--titlebar-->
    <RelativeLayout
        android:id="@+id/relative_collect_failure"
        android:layout_width="match_parent"
        android:layout_height="@dimen/face_title_height"
        android:background="@color/face_title_bar">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/verify_title_txt"
            android:textColor="@color/color_000000"
            android:textSize="@dimen/face_title_font" />
    </RelativeLayout>

    <View
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/face_title_line_height"
        android:layout_below="@id/relative_collect_failure"
        android:background="@color/face_title_line_color" />

    <ImageView
        android:id="@+id/image_failure_icon"
        android:layout_width="76dp"
        android:layout_height="76dp"
        android:layout_marginTop="88dp"
        android:layout_below="@id/view"
        android:src="@drawable/icon_verify_fail"
        android:layout_centerHorizontal="true"/>

    <TextView
        android:id="@+id/text_err_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tips3"
        android:textSize="@dimen/face_verify_font"
        android:layout_centerHorizontal="true"
        android:textColor="@color/color_000000"
        android:layout_marginTop="30dp"
        android:layout_below="@+id/image_failure_icon"/>

    <TextView
        android:id="@+id/text_err_tips"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/please_again"
        android:textSize="@dimen/face_verify_error_font"
        android:layout_centerHorizontal="true"
        android:textColor="@color/face_verify_result_txt"
        android:layout_marginTop="12dp"
        android:layout_below="@+id/text_err_message"/>

    <!--底部文字-->
    <TextView
        android:id="@+id/text_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/collect_bottom_txt"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="15dp"
        android:textSize="@dimen/face_collect_bottom_font"
        android:textColor="@color/face_collect_bottom_color"/>

    <Button
        android:id="@+id/btn_return_home"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_above="@+id/text_bottom"
        android:text="@string/verify_exit"
        android:textColor="#333333"
        android:textSize="18sp"
        android:layout_marginBottom="53dp"
        android:onClick="onCloseVerify"
        android:background="@drawable/success_button_return_selector"/>

    <Button
        android:id="@+id/btn_recollect"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:textSize="18sp"
        android:text="@string/collect_again"
        android:textColor="#FFFFFF"
        android:layout_above="@+id/btn_return_home"
        android:layout_centerHorizontal="true"
        android:onClick="onRecollect"
        android:background="@drawable/success_button_recollect_selector"/>

</RelativeLayout>