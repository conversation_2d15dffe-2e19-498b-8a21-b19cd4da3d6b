<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/color_FFFFFF">

    <!--titlebar-->
    <RelativeLayout
        android:id="@+id/relative_collect_success"
        android:layout_width="match_parent"
        android:layout_height="@dimen/face_title_height"
        android:background="@color/face_title_bar">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/verify_title_txt"
            android:textColor="@color/color_000000"
            android:textSize="@dimen/face_title_font" />
    </RelativeLayout>

    <View
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/face_title_line_height"
        android:layout_below="@id/relative_collect_success"
        android:background="@color/face_title_line_color" />


    <ImageView
        android:id="@+id/image_success"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/view"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="88dp"
        android:src="@drawable/icon_mask_success"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/tips6"
        android:textSize="20sp"
        android:layout_centerHorizontal="true"
        android:textColor="#000000"
        android:layout_marginTop="32dp"
        android:layout_below="@+id/image_success"/>

    <!--底部背景及文字-->
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/icon_collect_bottom"
        android:visibility="gone"
        android:layout_alignParentBottom="true"/>

    <TextView
        android:id="@+id/text_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/collect_bottom_txt"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="15dp"
        android:textSize="@dimen/face_collect_bottom_font"
        android:textColor="@color/face_collect_bottom_color"/>

    <Button
        android:id="@+id/btn_return_home"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_above="@+id/text_bottom"
        android:text="@string/closed"
        android:textColor="#333333"
        android:textSize="18sp"
        android:layout_marginBottom="53dp"
        android:onClick="onClose"
        android:background="@drawable/success_button_return_selector"/>

</RelativeLayout>