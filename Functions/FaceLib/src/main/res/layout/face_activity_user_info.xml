<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="userInfoOpt"
            type="com.rrtx.facelib.acticity.UserInfoActivity.UserInfoOpt" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".acticity.UserInfoActivity">

        <include
            android:id="@+id/include_title"
            layout="@layout/face_base_title" />

        <TextView
            android:id="@+id/hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="26pt"
            android:text="@string/confirm_info"
            android:textColor="@color/color_9B9B9B"
            android:textSize="26pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/include_title" />

        <androidx.cardview.widget.CardView
            android:id="@+id/showInfo"
            android:layout_width="690pt"
            android:layout_height="wrap_content"
            android:layout_marginTop="20pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/hint">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="20pt">

                <TextView
                    android:id="@+id/name"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="30pt"
                    android:text="@string/face_name"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="20pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/nameTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="5pt"
                    android:textColor="@color/color_131313"
                    android:textSize="28pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/name" />

                <TextView
                    android:id="@+id/id"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="10pt"
                    android:text="@string/id_number"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="20pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/nameTv" />

                <TextView
                    android:id="@+id/idTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="5pt"
                    android:textColor="@color/color_131313"
                    android:textSize="28pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/id" />

                <TextView
                    android:id="@+id/valid"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="10pt"
                    android:text="@string/valid_period"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="20pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/idTv" />

                <TextView
                    android:id="@+id/validTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="5pt"
                    android:textColor="@color/color_131313"
                    android:textSize="28pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/valid" />


                <TextView
                    android:id="@+id/gender"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="10pt"
                    android:text="@string/gender"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="20pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/validTv" />

                <TextView
                    android:id="@+id/genderTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="5pt"
                    android:textColor="@color/color_131313"
                    android:textSize="28pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/gender" />

                <TextView
                    android:id="@+id/address"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="10pt"
                    android:text="@string/address"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="20pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/genderTv" />

                <TextView
                    android:id="@+id/addressTv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="5pt"
                    android:layout_marginRight="30pt"
                    android:layout_marginBottom="30pt"
                    android:textColor="@color/color_131313"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/address" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/nextTv"
            android:layout_width="690pt"
            android:layout_height="80pt"
            android:layout_marginTop="60pt"
            android:background="@drawable/common_usable_btn"
            android:gravity="center"
            android:onClick="@{()->userInfoOpt.next()}"
            android:text="@string/next"
            android:textColor="@color/color_FFFFFF"
            android:textSize="32pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/showInfo" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
