<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="takePicOpt"
            type="com.rrtx.facelib.acticity.TakePicturesActivity.TakePicOpt" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/face_color_464646"
        tools:context=".acticity.TakePicturesActivity">

        <include
            android:id="@+id/include_title"
            layout="@layout/face_base_title"
            app:baseTitle="@{takePicOpt}" />

        <TextView
            android:id="@+id/hint1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="50pt"
            android:gravity="center"
            android:text="@string/face_hint3"
            android:textColor="@color/color_FFFFFF"
            android:textSize="28pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/include_title" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/tack_pic_bg"
            android:layout_width="608pt"
            android:layout_height="397pt"
            android:layout_marginTop="40pt"
            android:background="@drawable/face_ic_tack_pic_bg"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/hint1" />

        <androidx.camera.view.PreviewView
            android:id="@+id/picSur"
            android:layout_width="581pt"
            android:layout_height="368pt"
            app:layout_constraintBottom_toBottomOf="@id/tack_pic_bg"
            app:layout_constraintLeft_toLeftOf="@id/tack_pic_bg"
            app:layout_constraintRight_toRightOf="@id/tack_pic_bg"
            app:layout_constraintTop_toTopOf="@id/tack_pic_bg" />

        <TextView
            android:id="@+id/hint2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="90pt"
            android:layout_marginTop="50pt"
            android:layout_marginRight="90pt"
            android:text="@string/face_hint4"
            android:textColor="@color/color_FFFFFF"
            android:textSize="28pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tack_pic_bg" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/takePicIv"
            android:layout_width="88pt"
            android:layout_height="88pt"
            android:layout_marginTop="50pt"
            android:onClick="@{()->takePicOpt.takePic()}"
            android:src="@drawable/face_btn_take_pic"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/hint2" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>