<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/liveness_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/color_FFFFFF">

    <FrameLayout
        android:id="@+id/liveness_surface_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"/>

    <com.rrtx.facelib.widget.FaceDetectRoundView
        android:id="@+id/liveness_face_round"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <!--用于承载ImageView-->
    <RelativeLayout
        android:id="@+id/relative_add_image_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="invisible"/>

    <!--titlebar-->
    <RelativeLayout
        android:id="@+id/relative_detect"
        android:layout_width="match_parent"
        android:layout_height="@dimen/face_title_height"
        android:background="@color/face_title_bar">

        <ImageView
            android:id="@+id/liveness_close"
            android:layout_width="@dimen/face_title_height"
            android:layout_height="@dimen/face_title_height"
            android:layout_centerVertical="true"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:layout_marginLeft="6dp"
            android:src="@drawable/setting_image_close_selector" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/verify_title_txt"
            android:textColor="@color/color_000000"
            android:textSize="@dimen/face_title_font" />

        <ImageView
            android:id="@+id/liveness_sound"
            android:layout_width="@dimen/face_title_height"
            android:layout_height="@dimen/face_title_height"
            android:layout_alignParentRight="true"
            android:layout_centerVertical="true"
            android:layout_marginRight="17dp"
            android:paddingLeft="10dp"
            android:paddingRight="10dp"
            android:src="@drawable/icon_titlebar_voice2" />
    </RelativeLayout>

    <View
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/face_title_line_height"
        android:layout_below="@id/relative_detect"
        android:background="@color/face_title_line_color" />

    <!--底部文字-->
    <ImageView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/icon_collect_bottom"
        android:visibility="gone"
        android:layout_alignParentBottom="true"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/collect_bottom_txt"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="15dp"
        android:textSize="@dimen/face_collect_bottom_font"
        android:textColor="@color/face_collect_bottom_color"/>

    <HorizontalScrollView
        android:id="@+id/horizon1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true">

        <LinearLayout
            android:id="@+id/liveness_result_image_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"/>
    </HorizontalScrollView>

    <HorizontalScrollView
        android:id="@+id/horizon2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/horizon1"
        android:layout_marginBottom="5dp">

        <LinearLayout
            android:id="@+id/liveness_result_image_layout2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="horizontal"/>
    </HorizontalScrollView>

    <View
        android:id="@+id/view_live_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#7000"
        android:visibility="gone"/>

</RelativeLayout>