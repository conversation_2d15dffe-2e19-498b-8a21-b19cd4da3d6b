<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_FFFFFF"
    android:fitsSystemWindows="true">

    <!--titlebar-->
    <RelativeLayout
        android:id="@+id/relative_idcard"
        android:layout_width="match_parent"
        android:layout_height="@dimen/face_title_height"
        android:background="@color/face_title_bar">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:text="@string/verify_title_txt"
            android:textColor="@color/color_000000"
            android:textSize="@dimen/face_title_font" />
    </RelativeLayout>

    <View
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/face_title_line_height"
        android:layout_below="@id/relative_idcard"
        android:background="@color/face_title_line_color" />

    <ImageView
        android:id="@+id/image_anim"
        android:layout_width="76dp"
        android:layout_height="76dp"
        android:layout_below="@id/view"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="88dp"
        android:src="@drawable/icon_loading" />

    <TextView
        android:id="@+id/text_net_error"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/image_anim"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="30dp"
        android:text="@string/verify_net_error_txt"
        android:textColor="@color/color_000000"
        android:textSize="@dimen/face_verify_font"
        android:visibility="gone" />

    <TextView
        android:id="@+id/text_check_net"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/text_net_error"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="12dp"
        android:text="@string/verify_check_net_txt"
        android:textColor="@color/color_000000"
        android:textSize="@dimen/face_verify_error_font"
        android:visibility="gone" />

    <TextView
        android:id="@+id/text_verify_ing"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/image_anim"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="32dp"
        android:text="@string/verify_ing_txt"
        android:textColor="@color/face_verify_result_txt"
        android:textSize="@dimen/face_verify_error_font" />

    <TextView
        android:id="@+id/text_verify_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="15dp"
        android:text="@string/collect_bottom_txt"
        android:textColor="@color/face_collect_bottom_color"
        android:textSize="@dimen/face_collect_bottom_font" />

    <Button
        android:id="@+id/btn_return_home"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/text_verify_bottom"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="53dp"
        android:background="@drawable/success_button_return_selector"
        android:onClick="onReturnHome"
        android:text="@string/verify_exit"
        android:textColor="#333333"
        android:textSize="18sp"
        android:visibility="gone" />

    <Button
        android:id="@+id/btn_retry"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_above="@id/btn_return_home"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="20dp"
        android:background="@drawable/success_button_recollect_selector"
        android:onClick="onRetry"
        android:text="@string/loading_again"
        android:textColor="#FFFFFF"
        android:textSize="18sp"
        android:visibility="gone" />

</RelativeLayout>