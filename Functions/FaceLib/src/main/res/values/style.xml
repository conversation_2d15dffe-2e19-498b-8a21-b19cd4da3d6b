<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="face_baseTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/face_color_274F9B</item>
        <item name="colorPrimaryDark">@color/face_color_274F9B</item>
        <item name="colorAccent">@color/face_color_274F9B</item>
    </style>

    <style name="face_appThemeNoState" parent="face_baseTheme">
        <!--默认的颜色-->
        <item name="colorControlNormal">@color/face_color_274F9B</item>
        <!--整体的颜色-->
        <item name="colorAccent">@color/face_color_274F9B</item>
        <!--文字的提示颜色-->
        <item name="android:textColorHint">@color/face_color_274F9B</item>
    </style>
</resources>