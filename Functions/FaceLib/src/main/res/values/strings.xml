<resources>
    <string name="detect_no_face">未检测到人脸</string>
    <string name="detect_face_in">请将脸移入取景框</string>
    <string name="detect_zoom_in">请将脸部靠近一点</string>
    <string name="detect_zoom_out">请将脸部离远一点</string>
    <string name="detect_head_up">请略微抬头</string>
    <string name="detect_head_down">请略微低头</string>
    <string name="detect_head_left">请略微向左转头</string>
    <string name="detect_head_right">请略微向右转头</string>
    <string name="detect_occ_face">脸部有遮挡</string>
    <string name="detect_occ_left_eye">左眼有遮挡</string>
    <string name="detect_occ_right_eye">右眼有遮挡</string>
    <string name="detect_occ_nose">鼻子有遮挡</string>
    <string name="detect_occ_mouth">嘴部有遮挡</string>
    <string name="detect_occ_left_check">左脸颊有遮挡</string>
    <string name="detect_occ_right_check">右脸颊有遮挡</string>
    <string name="detect_occ_chin">下巴有遮挡</string>
    <string name="detect_low_light">请使环境光线再亮些</string>
    <string name="detect_keep">请握稳手机，视线正对屏幕</string>
    <string name="detect_standard">请正对手机</string>
    <string name="detect_timeout">检测超时，请按照提示重试</string>
    <string name="detect_left_eye_close">左眼未睁开</string>
    <string name="detect_right_eye_close">右眼未睁开</string>
    <string name="liveness_eye">眨眨眼</string>
    <string name="liveness_eye_left">请眨眨左边眼睛</string>
    <string name="liveness_eye_right">请眨眨右边眼睛</string>
    <string name="liveness_mouth">张张嘴</string>
    <string name="liveness_head_left">向左缓慢转头</string>
    <string name="liveness_head_right">向右缓慢转头</string>
    <string name="liveness_head_left_right">左右摇头</string>
    <string name="liveness_head_up">缓慢抬头</string>
    <string name="liveness_head_down">缓慢低头</string>
    <string name="liveness_good">请保持正脸</string>

    <!--home string-->
    <string name="home_greet_txt">为保证您的信息安全</string>
    <string name="home_greet_sdk_txt">请进行人脸识别认证</string>
    <string name="home_light_txt">识别光线适中</string>
    <string name="home_light_explain_txt">请保证光线不要过暗或过亮</string>
    <string name="home_handset_txt">正面对准手机</string>
    <string name="home_handset_explain_txt">保持您的脸出现在取景框内</string>
    <string name="home_mask_txt">确保本人操作</string>
    <string name="home_mask_explain_txt">非本人操作将无法通过认证</string>
    <string name="home_but_txt">开始身份认证</string>

    <!--home agreement string-->
    <string name="home_agreement_titlebar_txt">实名认证用户隐私协议</string>
    <string name="home_agreement_functional_txt">功能说明</string>
    <string name="home_agreement_functional_explain_txt">
        为保障用户账户的安全，提供更好的服务，在提供部分产品及服务之前,
        采用人脸实名认证功能对用户的身份进行认证，
        用于验证操作人是否为账户持有者本人，
        通过人脸识别结果评估是否为用户提供后续产品或服务。
    </string>
    <string name="home_agreement_functional_explain_old_txt">
        该功能会请求权威数据源进行身份信息确认。
    </string>
    <string name="home_agreement_permission_txt">授权与许可</string>
    <string name="home_agreement_permission_explain_txt">
        如您点击“开始认证”或以其他方式选择接受本协议规则，则视为您在使用人脸识别服务时，
        同意并授权、获取、使用您在申请过程中所提供的个人信息。
    </string>
    <string name="home_agreement_rmation_security_txt">信息安全声明</string>
    <string name="home_agreement_rmation_security_explaoin_txt">
        承诺对您的个人信息严格保密，并基于国家监管部门认可的加密算法进行数据加密传输，数据加密存储，
        承诺尽到信息安全保护义务。
    </string>

    <!--setting string-->
    <string name="setting_titlebar_txt">设置</string>
    <string name="setting_prompt_txt">提示: 正式使用时，开发者可将前端设置功能隐藏</string>
    <string name="setting_announcements_txt">语音播报</string>
    <string name="setting_live_detect_txt">活体检测</string>
    <string name="setting_actionlive_txt">活体动作顺序随机</string>
    <string name="setting_actionlive_blink_txt">眨眨眼</string>
    <string name="setting_actionlive_shake_head_txt">左右摇头</string>
    <string name="setting_actionlive_turn_left_txt">向左摇头</string>
    <string name="setting_actionlive_turn_right_txt">向右摇头</string>
    <string name="setting_actionlive_nod_txt">向下低头</string>
    <string name="setting_actionlive_look_up_txt">向上抬头</string>
    <string name="setting_actionlive_open_mouth_txt">张张嘴</string>
    <string name="collect_bottom_txt">— 锐融技术支持 —</string>

    <!--身份证信息-->
    <string name="id_card_title_txt">身份信息采集</string>
    <string name="id_card_title_confirm_txt">确认个人信息</string>
    <string name="id_card_name_txt">姓\t\t\t\t\t\t名</string>
    <string name="id_card_num_txt">身份证号</string>
    <string name="id_card_input_txt">输入身份证姓名和号码</string>
    <string name="id_card_invalid_txt">身份信息填写不合法</string>
    <string name="id_card_retake_txt">重新拍摄</string>
    <string name="id_card_next_txt">进行身份核验</string>

    <string name="verify_title_txt">人脸身份核验</string>
    <string name="verify_ing_txt">身份核验中...</string>
    <string name="verify_check_net_txt">请检查网络</string>
    <string name="verify_net_error_txt">网络连接失败</string>
    <string name="closed">关闭</string>
    <string name="tips1">公安网不存在或质量低</string>
    <string name="tips2">请到派出所更新身份证图片</string>
    <string name="tips3">身份核验失败</string>
    <string name="tips4">未检测到人脸 请确保正脸采集且清晰完整</string>
    <string name="tips5">请参考API说明文档，修改参数</string>
    <string name="tips6">身份核验成功</string>
    <string name="please_again">请重新尝试</string>
    <string name="params_error">参数格式错误</string>
    <string name="verify_error">校验异常</string>
    <string name="verify_exit">退出核验</string>
    <string name="collect_again">重新采集</string>
    <string name="loading_again">重新加载</string>
    <string name="face_timeout">人脸采集超时</string>

</resources>
