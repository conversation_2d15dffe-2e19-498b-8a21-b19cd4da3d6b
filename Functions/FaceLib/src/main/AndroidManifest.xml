<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.rrtx.facelib">

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.MOUNT_UNMOUNT_FILESYSTEMS" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.hardware.camera.autofocus" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />

    <application>
        <activity
            android:name=".acticity.AuthenticationHintActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".acticity.TakePicturesActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".acticity.ShowUserPhotoActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" /> <!-- 采集请求失败页面 -->
        <activity
            android:name=".acticity.CollectFailureActivity"
            android:screenOrientation="portrait"
            android:theme="@style/face_Theme_NoTitle" /> <!-- 采集请求失败页面 -->
        <!-- 采集成功页面 -->
        <activity
            android:name=".acticity.CollectionSuccessExpActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState" /> <!-- 活体图像采集界面 -->
        <activity
            android:name=".acticity.FaceLivenessExpActivity"
            android:hardwareAccelerated="true"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState" /> <!-- 人证核验请求页面 -->
        <activity
            android:name=".acticity.CollectVerifyActivity"
            android:hardwareAccelerated="true"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState" /> <!-- 活体图像采集界面 -->
        <activity
            android:name=".acticity.IdentitySuccessActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".acticity.UserInfoActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".acticity.FaceAuthActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
    </application>

</manifest>