package com.rrtx.facelib;

import com.rrtx.facelib.bean.CheckBean;

import java.util.List;

import io.reactivex.Observable;
import okhttp3.MultipartBody;
import retrofit2.Response;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;

/**
 * <AUTHOR>
 * 人脸是被的service
 */
public interface FaceCheckService {

    /**
     * 校验图片
     *
     * @return 相应的Observable
     */
    @Multipart
    @POST(FaceConfig.URL.CHECKIMG)
    Observable<Response<CheckBean>> checkImg(@Part List<MultipartBody.Part> list);
}
