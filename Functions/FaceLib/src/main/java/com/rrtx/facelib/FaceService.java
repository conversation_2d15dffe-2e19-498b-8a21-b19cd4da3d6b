package com.rrtx.facelib;

import com.rrtx.facelib.bean.PhotoBean;

import io.reactivex.Observable;
import okhttp3.MultipartBody;
import retrofit2.Response;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;

/**
 * <AUTHOR>
 * 人脸是被的service
 */
public interface FaceService {
    /**
     * 上传图片
     *
     * @return 相应的Observable
     */
    @Multipart
    @POST(FaceConfig.URL.UPLOADPHOTO)
    Observable<Response<PhotoBean>> uploadImg(@Part MultipartBody.Part body);
}
