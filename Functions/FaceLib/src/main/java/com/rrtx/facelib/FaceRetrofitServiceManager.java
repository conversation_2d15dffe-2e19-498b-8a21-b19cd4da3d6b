package com.rrtx.facelib;

import android.text.TextUtils;

import java.io.InputStream;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSession;
import javax.net.ssl.SSLSocketFactory;

import okhttp3.ConnectionPool;
import okhttp3.ConnectionSpec;
import okhttp3.CookieJar;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import om.rrtx.mobile.rrtxcommon1.net.HttpsTrustManager;
import om.rrtx.mobile.rrtxcommon1.net.RetrofitConfig;
import om.rrtx.mobile.rrtxcommon1.net.SslUtils;
import om.rrtx.mobile.rrtxcommon1.net.StringConverterFactory;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;

/**
 * 这个是请求的管理者
 * 这个应该通过相应的模块进行相应的初始化
 */
public class FaceRetrofitServiceManager {

    private static FaceRetrofitServiceManager sManager;

    private Retrofit mRetrofit;

    /**
     * 配置信息
     */
    private RetrofitConfig mRetrofitConfig;

    private FaceRetrofitServiceManager() {

    }

    public static FaceRetrofitServiceManager getInstance() {
        if (sManager == null) {
            synchronized (FaceRetrofitServiceManager.class) {
                if (sManager == null) {
                    sManager = new FaceRetrofitServiceManager();
                }
            }
        }
        return sManager;
    }

    /**
     * 初始化相应参数
     */
    public void init(RetrofitConfig retrofitConfig) {
        mRetrofitConfig = retrofitConfig;
        mRetrofit = createRetrofit();
    }

    private Retrofit createRetrofit() {
        /*构建Retrofit对象*/
        Retrofit.Builder builder = new Retrofit.Builder();
        if (TextUtils.isEmpty(mRetrofitConfig.getUrl())) {
            throw new RuntimeException("请初始化相应的内容调用init()");
        }
        //设置Url
        builder.baseUrl(mRetrofitConfig.getUrl());
        //设置HttpClient
        builder.client(getOkHttpClient());
        //返回数据设置
        if (mRetrofitConfig.isGson()) {
            builder.addConverterFactory(GsonConverterFactory.create());
            builder.addCallAdapterFactory(RxJava2CallAdapterFactory.create());
        } else {
            builder.addConverterFactory(StringConverterFactory.create());
        }

        return builder.build();
    }

    /**
     * author :  贺金龙
     * create time : 2017/10/26 10:58
     * description : 创建OkHttpClient的实例
     * instructions : 这里只是基础配置,如果想要添加公共的请求参数的话,重写这个方法自己添加就可以了
     * 其他的配置也是一样的(像缓存)
     */
    private OkHttpClient getOkHttpClient() {
        final OkHttpClient.Builder builder = new OkHttpClient().newBuilder();
        //读取超时
        int readTime = mRetrofitConfig.getReadTime();
        if (readTime <= 0) {
            throw new RuntimeException("请正确设置读取超时时间");
        }
        builder.readTimeout(readTime, TimeUnit.SECONDS);
        //写入超时
        int writeTime = mRetrofitConfig.getWriteTime();
        if (writeTime <= 0) {
            throw new RuntimeException("请正确设置写入超时时间");
        }
        builder.writeTimeout(writeTime, TimeUnit.SECONDS);
        //连接超时
        int connectTime = mRetrofitConfig.getConnectTime();
        if (connectTime <= 0) {
            throw new RuntimeException("请正确设置连接超时时间");
        }
        builder.connectTimeout(connectTime, TimeUnit.SECONDS);
        //设置拦截器
        Interceptor[] interceptors = mRetrofitConfig.getInterceptor();
        if (interceptors != null && interceptors.length > 0) {
            for (Interceptor interceptor : interceptors) {
                builder.addInterceptor(interceptor);
            }
        }
        //设置cookieJar
        CookieJar cookieJar = mRetrofitConfig.getCookieJar();
        if (cookieJar != null) {
            builder.cookieJar(cookieJar);
        }
        //设置证书
        InputStream[] certificateIss = mRetrofitConfig.getCertificatePath();
        if (certificateIss != null && certificateIss.length > 0) {
            //设置证书
            SSLSocketFactory sslSocketFactory = SslUtils.SSLContext(certificateIss);
            if (sslSocketFactory != null) {
                builder.sslSocketFactory(sslSocketFactory, new HttpsTrustManager());
            }

            //设置连接方式
            ConnectionSpec spec = new ConnectionSpec.Builder(ConnectionSpec.MODERN_TLS)
                    .allEnabledTlsVersions()
                    .allEnabledCipherSuites()
                    .build();
            builder.connectionSpecs(Collections.singletonList(spec));
            //设置域名校验
//            builder.hostnameVerifier(new HostnameVerifier() {
//                @Override
//                public boolean verify(String hostname, SSLSession session) {
//                    return true;
//                }
//            });
        }
        //失败重连
        boolean retry = mRetrofitConfig.isRetry();
        if (retry) {
            builder.retryOnConnectionFailure(true);
        }
        //设置连接池
        builder.connectionPool(new ConnectionPool());
        return builder.build();
    }

    /**
     * author :  贺金龙
     * create time : 2017/10/26 13:37
     * description :  提供一个返回ApiServer的方法
     * instructions : 各个module通过继承这个方法根据各自的需要返回相应的内容
     * version :1.0
     */
    public <T> T create(Class<T> service) {
        return mRetrofit.create(service);
    }
}
