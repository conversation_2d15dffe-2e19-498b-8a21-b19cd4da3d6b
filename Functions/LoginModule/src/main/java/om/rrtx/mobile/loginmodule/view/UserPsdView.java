package om.rrtx.mobile.loginmodule.view;

import java.util.List;

import om.rrtx.mobile.loginmodule.bean.RegionBean;
import om.rrtx.mobile.rrtxcommon1.bean.UserInfoBean;

public interface UserPsdView {

    /**
     * 请求时区接口成功
     *
     * @param regionBeans 时区接口实体类
     */
    void regionSuccess(List<RegionBean> regionBeans);

    /**
     * 请求失败
     *
     * @param sResMsg 失败信息
     */
    void requestFail(String sResMsg);

    /**
     * 忘记密码接口成功
     */
    void forgetNameSuccess();

    /**
     * 获取用户信息成功
     *
     * @param personalInfoBean 用户信息
     */
    void getUserInfoSuccess(UserInfoBean personalInfoBean);
}
