package om.rrtx.mobile.loginmodule.viewmodel;

import android.content.Context;
import android.text.TextUtils;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import om.rrtx.mobile.rrtxcommon1.bean.LoginBean;
import om.rrtx.mobile.loginmodule.bean.PubBean;
import om.rrtx.mobile.loginmodule.model.LoginModel;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * 临时设备的ViewModel
 */
public class TemporaryViewModel extends ViewModel {
    private LoginModel mLoginModel;
    private MutableLiveData<LoginBean> mLoginBeanMutableLiveData;
    private MutableLiveData<String> mErrorLiveData;

    public TemporaryViewModel() {
        mLoginModel = new LoginModel();
    }

    public MutableLiveData<LoginBean> getLoginBeanMutableLiveData() {
        if (mLoginBeanMutableLiveData == null) {
            mLoginBeanMutableLiveData = new MutableLiveData<>();
        }
        return mLoginBeanMutableLiveData;
    }

    public MutableLiveData<String> getErrorLiveData() {
        if (mErrorLiveData == null) {
            mErrorLiveData = new MutableLiveData<>();
        }
        return mErrorLiveData;
    }

    /**
     * 请求验证设备授权码
     *
     * @param context 上下文
     * @param code    授权码
     */
    public void requestValidateCode(Context context, String userName, String psd, String code, String passwordType) {
        String pubLick = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.USERID, "");
        String registrationId = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.REGISTRATIONID, "");
        String token = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.DEVICETOKEN, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mLoginModel.requestValidateCode(userId,userName, psd,passwordType, code,registrationId,token, new BaseObserver<LoginBean>(context) {
                @Override
                public void requestSuccess(LoginBean sResData) {
                    if (mLoginBeanMutableLiveData != null) {
                        mLoginBeanMutableLiveData.setValue(sResData);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (mErrorLiveData != null) {
                        mErrorLiveData.setValue(sResMsg);
                    }
                }
            });
        } else {
            mLoginModel.commonPub(new BaseNoDialogObserver<PubBean>(context) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(context, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mLoginModel.requestValidateCode(userId,userName, psd,passwordType, code,registrationId,token, new BaseObserver<LoginBean>(context) {
                        @Override
                        public void requestSuccess(LoginBean sResData) {
                            if (mLoginBeanMutableLiveData != null) {
                                mLoginBeanMutableLiveData.setValue(sResData);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (mErrorLiveData != null) {
                                mErrorLiveData.setValue(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }
}
