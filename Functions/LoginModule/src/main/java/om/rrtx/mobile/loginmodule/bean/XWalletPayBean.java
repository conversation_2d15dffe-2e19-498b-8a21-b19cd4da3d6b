package om.rrtx.mobile.loginmodule.bean;

/**
 * <AUTHOR>
 * 支付的实体类这里应该把参数都控制在这里
 */
public class XWalletPayBean {

    /**
     * 订单号
     */
    private String transOrderNo;
    /**
     * 支付类型
     */
    private String paymentProduct;
    /**
     * 订单类型
     * 0-内部 1-外部
     */
    private String orderSource;
    /**
     * 跳转标识
     */
    private String jumpFlag;

    /**
     * 外部调用->支付的token
     */
    private String payToken;
    /**
     * 外部调用->包名
     */
    private String packName;

    /**
     * 订单金额 -> 支付方式需要使用
     */
    private String orderAmt;
    /**
     * 商户号 -> 支付方式需要使用
     */
    private String merNo;
    /**
     * 支付订单号 -> 支付的时候使用
     */
    private String payOrderNo;
    /**
     * 二维码 -> paymentProduct为02时必须
     */
    private String qrCode;
    /**
     * 外部订单号 -> orderSource为1时必须
     */
    private String outOrderNo;
    /**
     * 订单信息
     */
    private String orderInfo;

    //订单返回需要
    /**
     * 全名
     */
    private String fullName;
    /**
     * 银行卡名称
     */
    private String bankName;
    /**
     * 卡号
     */
    private String cardNo;

    /**
     * 外部类型
     */
    private String outType;

    private XWalletPayBean(Builder builder) {
        transOrderNo = builder.transOrderNo;
        paymentProduct = builder.paymentProduct;
        orderSource = builder.orderSource;
        jumpFlag = builder.jumpFlag;
        payToken = builder.payToken;
        packName = builder.packName;
        orderAmt = builder.orderAmt;
        merNo = builder.merNo;
        orderInfo = builder.orderInfo;
        payOrderNo = builder.payOrderNo;
        qrCode = builder.qrCode;
        outOrderNo = builder.outOrderNo;
        fullName = builder.fullName;
        bankName = builder.bankName;
        cardNo = builder.cardNo;
        outType = builder.outType;
    }

    public String getTransOrderNo() {
        return transOrderNo;
    }

    public void setTransOrderNo(String transOrderNo) {
        this.transOrderNo = transOrderNo;
    }

    public String getPaymentProduct() {
        return paymentProduct;
    }

    public void setPaymentProduct(String paymentProduct) {
        this.paymentProduct = paymentProduct;
    }

    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    public String getJumpFlag() {
        return jumpFlag;
    }

    public void setJumpFlag(String jumpFlag) {
        this.jumpFlag = jumpFlag;
    }

    public String getPayToken() {
        return payToken;
    }

    public void setPayToken(String payToken) {
        this.payToken = payToken;
    }

    public String getPackName() {
        return packName;
    }

    public void setPackName(String packName) {
        this.packName = packName;
    }

    public String getOrderAmt() {
        return orderAmt;
    }

    public void setOrderAmt(String orderAmt) {
        this.orderAmt = orderAmt;
    }

    public String getMerNo() {
        return merNo;
    }

    public void setMerNo(String merNo) {
        this.merNo = merNo;
    }

    public String getPayOrderNo() {
        return payOrderNo;
    }

    public void setPayOrderNo(String payOrderNo) {
        this.payOrderNo = payOrderNo;
    }

    public String getQrCode() {
        return qrCode;
    }

    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }

    public String getOutOrderNo() {
        return outOrderNo;
    }

    public void setOutOrderNo(String outOrderNo) {
        this.outOrderNo = outOrderNo;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getOrderInfo() {
        return orderInfo;
    }

    public void setOrderInfo(String orderInfo) {
        this.orderInfo = orderInfo;
    }

    public String getOutType() {
        return outType;
    }

    public void setOutType(String outType) {
        this.outType = outType;
    }

    public static class Builder {
        /**
         * 订单号
         */
        private String transOrderNo;
        /**
         * 支付类型
         */
        private String paymentProduct;
        /**
         * 订单类型
         * 0-内部 1-外部
         */
        private String orderSource;
        /**
         * 跳转标识
         */
        private String jumpFlag;
        /**
         * 订单信息
         */
        private String orderInfo;
        /**
         * 外部调用->支付的token
         */
        private String payToken;
        /**
         * 外部调用->包名
         */
        private String packName;

        /**
         * 订单金额 -> 支付方式需要使用
         */
        private String orderAmt;
        /**
         * 商户号 -> 支付方式需要使用
         */
        private String merNo;
        /**
         * 支付订单号 -> 支付的时候使用
         */
        private String payOrderNo;
        /**
         * 二维码 -> paymentProduct为02时必须
         */
        private String qrCode;
        /**
         * 外部订单号 -> orderSource为1时必须
         */
        private String outOrderNo;

        //订单返回需要
        /**
         * 全名
         */
        private String fullName;
        /**
         * 银行卡名称
         */
        private String bankName;
        /**
         * 卡号
         */
        private String cardNo;
        /**
         * 卡号
         */
        private String outType;


        public Builder setTransOrderNo(String transOrderNo) {
            this.transOrderNo = transOrderNo;
            return this;
        }

        public Builder setPaymentProduct(String paymentProduct) {
            this.paymentProduct = paymentProduct;
            return this;
        }

        public Builder setOrderSource(String orderSource) {
            this.orderSource = orderSource;
            return this;
        }

        public Builder setJumpFlag(String jumpFlag) {
            this.jumpFlag = jumpFlag;
            return this;
        }

        public Builder setPayToken(String payToken) {
            this.payToken = payToken;
            return this;
        }

        public Builder setPackName(String packName) {
            this.packName = packName;
            return this;
        }

        public Builder setOrderAmt(String orderAmt) {
            this.orderAmt = orderAmt;
            return this;
        }

        public Builder setMerNo(String merNo) {
            this.merNo = merNo;
            return this;
        }

        public Builder setPayOrderNo(String payOrderNo) {
            this.payOrderNo = payOrderNo;
            return this;
        }

        public Builder setQrCode(String qrCode) {
            this.qrCode = qrCode;
            return this;
        }

        public Builder setOutOrderNo(String outOrderNo) {
            this.outOrderNo = outOrderNo;
            return this;
        }

        public Builder setFullName(String fullName) {
            this.fullName = fullName;
            return this;
        }

        public Builder setBankName(String bankName) {
            this.bankName = bankName;
            return this;
        }

        public Builder setCardNo(String cardNo) {
            this.cardNo = cardNo;
            return this;
        }

        public Builder setOrderInfo(String orderInfo) {
            this.orderInfo = orderInfo;
            return this;
        }

        public Builder setOutType(String outType) {
            this.outType = outType;
            return this;
        }

        public XWalletPayBean builder() {
            return new XWalletPayBean(this);
        }
    }
}
