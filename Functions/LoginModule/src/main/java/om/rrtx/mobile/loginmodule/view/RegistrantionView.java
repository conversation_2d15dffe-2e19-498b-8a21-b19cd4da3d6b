package om.rrtx.mobile.loginmodule.view;

import java.util.List;

import om.rrtx.mobile.rrtxcommon1.bean.ConditionBean;
import om.rrtx.mobile.loginmodule.bean.RegionBean;

public interface RegistrantionView {

    /**
     * 请求时区接口成功
     *
     * @param regionBeans 时区接口实体类
     */
    void regionSuccess(List<RegionBean> regionBeans);

    /**
     * 校验成功
     */
    void validateSuccess();

    /**
     * 请求失败
     *
     * @param sResMsg 失败信息
     */
    void requestFail(String sResMsg);

    /**
     * 请求协议成功
     *
     * @param bean
     */
    void conditionSuccess(ConditionBean bean);
    /**
     * 请求协议失败
     *
     * @param errMsg 失败信息
     */
    void conditionFail(String errMsg);
}
