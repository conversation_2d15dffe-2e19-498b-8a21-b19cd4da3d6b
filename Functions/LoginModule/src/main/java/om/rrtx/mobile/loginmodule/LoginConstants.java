package om.rrtx.mobile.loginmodule;

import om.rrtx.mobile.rrtxcommon1.UserConstants;

/**
 * <AUTHOR>
 * Login模块的数据整理
 */
public class LoginConstants {

    public interface Parameter {
        /**
         * 用户名称
         */
        String USERNAME = "userName";
        String MOBILENO = "mobileNo";
        /**
         * 真实姓名
         */
        String REALNAME = "realName";
        /**
         * nric
         */
        String IDCARD = "idCard";
        /**
         * 电话号
         */
        String MOBILE = "mobile";
        /**
         * 邀请码
         */
        String INVITECODE = "inviteCode";
        /**
         * 验证Pin
         */
        String PAYMENTPASSWORD = "paymentPassword";
        /**
         * 短线吗
         */
        String SMSCODE = "smsCode";
        /**
         * 区号
         */
        String MOBILEAREACODE = "mobileAreaCode";

        /**
         * 用户密码
         */
        String PASSWORD = "password";
        /**
         * 密码类型
         */
        String PASSWORDTYPE = "passwordType";
        /**
         * 设备标识
         */
        String BIOPWD = "bioPwd";
        /**
         * 设备标识
         */
        String GESTUREPWD = "gesturePwd";
        /**
         * 加密字段标识
         */
        String KEYID = "keyId";
        /**
         * app标识
         */
        String APPNAME = "appName";
        /**
         * 操作系统类型 （1：Android 2：iOS）
         */
        String OSTYPE = "osType";
        /**
         * 版本号
         */
        String VERSIONNO = "versionNo";
        /**
         * 极光注册ID
         */
        String REGISTRATIONID = "registrationId";
        /**
         * google deviceToken
         */
        String DEVICETOKEN = "deviceToken";
        /**
         * 授权码
         */
        String AUTHCODE = "authCode";
        /**
         * 用户id
         */
        String USERID = "userId";
        /**
         * 协议类型 01-钱包个人 02-钱包商户
         */
        String CONDITIONTYPE = UserConstants.Parameter.CONDITIONTYPE;
        /**
         * 协议版本
         */
        String CONDITIONVERSIONNO = UserConstants.Parameter.CONDITIONVERSIONNO;
        /**
         * 协议名称
         */
        String CONDITIONTITLE = UserConstants.Parameter.CONDITIONTITLE;
    }

    public interface URL {
        /**
         * 获取公钥接口
         */
        String GETPUB = "/encrypt/public/key";
        /**
         * 登录接口
         */
        String LOGIN = "user/login";
        /**
         * 请求时区接口
         */
        String RAGION = "/nation/list";
        /**
         * 请求注册接口
         */
        String REGISTER = "/user/register";
        String JUNIOR_REGISTER = "/juniorAccount/apply";
        /**
         * 请求注册校验
         */
        String REGISTERVALIDATE = "/user/register/validate";
        /**
         * 注册手机号验证
         */
        String REGISTER_PHONE_VALIDATE = "/user/register/mobile/validate";

        /**
         * 亲子注册校验
         */
        String JUNIOR_REGISTER_VALIDATE = "/juniorAccount/applyCheck";
        /**
         * 忘记用户名
         */
        String FORGETUSERNAME = "/user/forgetUsername";

        /**
         * 查询用户信息
         */
        String PSDSETUP = "/user/login/password/setup";
        /**
         * 邀请好友设置登录密码
         */
        String INVIRWFRIENDSETUP = "/inviteFriends/login/password/setup";
        /**
         * 检查app升级
         */
        String APPVERSION = "/appVersion/compare";
        /**
         * 验证设备授权码
         */
        String AUTHCODEVALIDATE = "/user/device/authCode/validate";

    }

    /**
     * 二维码类型
     */
    public interface QrType {
        /**
         * 个人收款信息
         */
        String PSK = "PSK";
        /**
         * 商户收款信息
         */
        String MER = "MER";
        /**
         * 个人二维码名片
         */
        String PMP = "PMP";

        /**
         * 扫描外部二维码
         */
        String QRCODEPREFIX = "https://xwallet-short.rrtx.vimbug.com/";

        /**
         * 扫描外部订单
         */
        String QRCODE = "qrCode";

        /**
         * h5跳转
         */
        String WEBTOKEN = "h5";
    }

    public interface Regex {
        /**
         * 姓名校验
         */
        String nameRegex = "^[a-zA-Z0-9_@.]{5,30}$";

        /**
         * 全名校验
         */
        String fullNameRegex = "^[a-zA-Z-,/@. '`]+$";

        /**
         * 密码
         */
        String psdResex = "^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*[.~!@#$%^&*()_\\-+={}<>])[a-zA-Z0-9.~!@#$%^&*()_\\-+={}<>]{8,20}$";
        /**
         * 卡号
         */
        String nricResex = "^[a-zA-Z0-9]{1,30}$";
    }

    /**
     * 传递的参数
     */
    public interface Transmit {
        /**
         * 用户名称
         */
        String USERNAME = "userName";
        /**
         * 电话
         */
        String MOBILE = "mobile";
        /**
         * 用户密码
         */
        String PASSWORD = "password";
        /**
         * 用户卡号
         */
        String IDCARD = "idCard";
        /**
         * 跳转类型 1:密码 2：指纹 3: 手势
         */
        String TYPE = "type";
    }

    public interface LoginType {
        String PasswordType = "1";
        String FingerprintType = "2";
        String GestureType = "3";
    }
}
