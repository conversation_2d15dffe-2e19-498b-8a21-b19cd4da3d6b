package om.rrtx.mobile.loginmodule.activity;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.util.Log;
import android.view.View;
import android.widget.EditText;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import org.json.JSONException;
import org.json.JSONObject;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functionapi.bean.OutJumpBean;
import om.rrtx.mobile.loginmodule.R;
import om.rrtx.mobile.loginmodule.R2;
import om.rrtx.mobile.loginmodule.bean.UpDataBean;
import om.rrtx.mobile.rrtxcommon1.bean.LoginBean;
import om.rrtx.mobile.loginmodule.presenter.LoginPresenter;
import om.rrtx.mobile.loginmodule.view.LoginView;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean;
import om.rrtx.mobile.rrtxcommon1.dialog.UpDataDialog;
import om.rrtx.mobile.rrtxcommon1.utils.AppInfoUtils;
import om.rrtx.mobile.rrtxcommon1.utils.AppInitUtil;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 登录页面
 */
@Route(path = ARouterPath.LoginPath.LoginActivity)
public class LoginActivity extends BaseSuperActivity<LoginView, LoginPresenter>
        implements LoginView, TextWatcher {

    @BindView(R2.id.forgetPsdTv)
    TextView mForgetPsdTv;
    @BindView(R2.id.loginTv)
    TextView mLoginTv;
    @BindView(R2.id.signTv)
    TextView mSignTv;
    @BindView(R2.id.userNameTIL)
    LinearLayout mUserNameTIL;
    @BindView(R2.id.psdEt)
    EditText mPsdEt;
    @BindView(R2.id.nameEt)
    EditText mNameEt;


    private long mStartTime = 0;
    private String mJumpFlag, mJpushJson;
    private String mOutJumpDateJson;

    static int REQUEST_CODE_CONDITION = 0x05;

    public static void jumpLogin(Context context) {
        Intent intent = new Intent(context, LoginActivity.class);
        context.startActivity(intent);
    }

    public static void jumpTopLogin(Context context) {
        Intent intent = new Intent(context, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        context.startActivity(intent);
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        mJumpFlag = getIntent().getStringExtra(BaseConstants.Transmit.JUMPFLAG);
        mJpushJson = getIntent().getStringExtra(BaseConstants.Transmit.JPUSHJSON);
        mOutJumpDateJson = getIntent().getStringExtra(BaseConstants.Transmit.OUTJUMPDATEJSON);

        String xWallerPayBeanJson = getIntent().getStringExtra(BaseConstants.Transmit.XWALLETPAYBEANJSON);
        if (!TextUtils.isEmpty(xWallerPayBeanJson)) {
            SharedPreferencesUtils.setParam(mContext, BaseConstants.Transmit.XWALLETPAYBEANJSON, xWallerPayBeanJson);
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent != null) {
            mJumpFlag = intent.getStringExtra(BaseConstants.Transmit.JUMPFLAG);
            mJpushJson = intent.getStringExtra(BaseConstants.Transmit.JPUSHJSON);
            mOutJumpDateJson = intent.getStringExtra(BaseConstants.Transmit.OUTJUMPDATEJSON);

            String xWallerPayBeanJson = intent.getStringExtra(BaseConstants.Transmit.XWALLETPAYBEANJSON);
            if (!TextUtils.isEmpty(xWallerPayBeanJson)) {
                SharedPreferencesUtils.setParam(mContext, BaseConstants.Transmit.XWALLETPAYBEANJSON, xWallerPayBeanJson);
            }
        }
    }

    @Override
    protected int createContentView() {
        return R.layout.login_activity_login;
    }

    @Override
    protected LoginPresenter createPresenter() {
        return new LoginPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true)
                .init();

        AppInitUtil.initImageLoader();
        mPresenter.requestUpdate(AppInfoUtils.getVersionName(mContext));

        if (!TextUtils.isEmpty(BaseApp.getIsHasDownLoadUrl())) {
            UpDataDialog upDataDialog = new UpDataDialog(mContext);
            upDataDialog.show();
            upDataDialog.setClickCallBack(() -> {
                Uri uri = Uri.parse(BaseApp.getIsHasDownLoadUrl());
                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                mContext.startActivity(intent);
            });
        }
    }

    @Override
    public void initDate() {
        super.initDate();
    }

    @Override
    public void initListener() {
        super.initListener();
        mLoginTv.setOnClickListener(mLoginCustom);
        mSignTv.setOnClickListener(mLoginCustom);

        mNameEt.addTextChangedListener(this);
        mPsdEt.addTextChangedListener(this);
        mForgetPsdTv.setOnClickListener(mLoginCustom);

    }

    /**
     * 点击事件
     */
    private CustomClickListener mLoginCustom = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.loginTv) {
                //登录按钮
                loginBtn();
            } else if (view.getId() == R.id.forgetPsdTv) {
                //忘记密码
                ForgetPinActivity.jump(mContext,BaseConstants.JumpFlag.LOGIN_FORGET_JUMP,mNameEt.getText().toString());
            } else if (view.getId() == R.id.signTv) {
                //注册
                NewRegistrationActivity.jump(mContext,BaseConstants.JumpFlag.Register_Account);
                finish();
            }
        }
    };

    /**
     * 登录
     */
    private void loginBtn() {
        String userName = mNameEt.getText().toString();

        String password = mPsdEt.getText().toString();

        String registrationId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.REGISTRATIONID, "");
        String deviceToken = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.DEVICETOKEN, "");
        if (!StringUtils.isValidMobile(userName)) {
            ToastUtil.show(mContext, getString(R.string.mobile_error));
            return;
        }
        mPresenter.requestLogin(userName, password, registrationId, deviceToken);
    }


    @Override
    public void beforeTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void onTextChanged(CharSequence charSequence, int i, int i1, int i2) {

    }

    @Override
    public void afterTextChanged(Editable editable) {
        String passwordStr = mPsdEt.getText().toString();
        String userStr = mNameEt.getText().toString();

        if (passwordStr.length() > 0 && userStr.length() > 0) {
            mLoginTv.setBackgroundResource(R.drawable.common_usable_btn);
            mLoginTv.setEnabled(true);
        } else {
            mLoginTv.setBackgroundResource(R.drawable.common_unusable_btn);
            mLoginTv.setEnabled(false);
        }
    }

    @Override
    public void loginSuccess(LoginBean loginBean) {
        Log.e("LoginActivity>>>", "zfw loginSuccess>>> loginBean:" + loginBean.toString());
        // 强制pin test01
        if (loginBean.getPinSetupFlag().equals("0")) {
            // 强制pin test02
            // 强制修改初始pin
            setPin();
        } else if (loginBean.getStockPaymentPasswordSetupFlag().equals("0")) {
            // 迁移用户重置PIN
            ResetPinActivity.jump(mContext,BaseConstants.JumpFlag.RESETJUMP,mNameEt.getText().toString());
        } else {
            // 强制pin test03
            if ("1".equals(loginBean.getHasNewCondition())) {
                String beanStr = new Gson().toJson(loginBean);
                Intent intent = new Intent(this, UpdateConditionActivity.class);
                intent.putExtra(UpdateConditionActivity.LOGINBEAN, beanStr);
                intent.putExtra(UpdateConditionActivity.JUMPFLG, mJumpFlag);
                intent.putExtra(UpdateConditionActivity.OUTJPUSHJSON, mJpushJson);
                intent.putExtra(UpdateConditionActivity.OUTJUMPDATEJSON, mOutJumpDateJson);
                startActivityForResult(intent, REQUEST_CODE_CONDITION);
            } else {
                executeLogin(loginBean);
            }
        }
    }


    private void setPin() {
        mPsdEt.setText("");
        ARouter.getInstance()
                .build(ARouterPath.SecurityPath.SetPinActivity)
                .withString(BaseConstants.Transmit.PINPAYMENT, BaseConstants.Check.AUTHENTICATION)
                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.LOGINJUMP)
                .navigation();
    }

    public void executeLogin(LoginBean loginBean) {
        String userStatus = loginBean.getUserStatus();
        //登录成功
        //保存设置支付密码状态
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PAYMENTPASSWORDSETUPFLAG, loginBean.getPinSetupFlag());
        //保存用户名称
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.MASKNAME, loginBean.getMaskName());
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERNAME, loginBean.getNickName());
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.REALNAME, loginBean.getRealName());
        //保存用户电话
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERMOBILE, loginBean.getMobile());
        //保存用户id
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERCARDID, loginBean.getIdCard());
        //保存用户头像
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERHEARD, loginBean.getUserAvatar());
        //保存用户ID
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERID, loginBean.getUserId());
        //nric
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.IDCARD, loginBean.getIdCard());
        //记录区号
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.MOBILEAREACODE, loginBean.getMobileAreaCode());
        //记录币种
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.SAVECURRENCY, TextUtils.isEmpty(loginBean.getCurrency()) ? "" : loginBean.getCurrency());

        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.LASTLOGINTIME, TextUtils.isEmpty(loginBean.getLastLoginTime()) ? "" : loginBean.getLastLoginTime());

        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.ISGESTURE, TextUtils.equals(loginBean.getGestureStatus(), "1"));
        //账户类型 0-常规账户 1-亲子账户
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.ACCOUNTTYPE, loginBean.getAccountType());

        if (TextUtils.equals(BaseConstants.UserStatus.NORMAL, userStatus)) {
            if (TextUtils.isEmpty(mJumpFlag)) {
                //正常跳转
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .navigation();
            } else {
                //外部跳转
                if (!TextUtils.isEmpty(mOutJumpDateJson)) {
                    externalJump(new Gson().fromJson(mOutJumpDateJson, OutJumpBean.class));
                } else if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.JPUSHJUMP)) {
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                            .navigation();

                    if (getIntent() != null) {
                        handlerJpush(mJpushJson);
                    }
                } else {
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity).navigation();
                    ARouter.getInstance().build(ARouterPath.Cashier.CashierActivity)
                            .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                            .navigation();
                    finish();
                }
            }
        } else if (TextUtils.equals(BaseConstants.UserStatus.NOTACTIVE, userStatus)) {
            ARouter.getInstance()
                    .build(ARouterPath.LoginPath.SetLoginPsdActivity)
                    .withString(BaseConstants.Transmit.USERNAME, loginBean.getNickName())
                    .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.USERNOACTIVE)
                    .navigation();
        } else {
            if (TextUtils.isEmpty(mJumpFlag)) {
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .navigation();
            } else {
                if (!TextUtils.isEmpty(mOutJumpDateJson)) {
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity).navigation();
                    ARouter.getInstance().build(ARouterPath.Cashier.ScanCashierActivity)
                            .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                            .withString(BaseConstants.Transmit.XWALLETPAYBEANJSON, mOutJumpDateJson)
                            .navigation();
                    finish();
                } else {
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity).navigation();
                    ARouter.getInstance().build(ARouterPath.Cashier.CashierActivity)
                            .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                            .navigation();
                    finish();
                }
            }
        }
        BaseApp.setIsLock(false);
        BaseApp.setIsPromotion(true);
    }

    /**
     * 处理外部跳转
     *
     * @param outJumpBean 外部传递的参数
     */
    private void externalJump(OutJumpBean outJumpBean) {
        if (TextUtils.equals(outJumpBean.getSource(), BaseConstants.ExternalJumpFlag.INVITE)) {
            //邀请好友无需处理,只要跳转到首页就好了
            ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                    .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    .navigation();
        } else if (TextUtils.equals(outJumpBean.getSource(), BaseConstants.ExternalJumpFlag.ORDERQR) ||
                TextUtils.equals(outJumpBean.getSource(), BaseConstants.ExternalJumpFlag.HTML)) {
            ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity).navigation();
            ARouter.getInstance().build(ARouterPath.Cashier.ScanCashierActivity)
                    .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    .withString(BaseConstants.Transmit.XWALLETPAYBEANJSON, mOutJumpDateJson)
                    .navigation();
        }
    }


    @Override
    public void requestFail(BaseBean<LoginBean> sResMsg) {
        if (TextUtils.equals(sResMsg.getStatus(), "RRB-01001047")) {
            //表示临时设备登录 走授权码验证流程
            String nameStr = mNameEt.getText().toString();
            String psdStr = mPsdEt.getText().toString();
            TemporaryActivity.jumpTemporary(mContext, nameStr, psdStr, "1");
        } else {
            ToastUtil.show(mContext, sResMsg.getMessage());
        }
    }

    @Override
    public void upDataSuccess(UpDataBean sResData) {
        if (!TextUtils.equals(sResData.getIsLatestVersion(), "1") &&
                TextUtils.equals(sResData.getUpdateFlag(), "0")) {
            BaseApp.setIsHasDownLoadUrl(sResData.getDownloadUrl());
        }
    }

    @Override
    public void onBackPressed() {
        long secondTime = System.currentTimeMillis();
        if (secondTime - mStartTime > 2000) {
            ToastUtil.show(mContext, getString(R.string.common_tip_double_click_tip));
            mStartTime = secondTime;
        } else {
            moveTaskToBack(true);
        }
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST_CODE_CONDITION) {
            mNameEt.setText("");
            mPsdEt.setText("");
        }
    }

    /**
     * 处理推送的接口
     *
     * @param extras 推送的数据
     */
    private void handlerJpush(String extras) {
        Log.e("LoginActivity>>>", "zfw handlerJpush>>> extras:" + extras);
        try {
            JSONObject jsonObject = new JSONObject(extras);
            //目标页面
            String target = jsonObject.optString("target");
            String[] targetSplit = target.split("/");
            if (targetSplit.length < 2) {
                return;
            }
            if (TextUtils.equals(targetSplit[1], BaseConstants.MessageType.HOME)) {
                //跳转到首页
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                        .navigation();
            } else if (TextUtils.equals(targetSplit[1], BaseConstants.MessageType.TRANSACTION)) {
                //转账类型
                if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONLIST)) {
                    //历史账单列表
                    ARouter.getInstance().build(ARouterPath.TransferPath.HistoryActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .navigation();
                } else if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONDETAIL)) {
                    String orderId = jsonObject.optString(BaseConstants.Transmit.ORDERID);
                    String transType = jsonObject.optString(BaseConstants.Transmit.ORDERTYPE);

                    if (TextUtils.equals(transType, BaseConstants.HistoryType.PAYTYPE)) {
                        ARouter.getInstance().build(ARouterPath.TransferPath.HistoryPaymentActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    } else if (TextUtils.equals(transType, BaseConstants.HistoryType.WITHDRAWALTYPE)) {
                        ARouter.getInstance().build(ARouterPath.TransferPath.WithdrawalTypeActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    } else if (TextUtils.equals(transType, BaseConstants.HistoryType.AGENTTOPUP) || TextUtils.equals(transType, BaseConstants.HistoryType.AGENTWITH)) {
                        ARouter.getInstance().build(ARouterPath.TransferPath.AgentTopUpActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    } else if (TextUtils.equals(transType, BaseConstants.HistoryType.AGENTTRAN)) {
                        ARouter.getInstance().build(ARouterPath.TransferPath.HistoryAgentTranActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    } else if (TextUtils.equals(transType, BaseConstants.HistoryType.AGENTMOBILE)) {
                        ARouter.getInstance().build(ARouterPath.TransferPath.HistoryAgentMobileActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    } else {
                        ARouter.getInstance().build(ARouterPath.TransferPath.HistoryDetailsActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    }
                } else if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONSPILTRECLIST)) {
                    //AA我发起的列表
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEAALAUNCH)
                            .navigation();
                } else if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONSPILTPAYLIST)) {
                    //AA我支付的列表
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEAARECEIVED)
                            .navigation();
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
