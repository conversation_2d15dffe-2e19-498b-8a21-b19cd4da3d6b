package om.rrtx.mobile.loginmodule.bean

data class RegisterInfoBean(
    /**
     * 亲子关系
     * 0 Mother，1 Father，2 Legal Guardian
     * 正常注册不需要该字段 手动置null
     */
    var relationShip: Int? = -1,
    var areaCode: String = "263",
    var mobile: String = "",
    var firstName: String = "",
    var middleName: String = "",
    var lastName: String = "",
    var gender: String = "",
    var dateOfBirth: String = "",
    /**
     * 10 身份证 ，20 护照
     */
    var idType: String = "",
    var idNumber: String = "",
    var cityCode: String = "",
    var address: String = "",
    var email: String = "",
    var password: String = "",
    var checkToken: String = "",

    var idPhotos: List<String> = emptyList(),
    var facePhotos: List<String> = emptyList()
) {
    fun verify(): Boolean {
        if (relationShip == null) return false
        if (mobile == "") return false
        if (firstName == "") return false
        if (lastName == "") return false
        if (gender == "") return false
        if (dateOfBirth == "") return false
        if (idType == "") return false
        if (idNumber == "") return false
        if (cityCode == "") return false
        if (address == "") return false
        if (idPhotos.isEmpty()) return false
        if (facePhotos.isEmpty()) return false
//        if (email == "*") return false
        return true
    }
}

data class RegisterResultBean(
    // 0 -正常 1-冻结 2-删除 3-待激活 4-待认证 6升级待认证
    var userStatus: String,
    var isWalletUser: String,
    var userName: String,
    var checkToken: String,
    var verifyStatus: String,
    var accountType: String,
    var isCardIdUsed: String
)