package om.rrtx.mobile.loginmodule.activity

import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.InputFilter
import android.text.Spanned
import android.text.TextUtils
import android.view.View
import android.widget.EditText
import com.alibaba.android.arouter.facade.annotation.Route
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.login_activity_forget_pin.idTitle_tv
import kotlinx.android.synthetic.main.login_activity_forget_pin.id_ed
import kotlinx.android.synthetic.main.login_activity_forget_pin.mobile_include
import kotlinx.android.synthetic.main.login_activity_forget_pin.nameTitle_tv
import kotlinx.android.synthetic.main.login_activity_forget_pin.name_ed
import kotlinx.android.synthetic.main.login_activity_forget_pin.next_tv
import kotlinx.android.synthetic.main.login_base_title.backIv
import kotlinx.android.synthetic.main.login_base_title.leftBg
import kotlinx.android.synthetic.main.login_base_title.titleTv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.activity.VerCodeActivity
import om.rrtx.mobile.loginmodule.R
import om.rrtx.mobile.loginmodule.databinding.LoginActivityForgetPinBinding
import om.rrtx.mobile.loginmodule.viewmodel.LoginViewModel
import om.rrtx.mobile.rrtxcommon.utils.toast.ToastUtil
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseVVMActivity
import om.rrtx.mobile.rrtxcommon1.dialog.AloneDialog
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.TextChangeEndListener

@Route(path = ARouterPath.LoginPath.ForgetPinActivity)
class ForgetPinActivity : BaseVVMActivity<LoginViewModel, LoginActivityForgetPinBinding>() {

    private lateinit var mobile_ed: EditText

    var mJumpFlag = ""
    var mobile = "";
    private var juniorId = ""
    override fun createContentView() = R.layout.login_activity_forget_pin

    override fun doGetExtra() {
        super.doGetExtra()
        mJumpFlag = intent.getStringExtra(BaseConstants.Transmit.JUMPFLAG).toString()
        juniorId = intent.getStringExtra(BaseConstants.Transmit.JUNIOR_ID).toString()
        mobile = intent.getStringExtra(BaseConstants.Transmit.MOBILE).toString()

    }

    override fun initView() {

        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true)
            .init()

        dataBinding.mobileInclude.apply {
            mobile_ed = mobileEd
            addressBockIv.visibility = View.GONE
        }
        if (BaseConstants.JumpFlag.SET_JUNIOR_PIN==mJumpFlag)
        {
            titleTv.setText(R.string.change_PIN)
            nameTitle_tv.setText(R.string.junior_name)
            name_ed.setHint("")
            id_ed.setHint("")
        }else{
            titleTv.setText(R.string.forgot_PIN)
            nameTitle_tv.setText(R.string.name)
            name_ed.setHint(R.string.name)
            id_ed.setHint(R.string.id_number)
        }
        titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
        backIv.setBackgroundResource(R.drawable.common_ic_back_black)
        titleTv.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))

        //dataBinding.mobileInclude.areaTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
        if (!TextUtils.isEmpty(mobile) && "null" != mobile){
            mobile_ed.setText(mobile)
        }

        /*val filter =
            InputFilter { source: CharSequence, start: Int, end: Int, dest: Spanned?, dstart: Int, dend: Int ->
                val regex = "[a-zA-Z ]+"
                if (source.isNotEmpty() && !source.toString().matches(regex.toRegex())) {
                    return@InputFilter "" // 不是字母、数字或空格，则返回空字符串，表示不允许输入
                }
                null // 允许输入
            }

        name_ed.text.filters = arrayOf<InputFilter>(filter)*/

        when (mJumpFlag) {
            BaseConstants.JumpFlag.SET_JUNIOR_PIN -> {
                // 手动填充激活 提交按钮
                mobile_ed.setText("719999999")
                mobile_include.visibility = View.GONE
            }

            else -> {
                mobile_include.visibility = View.VISIBLE
            }
        }
    }

    override fun initClickListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    leftBg -> onBackPressed()
                    next_tv -> onNextClick()
                }
            }

        }.apply {
            leftBg.setOnClickListener(this)
            next_tv.setOnClickListener(this)
        }

        object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                if (editable.isEmpty()) {
                    next_tv.isEnabled = false
                } else {
                    next_tv.isEnabled =
                        mobile_ed.text.isNotEmpty() && mobile_ed.text.length == 9 && name_ed.text.isNotEmpty() && id_ed.text.isNotEmpty()
                }

            }
        }.apply {
            mobile_ed.addTextChangedListener(this)
            name_ed.addTextChangedListener(this)
            id_ed.addTextChangedListener(this)
        }
    }

    override fun initVMListener() {
        viewModel.mCheckUserErrorLd.observe(this){
            if (it=="RRB-08000274")
            {
                showJuniorHintDialog()
            }
        }
        viewModel.mCheckUserLd.observe(this) {
            // 往里传太麻烦，使用sp,修改登录密码时使用
            SharedPreferencesUtils.setParam(mContext,
                BaseConstants.SaveParameter.USERNAME,
                name_ed.text.toString())
            SharedPreferencesUtils.setParam(mContext,
                BaseConstants.SaveParameter.USERCARDID,
                id_ed.text.toString())

            SharedPreferencesUtils.setParam(
                mContext,
                BaseConstants.SaveParameter.JUNIOR_ID,
                juniorId
            )

            if (BaseConstants.JumpFlag.SET_JUNIOR_PIN!=mJumpFlag) {
                SharedPreferencesUtils.setParam(
                    mContext,
                    BaseConstants.SaveParameter.ACCOUNTTYPE,
                    it.accountType
                )
                SharedPreferencesUtils.setParam(
                    mContext,
                    BaseConstants.SaveParameter.HASECRETWORD,
                    it.hasSecretWord
                )
            }

            //账户类型 0-常规账户 1-亲子账户
            val accountType=SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ACCOUNTTYPE,"0")
            if (accountType=="1")
            {
                showJuniorHintDialog()
            }else{
                VerCodeActivity.jump(mContext, it.mobile, mJumpFlag)
            }
        }
    }

    private fun showJuniorHintDialog() {
        val aloneDialog = AloneDialog(mContext)
        aloneDialog.setAloneCallBack { aloneDialog.dismiss() }
        aloneDialog.show()
        aloneDialog.setTitleStr(getString(R.string.notice)).setBottomStr(getString(R.string.common_btn_confirm))
            .setContentStr(getString(R.string.notice_hint))
    }

    private fun onNextClick() {
        val mobile = mobile_ed.text.toString()
        if (!StringUtils.isValidMobile(mobile)) {
            ToastUtil.show(mContext, getString(R.string.mobile_error))
            return
        }

        val idNumber = id_ed.text.toString()
        if (!StringUtils.isValidIdNumber(idNumber)) {
            ToastUtil.show(mContext, getString(R.string.idcard_error))
            return
        }

//        VerCodeActivity.jump(mContext, "*********", BaseConstants.JumpFlag.FORGETJUMP)
//        viewModel.requestCheckUser("*********", "zwh z", "nxnmm")
        when (mJumpFlag) {
            BaseConstants.JumpFlag.SET_JUNIOR_PIN -> {
                viewModel.requestCheckJuniorAccount(juniorId,
                    id_ed.text.toString(),
                    name_ed.text.toString())
//                viewModel.requestCheckJuniorAccount(juniorId,
//                    "Dd",
//                    "Xxx Sss")
            }

            else -> {
                viewModel.requestCheckUser(mobile_ed.text.toString(), name_ed.text.toString(), id_ed.text.toString())
            }
        }
    }


    companion object {
        @JvmStatic
        fun jump(mContext: Activity, jumpFlag: String,mobile:String) {
            val intent = Intent(mContext, ForgetPinActivity::class.java)
            intent.putExtra(BaseConstants.Transmit.JUMPFLAG, jumpFlag)
            intent.putExtra(BaseConstants.Transmit.MOBILE, mobile)
            mContext.startActivity(intent)
        }
    }
}