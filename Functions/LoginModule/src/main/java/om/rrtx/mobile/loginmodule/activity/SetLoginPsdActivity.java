package om.rrtx.mobile.loginmodule.activity;

import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.google.android.material.textfield.TextInputEditText;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.loginmodule.LoginConstants;
import om.rrtx.mobile.loginmodule.R;
import om.rrtx.mobile.loginmodule.R2;
import om.rrtx.mobile.rrtxcommon1.bean.ConditionBean;
import om.rrtx.mobile.loginmodule.bean.RegistrationBean;
import om.rrtx.mobile.loginmodule.presenter.SetLoginPsdPresenter;
import om.rrtx.mobile.loginmodule.view.SetLoginPsdView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 设置登录密码页面
 */
@Route(path = ARouterPath.LoginPath.SetLoginPsdActivity)
public class SetLoginPsdActivity extends BaseSuperActivity<SetLoginPsdView, SetLoginPsdPresenter>
        implements TextWatcher, View.OnFocusChangeListener, SetLoginPsdView {


    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.psdOneTie)
    TextInputEditText mPsdOneTie;
    @BindView(R2.id.psdTwoTie)
    TextInputEditText mPsdTwoTie;
    @BindView(R2.id.psdOneIv)
    ImageView mPsdOneIv;
    @BindView(R2.id.psdTwoIv)
    ImageView mPsdTwoIv;
    @BindView(R2.id.submitTv)
    TextView mSubmitTv;
    private RegistrationBean mRegistrationBean;
    private String mJumpFlag;
    private String mIdCard;
    private String mUserName;

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        mJumpFlag = getIntent().getStringExtra(BaseConstants.Transmit.JUMPFLAG);
        mIdCard = getIntent().getStringExtra(LoginConstants.Transmit.IDCARD);
        mUserName = getIntent().getStringExtra(LoginConstants.Transmit.USERNAME);
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    protected int createContentView() {
        return R.layout.login_activity_set_login_psd;
    }

    @Override
    protected SetLoginPsdPresenter createPresenter() {
        return new SetLoginPsdPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.login_ic_back);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));
        mTitleTv.setText(getString(R.string.retrieve_title_set_login_pwd));
    }

    @Override
    public void initListener() {
        super.initListener();

        mPsdOneTie.addTextChangedListener(this);
        mPsdTwoTie.addTextChangedListener(this);

        mPsdOneTie.setOnFocusChangeListener(this);
        mPsdTwoTie.setOnFocusChangeListener(this);

        mPsdOneIv.setOnClickListener(loginCustom);
        mPsdTwoIv.setOnClickListener(loginCustom);
        mLeftBg.setOnClickListener(loginCustom);

        mSubmitTv.setOnClickListener(loginCustom);
    }

    private CustomClickListener loginCustom = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.psdOneIv) {
                mPsdOneTie.setText("");
            } else if (view.getId() == R.id.psdTwoIv) {
                mPsdTwoTie.setText("");
            } else if (view.getId() == R.id.leftBg) {
                finish();
            } else if (view.getId() == R.id.submitTv) {
                submitClick();
            }
        }
    };

    private void submitClick() {
        String psdOneStr = mPsdOneTie.getText().toString();
        String psdTwoStr = mPsdTwoTie.getText().toString();

        if (!psdOneStr.matches(LoginConstants.Regex.psdResex) || !psdTwoStr.matches(LoginConstants.Regex.psdResex)) {
            ToastUtil.show(mContext, getResources().getString(R.string.common_tip_error_pwd_tip));
            return;
        }

        if (!TextUtils.equals(psdOneStr, psdTwoStr)) {
            ToastUtil.show(mContext, getResources().getString(R.string.common_tip_no_match_pwd_tip));
            return;
        }

        if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.REGISTRATION)) {
            String registrationStr = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.REGISTRATIONSTR, "");
            mRegistrationBean = new Gson().fromJson(registrationStr, RegistrationBean.class);
            String conditionStr = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.CONDITIONSTR, "");
            ConditionBean conditionBean = new Gson().fromJson(conditionStr, ConditionBean.class);
            if (mRegistrationBean != null && conditionBean != null) {
                //清除所有缓存
                mRegistrationBean.setPassword(psdOneStr);
                mPresenter.requestRegistration(mRegistrationBean, conditionBean);
            }
        } else if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.FORGETPASSWORD)) {
            mPresenter.setUpLoginPsd(psdOneStr, mUserName, mIdCard);
        } else if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.USERNOACTIVE)) {
            mPresenter.setInviteFriendsPsd(psdOneStr);
        }
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable editable) {
        Editable psdOne = mPsdOneTie.getText();
        Editable psdTwo = mPsdTwoTie.getText();

        if (psdOne == null || psdTwo == null) {
            return;
        }

        String psdOneStr = psdOne.toString();
        String psdTwoStr = psdTwo.toString();

        if (editable == psdOne) {
            if (psdOneStr.length() > 0) {
                mPsdOneIv.setVisibility(View.VISIBLE);
            } else {
                mPsdOneIv.setVisibility(View.GONE);
            }
        } else if (editable == psdTwo) {

            if (psdTwoStr.length() > 0) {
                mPsdTwoIv.setVisibility(View.VISIBLE);
            } else {
                mPsdTwoIv.setVisibility(View.GONE);
            }
        }

        if (psdOneStr.length() > 0 && psdTwoStr.length() > 0) {
            mSubmitTv.setBackgroundResource(R.drawable.login_drawable_btn_select);
            mSubmitTv.setEnabled(true);
        } else {
            mSubmitTv.setBackgroundResource(R.drawable.login_drawable_btn_unselect);
            mSubmitTv.setEnabled(false);
        }
    }

    @Override
    public void onFocusChange(View view, boolean hasFocus) {
        if (view == mPsdOneTie) {
            Editable text = mPsdOneTie.getText();
            if (hasFocus && text != null && text.length() > 0) {
                mPsdOneIv.setVisibility(View.VISIBLE);
            } else {
                mPsdOneIv.setVisibility(View.GONE);
            }
        } else if (view == mPsdTwoTie) {
            Editable text = mPsdTwoTie.getText();
            if (hasFocus && text != null && text.length() > 0) {
                mPsdTwoIv.setVisibility(View.VISIBLE);
            } else {
                mPsdTwoIv.setVisibility(View.GONE);
            }
        }
    }

    @Override
    public void registerSuccess() {
        LoginSuccessActivity.jumpLoginSuccess(mContext, mRegistrationBean.getUserName(), mRegistrationBean.getPassword(), BaseConstants.JumpFlag.REGISTRATION);
    }

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    public void upLoginPsdSuccess() {
        LoginSuccessActivity.jumpForgetPsdSuccess(mContext, BaseConstants.JumpFlag.FORGETPASSWORD);
    }

    @Override
    public void inviteFriendPsdSuccess() {
        String psdOneStr = mPsdOneTie.getText().toString();
        LoginSuccessActivity.jumpNoActiveSuccess(mContext, BaseConstants.JumpFlag.USERNOACTIVE, mUserName, psdOneStr);
    }
}
