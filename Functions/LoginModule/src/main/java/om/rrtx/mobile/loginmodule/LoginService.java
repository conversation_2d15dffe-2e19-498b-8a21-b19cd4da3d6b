package om.rrtx.mobile.loginmodule;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import okhttp3.MultipartBody;
import om.rrtx.mobile.loginmodule.bean.UploadBean;
import om.rrtx.mobile.rrtxcommon1.bean.CheckAccountResultBean;
import om.rrtx.mobile.loginmodule.bean.PubBean;
import om.rrtx.mobile.loginmodule.bean.RegionBean;
import om.rrtx.mobile.loginmodule.bean.RegisterResultBean;
import om.rrtx.mobile.loginmodule.bean.UpDataBean;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.rrtxcommon1.bean.AppDictListBean;
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean;
import om.rrtx.mobile.rrtxcommon1.bean.LoginBean;
import om.rrtx.mobile.rrtxcommon1.bean.UpgradeInfoBean;
import retrofit2.Response;
import retrofit2.http.Body;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.Header;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;

/**
 * 登录的Api接口层
 */
public interface LoginService {


    /**
     * 获取公钥的接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(LoginConstants.URL.GETPUB)
    Observable<Response<BaseBean<PubBean>>> requestPub(@FieldMap Map<String, String> formData);

    /**
     * 请求登录接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(LoginConstants.URL.LOGIN)
    Observable<Response<BaseBean<LoginBean>>> requestLogin(@FieldMap Map<String, String> formData);

    /**
     * 请求时区接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(LoginConstants.URL.RAGION)
    Observable<Response<BaseBean<List<RegionBean>>>> requestRegion(@FieldMap Map<String, String> formData);

    /**
     * 请求注册接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(LoginConstants.URL.REGISTER)
    Observable<Response<BaseBean<Object>>> requestRegistration(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(LoginConstants.URL.JUNIOR_REGISTER)
    Observable<Response<BaseBean<Object>>> requestJuniorRegistration(@FieldMap Map<String, String> formData);

    /**
     * 上传图片
     *
     * @param body 实体信息
     * @return 相应的Observable
     */
    @Multipart
    @POST(UserConstants.URL.UPLOADPHOTO)
    Observable<Response<BaseBean<UploadBean>>> uploadPhoto(@Part MultipartBody.Part body);


    /**
     * 请求注册接口校验
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(LoginConstants.URL.REGISTERVALIDATE)
    Observable<Response<BaseBean<RegisterResultBean>>> requestValidate(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(LoginConstants.URL.REGISTER_PHONE_VALIDATE)
    Observable<Response<BaseBean<RegisterResultBean>>> requestPhoneValidate(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(LoginConstants.URL.JUNIOR_REGISTER_VALIDATE)
    Observable<Response<BaseBean<RegisterResultBean>>> requestJuniorValidate(@FieldMap Map<String, String> formData);

    /**
     * 忘记用户名
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(LoginConstants.URL.FORGETUSERNAME)
    Observable<Response<BaseBean<Object>>> requestForgetName(@FieldMap Map<String, String> formData);


    /**
     * 设置登录密码
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(LoginConstants.URL.PSDSETUP)
    Observable<Response<BaseBean<Object>>> setUpLoginPsd(@FieldMap Map<String, String> formData);

    /**
     * 邀请好友密码设置
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(LoginConstants.URL.INVIRWFRIENDSETUP)
    Observable<Response<BaseBean<Object>>> setInviteFriendsPsd(@FieldMap Map<String, String> formData);

    /**
     * 检查版本更新
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(LoginConstants.URL.APPVERSION)
    Observable<Response<BaseBean<UpDataBean>>> requestUpData(@FieldMap Map<String, String> formData);

    /**
     * 检查版本更新
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(LoginConstants.URL.AUTHCODEVALIDATE)
    Observable<Response<BaseBean<LoginBean>>> requestValidateCode(@FieldMap Map<String, String> formData);


    /**
     * 获取区域信息
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.APPDICTCODE)
    Observable<Response<BaseBean<AppDictListBean>>> requestDictCodeList(@FieldMap Map<String, String> formData);

    @POST(UserConstants.URL.Check_Upgrade_Junior_Info)
    Observable<Response<BaseBean<CheckAccountResultBean>>> requestCheckUpGradeInfo(@Body UpgradeInfoBean bean);


}
