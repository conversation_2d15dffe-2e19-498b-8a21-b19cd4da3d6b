package om.rrtx.mobile.loginmodule.adapter

import android.view.LayoutInflater
import android.view.View
import om.rrtx.mobile.loginmodule.R
import android.view.ViewGroup
import android.widget.ImageButton
import android.widget.ImageView
import android.widget.RelativeLayout
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.Glide

class PhotoAdapter(private val photoNumber: Int,private val listener: OnItemClickListener) :
    RecyclerView.Adapter<PhotoAdapter.PhotoViewHolder>() {

    companion object {
        const val TYPE_ADD = 0
        const val TYPE_IMAGE = 1
    }

    private val photoUrls = mutableListOf<String>()

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): PhotoViewHolder {
        val view = LayoutInflater.from(parent.context).inflate(R.layout.item_photo, parent, false)
        return PhotoViewHolder(view)
    }

    override fun onBindViewHolder(holder: PhotoViewHolder, position: Int) {
        if (position == 0 && photoUrls.size < photoNumber + 1) {
            // 第一张显示添加图标
            holder.relOne.visibility = View.VISIBLE
            holder.ibDelete.visibility = View.GONE
            holder.tvNumber.text = "${photoUrls.size}/$photoNumber"
        } else {
            // 显示实际图片
            Glide.with(holder.itemView.context)
                .load(photoUrls[position - 1])
                .into(holder.ivPhoto)
            holder.ibDelete.visibility = View.VISIBLE
            holder.relOne.visibility = View.GONE
        }

        holder.ibDelete.setOnClickListener {
            if (position > 0) {
                listener.onItemDeleted(position - 1)
            }
        }

        holder.itemView.setOnClickListener {
            if (position == 0 && photoUrls.size < photoNumber + 1) {
                listener.onAddPhotoClicked()
            }
        }
    }

    override fun getItemCount(): Int {
        return photoUrls.size + 1
    }

    fun addPhoto(url: String) {
        photoUrls.add(url)
        notifyDataSetChanged()
    }

    fun removePhoto(position: Int) {
        if (position in 0 until photoUrls.size) {
            photoUrls.removeAt(position)
            notifyDataSetChanged()
        }
    }

    interface OnItemClickListener {
        fun onAddPhotoClicked()
        fun onItemDeleted(position: Int)
    }

    inner class PhotoViewHolder(itemView: View) : RecyclerView.ViewHolder(itemView) {
        val ivPhoto: ImageView = itemView.findViewById(R.id.iv_photo)
        val ibDelete: ImageButton = itemView.findViewById(R.id.ib_delete)
        val relOne: RelativeLayout = itemView.findViewById(R.id.rel_one)
        val tvNumber: TextView = itemView.findViewById(R.id.tv_num)
    }
}
