package om.rrtx.mobile.loginmodule.bean;

/**
 * <AUTHOR>
 * 注册的信息
 */
public class RegistrationBean {
    private String fullName;
    private String userName;
    private String nric;
    private String mobile;
    private String password;
    private String confirmPwd;
    private String code;
    private String mobileAreaCode;

    public String getMobileAreaCode() {
        return mobileAreaCode;
    }

    public void setMobileAreaCode(String mobileAreaCode) {
        this.mobileAreaCode = mobileAreaCode;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getNric() {
        return nric;
    }

    public void setNric(String nric) {
        this.nric = nric;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }


    public void setMobile(String mobile) {
        this.mobile = mobile;
    }


    public void setUserName(String userName) {
        this.userName = userName;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public void setConfirmPwd(String confirmPwd) {
        this.confirmPwd = confirmPwd;
    }


    public String getFullName() {
        return fullName;
    }

    public String getMobile() {
        return mobile;
    }

    public String getUserName() {
        return userName;
    }

    public String getPassword() {
        return password;
    }

    public String getConfirmPwd() {
        return confirmPwd;
    }


    public RegistrationBean(Builder builder) {
        this.fullName = builder.fullName;
        this.mobile = builder.mobile;
        this.nric = builder.nric;
        this.userName = builder.userName;
        this.password = builder.password;
        this.confirmPwd = builder.confirmPwd;
        this.code = builder.code;
        this.mobileAreaCode = builder.mobileAreaCode;
    }

    public static final class Builder {
        String fullName;
        String nric;
        String mobile;
        String userName;
        String password;
        String confirmPwd;
        String code;
        String mobileAreaCode;

        public Builder setMobileAreaCode(String mobileAreaCode) {
            this.mobileAreaCode = mobileAreaCode;
            return this;
        }

        public Builder setCode(String code) {
            this.code = code;
            return this;
        }

        public Builder setFullName(String fullName) {
            this.fullName = fullName;
            return this;
        }

        public Builder setNric(String nric) {
            this.nric = nric;
            return this;
        }


        public Builder setMobile(String mobile) {
            this.mobile = mobile;
            return this;
        }


        public Builder setUserName(String userName) {
            this.userName = userName;
            return this;
        }

        public Builder setPassword(String password) {
            this.password = password;
            return this;
        }

        public Builder setConfirmPwd(String confirmPwd) {
            this.confirmPwd = confirmPwd;
            return this;
        }

        public RegistrationBean build() {
            return new RegistrationBean(this);
        }
    }
}
