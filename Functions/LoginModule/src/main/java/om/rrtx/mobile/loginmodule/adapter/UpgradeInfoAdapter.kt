package om.rrtx.mobile.loginmodule.adapter

import android.text.Editable
import android.text.InputFilter
import android.text.Spanned
import android.view.View
import android.widget.EditText
import androidx.recyclerview.widget.RecyclerView
import om.rrtx.mobile.functioncommon.dialog.CommonMoreDataDialog
import om.rrtx.mobile.loginmodule.R
import om.rrtx.mobile.loginmodule.databinding.HomeItemUpgradeInfoBinding
import om.rrtx.mobile.rrtxcommon1.base.BaseAdapterDataCallback
import om.rrtx.mobile.rrtxcommon1.base.BaseRecyclerViewAdapter
import om.rrtx.mobile.rrtxcommon1.bean.AppDictListBean
import om.rrtx.mobile.rrtxcommon1.bean.InfoItemBean
import om.rrtx.mobile.rrtxcommon1.bean.UpgradeInfoBean
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.TextChangeEndListener

class UpgradeInfoAdapter(val mBean: UpgradeInfoBean, val callback: BaseAdapterDataCallback<UpgradeInfoBean>) :
    BaseRecyclerViewAdapter<InfoItemBean, HomeItemUpgradeInfoBinding>() {

    private lateinit var appDictCodeList: List<AppDictListBean.AppDictBean>
    fun setDictCodeList(list: List<AppDictListBean.AppDictBean>) {
        appDictCodeList = list
    }

    override fun getItemCount() = 5
    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        recyclerView.recycledViewPool.setMaxRecycledViews(0, 0);
    }

    override fun getItemId() = R.layout.home_item_upgrade_info
    override fun handleItemData(binding: HomeItemUpgradeInfoBinding, position: Int) {
        binding.apply {

            when (position) {
                0 -> {
                    titleTv.setText(R.string.id_type)
                    moreIv.visibility = View.VISIBLE
                    textEd.isEnabled = false
                    moreIv.setOnClickListener(object : CustomClickListener() {
                        override fun onSingleClick(view: View) {
                            showIdTypeDialog(textEd)
                        }
                    })
                }

                1 -> {
                    titleTv.setText(R.string.id_number)
                    moreIv.visibility = View.GONE
                    textEd.isEnabled = true
                    textEd.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(12))
                    textEd.addTextChangedListener(object : TextChangeEndListener {
                        override fun textChangeEnd(editable: Editable) {
                            val number = editable.toString()
                            mBean.idNumber = number
                            callback.callBack(1,mBean)
                        }
                    })
                }

                2 -> {
                    titleTv.setText(R.string.location)
                    moreIv.visibility = View.VISIBLE
                    textEd.isEnabled = false
                    moreIv.setOnClickListener(object : CustomClickListener() {
                        override fun onSingleClick(view: View) {
                            showLocationDialog(textEd)
                        }
                    })
                }

                3 -> {
                    titleTv.setText(R.string.address)
                    moreIv.visibility = View.GONE
                    textEd.isEnabled = true
                    textEd.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(100))
                    val filter =
                        InputFilter { source: CharSequence, start: Int, end: Int, dest: Spanned?, dstart: Int, dend: Int ->
                            val regex = "[0-9a-zA-Z ]+"
                            if (source.isNotEmpty() && !source.toString().matches(regex.toRegex())) {
                                return@InputFilter "" // 不是字母、数字或空格，则返回空字符串，表示不允许输入
                            }
                            null // 允许输入
                        }

                    textEd.filters = arrayOf<InputFilter>(filter)
                    textEd.addTextChangedListener(object : TextChangeEndListener {
                        override fun textChangeEnd(editable: Editable) {
                            val address = editable.toString()
                            mBean.address = address
                            callback.callBack(3,mBean)
                        }
                    })
                }

                4 -> {
                    titleTv.setText(R.string.email)
                    moreIv.visibility = View.GONE
                    textEd.isEnabled = true
                    textEd.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(50))
                    textEd.hint = context.getString(R.string.email_optional)
                    textEd.addTextChangedListener(object : TextChangeEndListener {
                        override fun textChangeEnd(editable: Editable) {
                            val email = editable.toString()
                            mBean.email = email
                            callback.callBack(4,mBean)
                            //只允许英文字母、数字、下划线、英文句号、以及中划线组成
                            /*val p = Pattern.compile("^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+");
                            val group = p.matcher(textEd.text).matches()
                            if (group) {
                                mBean.email = email
                            } else {
                                ToastUtil.show(context,context.getString(R.string.email_error))
                                mBean.email = ""
                            }*/
                        }
                    })
                }
            }
        }
    }

    private fun showIdTypeDialog(textEd: EditText) {
        var stringArray = context.resources.getStringArray(om.rrtx.mobile.rrtxcommon1.R.array.id_type)
        val entries = listOf<String>(stringArray[0],stringArray[1])

        val mOldCurrencyDialog =
            CommonMoreDataDialog(context, "1", entries)
        mOldCurrencyDialog.setOnCurrencyClickListener(CommonMoreDataDialog.OnCurrencyClickListener { position, value ->
            textEd.setText(value)
            if (position == 0) {
                mBean.idType = "10"
            } else {
                mBean.idType = "20"
            }
            callback.callBack(0,mBean)
        })
        mOldCurrencyDialog.show()
    }

    private fun showLocationDialog(textEd: EditText) {
        //if (this@UpgradeInfoAdapter::appDictCodeList.isInitialized)

        val entries = arrayListOf<String>()
        for (bean in appDictCodeList) {
            entries.add(bean.dictLabel)
        }

        val mOldCurrencyDialog =
            CommonMoreDataDialog(context, "1", entries)
        mOldCurrencyDialog.setOnCurrencyClickListener(CommonMoreDataDialog.OnCurrencyClickListener { position, value ->
            textEd.setText(value)
            mBean.cityCode = appDictCodeList[position].dictValue
            callback.callBack(2,mBean)
        })
        mOldCurrencyDialog.show()
    }
}