package om.rrtx.mobile.loginmodule.activity;

import android.content.Context;
import android.content.Intent;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.inputmethod.InputMethodManager;

import androidx.lifecycle.ViewModelProvider;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.gyf.immersionbar.ImmersionBar;

import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.loginmodule.LoginConstants;
import om.rrtx.mobile.loginmodule.R;
import om.rrtx.mobile.loginmodule.databinding.LoginActivityTemporaryBinding;
import om.rrtx.mobile.loginmodule.utils.BaseClick;
import om.rrtx.mobile.loginmodule.viewmodel.TemporaryViewModel;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;
import om.rrtx.mobile.rrtxcommon1.widget.VerificationCodeEditText;

/**
 * <AUTHOR>
 * 临时设备登录 验证登录密码
 */
@Route(path = ARouterPath.LoginPath.TemporaryActivity)
public class TemporaryActivity extends BaseActivity<LoginActivityTemporaryBinding> implements TextWatcher {

    private TemporaryViewModel mTemporaryViewModel;
    private String mUserName;
    private String mPassWord;
    private String mType;

    public static void jumpTemporary(Context context, String nameStr, String psdStr, String type) {
        Intent intent = new Intent(context, TemporaryActivity.class);
        intent.putExtra(LoginConstants.Transmit.USERNAME, nameStr);
        intent.putExtra(LoginConstants.Transmit.PASSWORD, psdStr);
        intent.putExtra(LoginConstants.Transmit.TYPE, type);
        context.startActivity(intent);
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        mUserName = getIntent().getStringExtra(LoginConstants.Transmit.USERNAME);
        mPassWord = getIntent().getStringExtra(LoginConstants.Transmit.PASSWORD);
        mType = getIntent().getStringExtra(LoginConstants.Transmit.TYPE);
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    protected int createContentView() {
        return R.layout.login_activity_temporary;
    }

    @Override
    protected void initWorkspaceAction() {
        dataBinding.setTemporaryOpt(new TemporaryOpt());
        mTemporaryViewModel = new ViewModelProvider(this).get(TemporaryViewModel.class);

        if (!mTemporaryViewModel.getLoginBeanMutableLiveData().hasObservers()) {
            mTemporaryViewModel.getLoginBeanMutableLiveData().observe(this, loginBean -> {
                //保存设置支付密码状态
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PAYMENTPASSWORDSETUPFLAG, loginBean.getPinSetupFlag());
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.REALNAME, loginBean.getRealName());
                //保存用户名称
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.MASKNAME, loginBean.getMaskName());
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERNAME, loginBean.getNickName());
                //保存用户电话
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERMOBILE, loginBean.getMobile());
                //保存用户id
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERCARDID, loginBean.getIdCard());
                //保存用户头像
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERHEARD, loginBean.getUserAvatar());
                //保存用户ID
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERID, loginBean.getUserId());
                //nric
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.IDCARD, loginBean.getIdCard());
                //记录区号
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.MOBILEAREACODE, loginBean.getMobileAreaCode());
                //记录币种
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.SAVECURRENCY, TextUtils.isEmpty(loginBean.getCurrency()) ? "" : loginBean.getCurrency());
                //这里应该把之前的用户指纹状态关闭
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.ISFINGER, false);
                //账户类型 0-常规账户 1-亲子账户
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.ACCOUNTTYPE, loginBean.getAccountType());

                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .navigation();
                BaseApp.setIsLock(false);
                BaseApp.setIsPromotion(true);
            });
        }

        if (!mTemporaryViewModel.getErrorLiveData().hasObservers()) {
            mTemporaryViewModel.getErrorLiveData().observe(this, s -> {
                ToastUtil.show(mContext, s);
            });
        }
    }

    @Override
    public void initView() {
        super.initView();
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        dataBinding.includeTitle.backIv.setImageResource(R.drawable.login_ic_back);

        dataBinding.includeTitle.titleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        dataBinding.includeTitle.titleTv.setTextColor(getResources().getColor(R.color.color_131313));
        dataBinding.includeTitle.titleTv.setText(R.string.login_title_login);

        dataBinding.hintTv.setText(R.string.login_label_tempdevice_tip);
        dataBinding.hint2Tv.setText(R.string.login_label_authcode_input_tip);
    }

    @Override
    public void initListener() {
        super.initListener();
        dataBinding.code.setVerificationCallBack(new VerificationCodeEditText.VerificationCallBack() {
            @Override
            public void showKeyBoard() {
                InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
                if (imm != null) {
                    imm.showSoftInput(dataBinding.code, InputMethodManager.SHOW_IMPLICIT);
                }
            }
        });

        dataBinding.code.addTextChangedListener(this);
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        String code = dataBinding.code.getText().toString();
        if (code.length() == 6) {
            mTemporaryViewModel.requestValidateCode(mContext, mUserName, mPassWord, code,mType);
        }
    }

    public class TemporaryOpt extends BaseClick {

        @Override
        public void leftClick() {
            finish();
        }
    }
}