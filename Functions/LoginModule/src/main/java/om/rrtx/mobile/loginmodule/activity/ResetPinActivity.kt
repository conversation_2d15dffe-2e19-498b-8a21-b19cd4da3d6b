package om.rrtx.mobile.loginmodule.activity

import android.app.Activity
import android.content.Intent
import android.text.Editable
import android.text.TextUtils
import android.view.View
import android.widget.EditText
import com.alibaba.android.arouter.facade.annotation.Route
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.login_activity_reset_pin.id_ed
import kotlinx.android.synthetic.main.login_activity_reset_pin.next_tv
import kotlinx.android.synthetic.main.login_base_title.backIv
import kotlinx.android.synthetic.main.login_base_title.leftBg
import kotlinx.android.synthetic.main.login_base_title.titleTv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.activity.VerCodeActivity
import om.rrtx.mobile.loginmodule.R
import om.rrtx.mobile.loginmodule.databinding.LoginActivityResetPinBinding
import om.rrtx.mobile.loginmodule.viewmodel.LoginViewModel
import om.rrtx.mobile.rrtxcommon.utils.toast.ToastUtil
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseVVMActivity
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.TextChangeEndListener

@Route(path = ARouterPath.LoginPath.ResetPinActivity)
class ResetPinActivity : BaseVVMActivity<LoginViewModel, LoginActivityResetPinBinding>() {

    private lateinit var mobile_ed: EditText
    var mJumpFlag = ""
    var mobile = "";
    override fun createContentView() = R.layout.login_activity_reset_pin

    override fun doGetExtra() {
        super.doGetExtra()
        mJumpFlag = intent.getStringExtra(BaseConstants.Transmit.JUMPFLAG).toString()
        mobile = intent.getStringExtra(BaseConstants.Transmit.MOBILE).toString()
    }

    override fun initView() {

        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true)
            .init()

        dataBinding.mobileInclude.apply {
            mobile_ed = mobileEd
            addressBockIv.visibility = View.GONE
        }
        titleTv.setText(R.string.reset_PIN)
        titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
        backIv.setBackgroundResource(R.drawable.common_ic_back_black)
        titleTv.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))

        mobile_ed.isEnabled = false
        if (!TextUtils.isEmpty(mobile) && "null" != mobile){
            mobile_ed.setText(mobile)
        }

    }

    override fun initClickListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    leftBg -> onBackPressed()
                    next_tv -> onNextClick()
                }
            }

        }.apply {
            leftBg.setOnClickListener(this)
            next_tv.setOnClickListener(this)
        }

        object : TextChangeEndListener {
            override fun textChangeEnd(editable: Editable) {
                if (editable.isEmpty()) {
                    next_tv.isEnabled = false
                } else {
                    next_tv.isEnabled = id_ed.text.isNotEmpty()
                }

            }
        }.apply {
            id_ed.addTextChangedListener(this)
        }
    }

    override fun initVMListener() {
        viewModel.mCheckUserLd.observe(this) {
            SharedPreferencesUtils.setParam(mContext,
                BaseConstants.SaveParameter.USERCARDID,
                it.idCard)
            SharedPreferencesUtils.setParam(mContext,
                BaseConstants.SaveParameter.USERNAME,
                it.userName)
            VerCodeActivity.jump(mContext, it.mobile, mJumpFlag)
        }
    }

    private fun onNextClick() {
        val mobile = mobile_ed.text.toString()
        if (!StringUtils.isValidMobile(mobile)) {
            ToastUtil.show(mContext, getString(R.string.mobile_error))
            return
        }

        val idNumber = id_ed.text.toString()
        if (!StringUtils.isValidIdNumber(idNumber)) {
            ToastUtil.show(mContext, getString(R.string.idcard_error))
            return
        }

        viewModel.getUserInfoByIdCardAndMobile(mobile,idNumber)
    }


    companion object {
        @JvmStatic
        fun jump(mContext: Activity, jumpFlag: String,mobile:String) {
            val intent = Intent(mContext, ResetPinActivity::class.java)
            intent.putExtra(BaseConstants.Transmit.JUMPFLAG, jumpFlag)
            intent.putExtra(BaseConstants.Transmit.MOBILE, mobile)
            mContext.startActivity(intent)
        }
    }
}