package om.rrtx.mobile.loginmodule;

/**
 * <AUTHOR>
 * Login模块的数据整理
 */
public class LoginConstants1 {


    public interface Parameter {
        /**
         * 名字
         */
        String AREACODE = "areaCode";
        String RELATION_SHIP = "relationShip";

        /**
         * 电话号码
         */
        String MOBILE = "mobile";

        /**
         * 名字
         */
        String FIRSTNAME = "firstName";
        /**
         * 姓氏
         */
        String LASTNAME = "lastName";
        /**
         *
         */
        String MIDDLENAME = "middleName";

        /**
         * 性别（1:女 2:男）
         */
        String GENDER = "gender";

        /**
         * 生日(yyyyMMdd)
         */
        String BIRTHDAY = "dateOfBirth";

        /**
         * 证件类型（10-身份证 20-护照）
         */
        String IDTYPE = "idType";

        /**
         * 证件号
         */
        String IDNUMBER = "idNumber";

        /**
         * 省市编码
         */
        String CITYCODE = "cityCode";

        /**
         * 地址
         */
        String ADDRESS = "address";
        String CHECK_TOKEN = "checkToken";

        /**
         * 邮箱
         */
        String EMAIL = "email";

        /**
         * password
         */
        String PASSWORD = "password";
    }

}
