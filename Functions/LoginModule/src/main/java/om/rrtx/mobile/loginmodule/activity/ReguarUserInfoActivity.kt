package om.rrtx.mobile.loginmodule.activity

import android.Manifest
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.text.InputFilter
import android.util.Log
import android.view.View
import android.widget.EditText
import androidx.fragment.app.FragmentActivity
import androidx.recyclerview.widget.GridLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.Gson
import com.gyf.immersionbar.ImmersionBar
import com.tbruyelle.rxpermissions2.RxPermissions
import io.reactivex.disposables.Disposable
import io.reactivex.functions.Consumer
import kotlinx.android.synthetic.main.login_base_title.backIv
import kotlinx.android.synthetic.main.login_base_title.titleTv
import kotlinx.android.synthetic.main.login_reguar_info.email_TIL
import kotlinx.android.synthetic.main.login_reguar_info.tv_all_step
import kotlinx.android.synthetic.main.login_reguar_info.tv_cur_page
import kotlinx.android.synthetic.main.login_reguar_info.tv_cur_step
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.dialog.CommonMoreDataDialog
import om.rrtx.mobile.loginmodule.R
import om.rrtx.mobile.loginmodule.adapter.PhotoAdapter
import om.rrtx.mobile.loginmodule.bean.RegisterInfoBean
import om.rrtx.mobile.loginmodule.databinding.LoginReguarInfoBinding
import om.rrtx.mobile.loginmodule.dialog.PictureUploadHelper
import om.rrtx.mobile.loginmodule.login.viewModel.UserinfoRegisterVM
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseVVMActivity
import om.rrtx.mobile.rrtxcommon1.bean.AppDictListBean.AppDictBean
import om.rrtx.mobile.rrtxcommon1.dialog.AloneDialog
import om.rrtx.mobile.rrtxcommon1.dialog.DateSelectDialog
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils
import om.rrtx.mobile.rrtxcommon1.utils.SoftKeyBoardListener
import om.rrtx.mobile.rrtxcommon1.utils.TimeUtils
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.util.Date
import java.util.regex.Pattern


/**
 * 常规账户注册4
 * */
@Route(path = ARouterPath.LoginPath.ReguarUserInfoActivity)
class ReguarUserInfoActivity : BaseVVMActivity<UserinfoRegisterVM, LoginReguarInfoBinding>(), PictureUploadHelper.SnapshotCallBack{

    var mJumpFlag = ""
    private var mRxPermissions: RxPermissions? = null
    private var adapter: PhotoAdapter? = null
    private var faceAdapter: PhotoAdapter? = null
    //private var mSelectPictureDialog: SelectPictureDialog? = null
    private var mPictureHelper: PictureUploadHelper? = null
    // 图片 URL 列表
    private val idPhotoUrls = mutableListOf<String>()
    private val facePhotoUrls = mutableListOf<String>()

    private lateinit var curEditText: EditText
    var registerInfoBean: RegisterInfoBean = RegisterInfoBean()
    private lateinit var appDictCodeList: List<AppDictBean>

    companion object {
        @JvmStatic
        fun jump(context: Context, flag: String) {
            val intent = Intent(context, ReguarUserInfoActivity::class.java)
            intent.putExtra(BaseConstants.Transmit.JUMPFLAG, flag)
            context.startActivity(intent)
        }
    }

    override fun doGetExtra() {
        super.doGetExtra()
        mJumpFlag = intent.getStringExtra(BaseConstants.Transmit.JUMPFLAG).toString()
    }

    override fun createContentView() = R.layout.login_reguar_info

    override fun initView() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true)
            .init()
        val registerJson =
            SharedPreferencesUtils.getParam(this, BaseConstants.SaveParameter.REGISTER_INFO, "")
                .toString()
        if (registerJson.isNotEmpty()) {
            registerInfoBean = Gson().fromJson(registerJson, RegisterInfoBean::class.java)
        }

        backIv.background = ResourceHelper.getDrawable(R.drawable.login_ic_back)
        titleTv.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
        titleTv.setTextColor(ResourceHelper.getColor(R.color.color_131313))
        titleTv.text = getString(R.string.register_title_user_registration)
        email_TIL.visibility = View.VISIBLE

        backIv.setOnClickListener {
            onBackPressed()
        }

        tv_cur_step.text = getString(R.string.user_info)
        tv_cur_page.text = "4"
        tv_all_step.text = "/6"

        dataBinding.hintTv.text = getString(R.string.users_must_be_at_least_16_years_old)
        val iconId = R.drawable.ic_hint
        val drawableLeft = ResourceHelper.getDrawable(iconId)
        drawableLeft?.setBounds(0, 0, 24.pt2px(), 24.pt2px())
        dataBinding.hintTv.setCompoundDrawables(drawableLeft, null, null, null);
        dataBinding.hintTv.compoundDrawablePadding = 10.pt2px()

        mRxPermissions = RxPermissions((mContext as FragmentActivity))
        mPictureHelper = PictureUploadHelper(this)

        // 身份证照片列表
        dataBinding.rvPhotos.layoutManager = object : GridLayoutManager(mContext, 3) {
            override fun canScrollVertically(): Boolean = false
        }
        adapter = PhotoAdapter(3,object : PhotoAdapter.OnItemClickListener {
            override fun onAddPhotoClicked() = <EMAIL>()
            override fun onItemDeleted(position: Int) = <EMAIL>(position)
        })
        dataBinding.rvPhotos.adapter = adapter

        // 人脸照片列表
        dataBinding.facePhotos.layoutManager = object : GridLayoutManager(mContext, 3) {
            override fun canScrollVertically(): Boolean = false
        }
        faceAdapter = PhotoAdapter(2,object : PhotoAdapter.OnItemClickListener {
            override fun onAddPhotoClicked() = <EMAIL>()
            override fun onItemDeleted(position: Int) = <EMAIL>(position)
        })
        dataBinding.facePhotos.adapter = faceAdapter

    }

    override fun initListener() {
        initFocusListener()
        initOnClickListener()
        initEndIconListener()
        initVResultListener()
    }

    /**
     * 姓名、姓氏、证件号、地址、邮箱
     */
    private fun initFocusListener() {
        View.OnFocusChangeListener { view, b ->
            val ed = view as EditText
            val text = ed.text.toString()
            when (ed) {
                dataBinding.firstNameTIL.editText -> {
                    registerInfoBean.firstName = text
                }

                dataBinding.middleNameTIL.editText -> {
                    registerInfoBean.middleName = text
                }

                dataBinding.lastNameTIL.editText -> {
                    registerInfoBean.lastName = text
                }

                dataBinding.idNumberTIL.editText -> {
                    registerInfoBean.idNumber = text
                }

                dataBinding.addressTIL.editText -> {
                    registerInfoBean.address = text
                }

                dataBinding.emailTIL.editText -> {
                    if (ed.text.toString() == "") return@OnFocusChangeListener
//                    只允许英文字母、数字、下划线、英文句号、以及中划线组成
                    val p = Pattern.compile("^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+");
                    val group = p.matcher(view.text).matches()
                    if (group) {
                        dataBinding.emailError.visibility = View.GONE
                        registerInfoBean.email = text
                    } else {
                        dataBinding.emailError.visibility = View.VISIBLE
                    }
                }
            }
            upInfo()

        }.apply {
            dataBinding.firstNameTIL.editText?.onFocusChangeListener = this
            dataBinding.middleNameTIL.editText?.onFocusChangeListener = this
            dataBinding.lastNameTIL.editText?.onFocusChangeListener = this
            dataBinding.idNumberTIL.editText?.onFocusChangeListener = this
            dataBinding.addressTIL.editText?.onFocusChangeListener = this
            dataBinding.emailTIL.editText?.onFocusChangeListener = this
        }
    }

    private fun initOnClickListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                if (this@ReguarUserInfoActivity::curEditText.isInitialized) curEditText.clearFocus()
                when (view) {
                    dataBinding.tvMale -> {
                        dataBinding.tvMale.setTextColor(ResourceHelper.getColor(R.color.common_ye_F3881E))
                        dataBinding.tvMale.background =
                            ResourceHelper.getDrawable(R.drawable.shape_gender_select)
                        dataBinding.tvFemale.setTextColor(ResourceHelper.getColor(R.color.common_text_4E5969))
                        dataBinding.tvFemale.background =
                            ResourceHelper.getDrawable(R.drawable.shape_gender_unselect)
                        registerInfoBean.gender = "2"
                    }

                    dataBinding.tvFemale -> {
                        dataBinding.tvFemale.setTextColor(ResourceHelper.getColor(R.color.common_ye_F3881E))
                        dataBinding.tvFemale.background =
                            ResourceHelper.getDrawable(R.drawable.shape_gender_select)
                        dataBinding.tvMale.setTextColor(ResourceHelper.getColor(R.color.common_text_4E5969))
                        dataBinding.tvMale.background =
                            ResourceHelper.getDrawable(R.drawable.shape_gender_unselect)
                        registerInfoBean.gender = "1"
                    }

                    dataBinding.tvNext -> onNext()
                }
            }

        }.apply {
            dataBinding.tvMale.setOnClickListener(this)
            dataBinding.tvFemale.setOnClickListener(this)
            dataBinding.tvNext.setOnClickListener(this)
        }
    }

    private fun initEndIconListener() {
        dataBinding.birthdayTIL.editText?.setOnClickListener {
            DateSelectDialog(mContext!!, mJumpFlag, object : DateSelectDialog.DialogResult {
                override fun onConfirm(year: Int, monthO: Int, date: Int) {
                    val date = Date(year - 1900, monthO - 1, date)
                    dataBinding.birthdayTIL.editText?.setText(TimeUtils.date2StringDMY(date))
                    registerInfoBean.dateOfBirth = TimeUtils.date2StringDMY(date).toString()
                    upInfo()
                }
            }).show()
        }

        dataBinding.idTypeTIL.editText?.setOnClickListener {
            var stringArray = if (BaseConstants.JumpFlag.Register_junior_Account == mJumpFlag) {
                mContext.resources.getStringArray(om.rrtx.mobile.rrtxcommon1.R.array.id_type_junior)
            } else {
                mContext.resources.getStringArray(om.rrtx.mobile.rrtxcommon1.R.array.id_type)
            }
            val entries = listOf<String>(stringArray[0], stringArray[1])

            val mOldCurrencyDialog =
                CommonMoreDataDialog(
                    this@ReguarUserInfoActivity, "1", entries
                )
            mOldCurrencyDialog.setOnCurrencyClickListener(CommonMoreDataDialog.OnCurrencyClickListener { position, value ->
                dataBinding.idTypeTIL.editText?.setText(value)
                if (position == 0) {
                    if (BaseConstants.JumpFlag.Register_junior_Account == mJumpFlag) {
                        registerInfoBean.idType = "30"
                    } else {
                        registerInfoBean.idType = "10"
                        dataBinding.idNumberEd.filters = arrayOf<InputFilter>(
                            InputFilter.LengthFilter(
                                12
                            )
                        )
                    }

                } else {
                    registerInfoBean.idType = "20"
                    dataBinding.idNumberEd.filters = arrayOf<InputFilter>(
                        InputFilter.LengthFilter(
                            50
                        )
                    )
                }
                dataBinding.idNumberEd.setText("")
            })
            mOldCurrencyDialog.show()
        }

        dataBinding.areaTIL.editText?.setOnClickListener {
            if (!this::appDictCodeList.isInitialized) return@setOnClickListener

            val entries = arrayListOf<String>()
            for (bean in appDictCodeList) {
                entries.add(bean.dictLabel)
            }

            val mOldCurrencyDialog =
                CommonMoreDataDialog(
                    this@ReguarUserInfoActivity, "1", entries
                )
            mOldCurrencyDialog.setOnCurrencyClickListener(CommonMoreDataDialog.OnCurrencyClickListener { position, value ->
                dataBinding.areaTIL.editText?.setText(value)
                registerInfoBean.cityCode = appDictCodeList[position].dictValue
                upInfo()
            })
            mOldCurrencyDialog.show()
        }
    }

    private fun initVResultListener() {
        viewModel.uploadResult.observe(this){
            if (it == null) return@observe
            if ("face" == it.photoType){
                faceAdapter!!.addPhoto(it.fileUrl)
                facePhotoUrls.add(it.fileUrl)
                registerInfoBean.facePhotos = facePhotoUrls
            }else{
                adapter!!.addPhoto(it.fileUrl)
                idPhotoUrls.add(it.fileUrl)
                registerInfoBean.idPhotos = idPhotoUrls
            }
            upInfo()
        }

        viewModel.registerResult.observe(this) {
            if (it == null) return@observe
            if (it.isWalletUser == "1") {
                when (it.userStatus) {
                    // 正常 冻结
                    "0", "1" -> {
                        val aloneDialog = AloneDialog(mContext)
                        aloneDialog.show()
                        aloneDialog.setTitleStr(ResourceHelper.getString(R.string.notice))
                        if (BaseConstants.JumpFlag.Register_junior_Account == mJumpFlag) {
                            aloneDialog.setContentStr(
                                ResourceHelper.getString(
                                    R.string.notice_registered,
                                    it.userName
                                )
                            )
                        } else {
                            aloneDialog.setContentStr(
                                ResourceHelper.getString(
                                    R.string.notice_registered2,
                                    it.userName
                                )
                            )
                        }
                    }
                    // 待认证
                    "4" -> {
                        if (it.accountType == "1") {
                            //待认证亲子账户
                            val aloneDialog = AloneDialog(mContext)
                            aloneDialog.show()
                            aloneDialog.setTitleStr(ResourceHelper.getString(R.string.notice))
                            if (BaseConstants.JumpFlag.Register_junior_Account == mJumpFlag) {
                                aloneDialog.setContentStr(ResourceHelper.getString(R.string.notice_go_authentication))
                            } else {
                                aloneDialog.setContentStr(ResourceHelper.getString(R.string.notice_registered1))
                            }
                        } else {
                            val aloneDialog = AloneDialog(mContext)
                            aloneDialog.show()
                            aloneDialog.setTitleStr(ResourceHelper.getString(R.string.notice))
                            if (BaseConstants.JumpFlag.Register_junior_Account == mJumpFlag) {
                                aloneDialog.setContentStr(ResourceHelper.getString(R.string.notice_registered))
                            } else {
                                aloneDialog.setContentStr(ResourceHelper.getString(R.string.notice_registered1))
                            }
                        }

                    }
                }
                if (it.isCardIdUsed == "1") {
                    //证件号重复
                    val aloneDialog = AloneDialog(mContext)
                    aloneDialog.show()
                    aloneDialog.setTitleStr(ResourceHelper.getString(R.string.notice))
                    aloneDialog.setContentStr(ResourceHelper.getString(R.string.notice_idcard))

                }
            } else {
                // 下一步发送验证码
                if (it.checkToken != null) registerInfoBean.checkToken = it.checkToken
                SharedPreferencesUtils.setParam(
                    mContext,
                    BaseConstants.SaveParameter.REGISTER_INFO,
                    Gson().toJson(registerInfoBean)
                );

                jumpSetPin()
            }
        }
        viewModel.dictCodeListResult.observe(this) {
            appDictCodeList = it.appDictCodeList
        }
    }

    private fun jumpSetPin() {
        ARouter.getInstance()
            .build(ARouterPath.SecurityPath.SetRegisterPinActivity)
            .withString(BaseConstants.Transmit.JUMPFLAG, mJumpFlag)
            .withString(BaseConstants.Transmit.PINPAYMENT, BaseConstants.Check.SETFIRST)
            .navigation()
    }

    private fun onNext() {
        Log.e("onNext", "idPhotos:"+registerInfoBean.idPhotos)
        Log.e("onNext", "facePhotos:"+registerInfoBean.facePhotos)
        viewModel.requestValidate(registerInfoBean, 0)
    }

    override fun initData() {
        viewModel.requestDictCodeList()
        SoftKeyBoardListener.setListener(
            mContext,
            object : SoftKeyBoardListener.OnSoftKeyBoardChangeListener {
                override fun keyBoardShow(height: Int) {
                    //键盘显示
                }

                override fun keyBoardHide(height: Int) {
                   upInfo()
                }
            })

    }

    fun upInfo() {
        // 手动触发失焦采集输入信息
        if (this::curEditText.isInitialized) curEditText.clearFocus()
        if (registerInfoBean.firstName.isEmpty()){
            registerInfoBean.firstName = dataBinding.firstNameTIL.editText?.text.toString()
        }
        if (registerInfoBean.lastName.isEmpty()){
            registerInfoBean.lastName = dataBinding.lastNameTIL.editText?.text.toString()
        }
        if (registerInfoBean.idNumber.isEmpty()){
            registerInfoBean.idNumber = dataBinding.idNumberTIL.editText?.text.toString()
        }
        if (registerInfoBean.address.isEmpty()){
            registerInfoBean.address = dataBinding.addressTIL.editText?.text.toString()
        }
        val isEnabled = registerInfoBean.verify()
        dataBinding.tvNext.isEnabled = isEnabled
        if (isEnabled) {
            dataBinding.tvNext.background = ResourceHelper.getDrawable(R.drawable.common_usable_btn)
        } else {
            dataBinding.tvNext.background =
                ResourceHelper.getDrawable(R.drawable.common_unusable_btn)
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel.registerResult.value = null
        viewModel.registerResult.removeObservers(this)
        viewModel.dictCodeListResult.removeObservers(this)
    }

    /**添加身份证照片**/
    private fun onAddPhotoClicked() {
        if (adapter!!.itemCount < 4) {
            val subscribe: Disposable = mRxPermissions!!.request(Manifest.permission.CAMERA)
                .subscribe(Consumer<Boolean> { aBoolean: Boolean ->
                    if (aBoolean) {
                        if (mPictureHelper != null) {
                            mPictureHelper!!.openSnapshot("id",
                                System.currentTimeMillis().toString() + "id" + ".png",
                                "0X98",
                                200,
                                200,
                                this
                            )
                        }
                    }
                },
                    Consumer<Throwable> { throwable: Throwable ->
                        Log.e(
                            "TAG",
                            "accept: " + throwable.message
                        )
                    })
            /*mSelectPictureDialog = SelectPictureDialog(mContext,"id")
            mSelectPictureDialog!!.setSelectPictureCallBack(this)
            mSelectPictureDialog!!.show()*/
        }
    }

    /**删除身份证照片**/
    private fun onItemDeleted(position: Int) {
        adapter!!.removePhoto(position)
        if (position < idPhotoUrls.size) {
            idPhotoUrls.removeAt(position)
            registerInfoBean.idPhotos = idPhotoUrls
        }
    }

    /**添加人脸照片**/
    private fun onAddFacePhotoClicked() {
        if (faceAdapter!!.itemCount < 3) {
            val subscribe: Disposable = mRxPermissions!!.request(Manifest.permission.CAMERA)
                .subscribe(Consumer<Boolean> { aBoolean: Boolean ->
                    if (aBoolean) {
                        if (mPictureHelper != null) {
                            mPictureHelper!!.openSnapshot("face",
                                System.currentTimeMillis().toString() + "face" + ".png",
                                "0X98",
                                200,
                                200,
                                this
                            )
                        }
                    }
                },
                    Consumer<Throwable> { throwable: Throwable ->
                        Log.e(
                            "TAG",
                            "accept: " + throwable.message
                        )
                    })
            /*mSelectPictureDialog = SelectPictureDialog(mContext,"face")
            mSelectPictureDialog!!.setSelectPictureCallBack(this)
            mSelectPictureDialog!!.show()*/
        }
    }

    /**删除人脸照片**/
    private fun onItemFaceDeleted(position: Int) {
        faceAdapter!!.removePhoto(position)
        if (position < facePhotoUrls.size) {
            facePhotoUrls.removeAt(position)
            registerInfoBean.facePhotos = facePhotoUrls
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (mPictureHelper != null) {
            mPictureHelper!!.onActivityResult(requestCode, resultCode, data)
        }
    }
    override fun snapshotCallBack(type: String,mRequestCode: String, bitmap: Bitmap, mPath: String) {
        Log.e("snapshotCallBack", "$type==snapshotCallBack:$mPath")
        Log.e("snapshotCallBack", "原图大小："+bitmap.byteCount)
        // 压缩图片并保存新路径
        val compressedFile = compressBitmapToFile(bitmap, mPath)

        // 获取压缩后的文件大小
        val compressedSize = compressedFile.length()
        Log.e("snapshotCallBack", "压缩后大小：$compressedSize bytes")

        viewModel.uploadPhoto(type,compressedFile)
        /*if ("face" == type){
            faceAdapter!!.addPhoto(mPath!!)
        }else{
            adapter!!.addPhoto(mPath!!)
        }*/
    }

    /**压缩图片**/
    private fun compressBitmapToFile(bitmap: Bitmap, originalPath: String): File {
        val outputStream = ByteArrayOutputStream()

        // 判断原始图片格式，并选择合适的压缩方式
        val format = if (originalPath.endsWith(".png", true)) {
            Bitmap.CompressFormat.PNG
        } else {
            Bitmap.CompressFormat.JPEG
        }

        // 设置压缩质量（100 表示原图质量）
        var quality = 100

        // 对于 JPEG 类型，可以尝试逐步压缩到合适大小
        do {
            outputStream.reset()
            bitmap.compress(format, quality, outputStream)
            quality -= 5 // 每次减少5%的质量
            Log.d("Compress", "Current size: ${outputStream.size() / 1024} KB, Quality: $quality")
        } while (outputStream.size() > 500 * 1024 && quality > 70) // 控制最大为 500KB，最小质量 70%

        // 写入临时文件（可替换为你自己的缓存路径）
        val compressedFile = File(cacheDir, "compressed_${System.currentTimeMillis()}.jpg")
        val fos = FileOutputStream(compressedFile)
        fos.write(outputStream.toByteArray())
        fos.flush()
        fos.close()

        return compressedFile
    }

    /**选择图库**/
   /* override fun openAlbum(type: String) {
        if (mPictureHelper != null) {
            mPictureHelper!!.openAlbum(type,System.currentTimeMillis().toString() + type, "0X99", 200, 200, this);
        }
        if (mSelectPictureDialog != null) {
            mSelectPictureDialog!!.dismiss();
        }
    }*/

    /**拍照**/
    /*override fun openSnapshot(type: String) {
        if (mPictureHelper != null) {
            mPictureHelper!!.openSnapshot(type,
                System.currentTimeMillis().toString() + type + ".png",
                "0X98",
                200,
                200,
                this
            )
        }
        if (mSelectPictureDialog != null) {
            mSelectPictureDialog!!.dismiss()
        }
    }*/

}