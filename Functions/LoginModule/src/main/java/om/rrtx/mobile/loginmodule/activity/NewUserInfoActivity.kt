package om.rrtx.mobile.loginmodule.activity

import android.content.Context
import android.content.Intent
import android.text.Editable
import android.text.InputFilter
import android.text.Spannable
import android.text.SpannableString
import android.text.TextUtils
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.EditText
import android.widget.LinearLayout
import androidx.recyclerview.widget.GridLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.google.gson.Gson
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.login_base_title.backIv
import kotlinx.android.synthetic.main.login_base_title.titleTv
import kotlinx.android.synthetic.main.login_new_user_info.email_TIL
import kotlinx.android.synthetic.main.login_new_user_info.tv_all_step
import kotlinx.android.synthetic.main.login_new_user_info.tv_cur_page
import kotlinx.android.synthetic.main.login_new_user_info.tv_cur_step
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.activity.ConditionActivity
import om.rrtx.mobile.functioncommon.activity.RegisterVerCodeActivity
import om.rrtx.mobile.functioncommon.adapter.OptionsAdapter
import om.rrtx.mobile.functioncommon.dialog.CommonMoreDataDialog
import om.rrtx.mobile.loginmodule.R
import om.rrtx.mobile.loginmodule.bean.RegisterInfoBean
import om.rrtx.mobile.loginmodule.databinding.LoginNewUserInfoBinding
import om.rrtx.mobile.loginmodule.login.viewModel.UserinfoRegisterVM
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseVVMActivity
import om.rrtx.mobile.rrtxcommon1.bean.AppDictListBean.AppDictBean
import om.rrtx.mobile.rrtxcommon1.bean.ConditionBean
import om.rrtx.mobile.rrtxcommon1.bean.InfoItemBean
import om.rrtx.mobile.rrtxcommon1.dialog.AloneDialog
import om.rrtx.mobile.rrtxcommon1.dialog.DateSelectDialog
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.FormatCheck
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils
import om.rrtx.mobile.rrtxcommon1.utils.SoftKeyBoardListener
import om.rrtx.mobile.rrtxcommon1.utils.TimeUtils
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil
import om.rrtx.mobile.rrtxcommon1.widget.SpacesItemDecoration
import java.util.Date
import java.util.regex.Pattern

/**
 * 亲子账户注册1
 * */
@Route(path = ARouterPath.LoginPath.NewUserInfoActivity)
class NewUserInfoActivity : BaseVVMActivity<UserinfoRegisterVM, LoginNewUserInfoBinding>() {

    var mJumpFlag = ""
    private lateinit var curEditText: EditText
    // 协议选择
    private var isAgreement: Boolean = false
    val registerInfoBean: RegisterInfoBean = RegisterInfoBean()
    private lateinit var appDictCodeList: List<AppDictBean>
    private lateinit var conditionBean: ConditionBean
    companion object {
        @JvmStatic
        fun jump(context: Context,flag:String) {
            val intent=Intent(context, NewUserInfoActivity::class.java)
            intent.putExtra(BaseConstants.Transmit.JUMPFLAG,flag)
            context.startActivity(intent)
        }
    }

    override fun doGetExtra() {
        super.doGetExtra()
        mJumpFlag = intent.getStringExtra(BaseConstants.Transmit.JUMPFLAG).toString()
    }

    override fun createContentView() = R.layout.login_new_user_info

    override fun initView() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true)
            .init()

        backIv.background = ResourceHelper.getDrawable(R.drawable.login_ic_back)
        titleTv.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
        titleTv.setTextColor(ResourceHelper.getColor(R.color.color_131313))
        if (BaseConstants.JumpFlag.Register_junior_Account==mJumpFlag)
        {
            titleTv.text = getString(R.string.register_junior)
            email_TIL.visibility = View.GONE
        }else
        {
            titleTv.text = getString(R.string.register_title_user_registration)
            email_TIL.visibility = View.VISIBLE
        }

        backIv.setOnClickListener {
            onBackPressed()
        }

        if (BaseConstants.JumpFlag.Register_junior_Account==mJumpFlag)
        {
            tv_cur_step.text = getString(R.string.junior_info)
            tv_cur_page.text = "1"
            tv_all_step.text = "/5"
        }else{
            tv_cur_step.text = getString(R.string.user_info)
            tv_cur_page.text = "2"
            tv_all_step.text = "/6"
        }

        dataBinding.apply {
            when (mJumpFlag) {
                BaseConstants.JumpFlag.Register_junior_Account -> {
                    initJuniorRegisterView()
                }

                else -> {
                    dataBinding.juniorCl.visibility = View.GONE
                }
            }
        }
    }

    override fun initListener() {
        initFocusListener()
        initOnClickListener()
        initEndIconListener()
        initVResultListener()
        initTextChangedListener()

        dataBinding.cbOk.setOnCheckedChangeListener { bt, b ->
            viewModel.requestDictCodeList()
            isAgreement = b
            upInfo()
        }
    }

    private fun initJuniorRegisterView() {
        dataBinding.apply {
            dataBinding.juniorCl.visibility = View.VISIBLE

            if (BaseConstants.JumpFlag.Register_junior_Account==mJumpFlag)
            {
                dataBinding.hintTv.text = getString(R.string.uses_limit)
            }else{
                dataBinding.hintTv.text = getString(R.string.users_must_be_at_least_16_years_old)
            }

            val iconId = R.drawable.ic_hint
            val drawableLeft = ResourceHelper.getDrawable(iconId)
            drawableLeft?.setBounds(0, 0, 24.pt2px(), 24.pt2px())
            dataBinding.hintTv.setCompoundDrawables(drawableLeft, null, null, null);
            dataBinding.hintTv.compoundDrawablePadding = 10.pt2px()

            dataBinding.relationshipRv.apply {
                layoutManager = GridLayoutManager(mContext, 3)
                val optionsAdapter = OptionsAdapter()
                optionsAdapter.setNewData(arrayListOf(
                    InfoItemBean(getString(R.string.mother),
                        callBack1 = {
                            selectRelationship(it)
                        }),
                    InfoItemBean(getString(R.string.father),
                        callBack1 = {
                            selectRelationship(it)
                        }),
                    InfoItemBean(getString(R.string.legal_Guardian),
                        callBack1 = {
                            selectRelationship(it)
                        })
                ))
                adapter = optionsAdapter
                addItemDecoration(SpacesItemDecoration(LinearLayout.HORIZONTAL))

                // 手动置null过校验
                registerInfoBean.relationShip = null
            }
        }
    }


    private fun selectRelationship(position: Int) {
        registerInfoBean.relationShip = position
        upInfo()
    }


    /**
     * 手机号、姓名、姓氏、证件号、地址、邮箱
     */
    private fun initFocusListener() {
        View.OnFocusChangeListener { view, b ->
            val ed = view as EditText
            if (b) {
                curEditText = ed
                if (dataBinding.mobileTIL.editText==curEditText)
                {
                    dataBinding.mobileTIL.hint = " "
                    dataBinding.mobileHint.visibility = View.VISIBLE
                }
                return@OnFocusChangeListener
            }
            val text = ed.text.toString()
            when (ed) {
                dataBinding.mobileTIL.editText -> {
                    if (TextUtils.isEmpty(dataBinding.mobileTIL.editText?.text.toString())) {
                        dataBinding.mobileTIL.hint = getString(R.string.common_label_mobile)
                        dataBinding.mobileHint.visibility = View.GONE
                        dataBinding.mobileError.visibility = View.GONE

                    }else
                    {
                        dataBinding.mobileTIL.hint = " "
                        dataBinding.mobileHint.visibility = View.VISIBLE
                        mobileFormatCheck(dataBinding.mobileTIL.editText!!.text.toString())

                    }
                }
                dataBinding.firstNameTIL.editText -> {
                    registerInfoBean.firstName = text
                }
                dataBinding.middleNameTIL.editText -> {
                    registerInfoBean.middleName = text
                }
                dataBinding.lastNameTIL.editText -> {
                    registerInfoBean.lastName = text
                }
                dataBinding.idNumberTIL.editText -> {
                    registerInfoBean.idNumber = text
                }

                dataBinding.addressTIL.editText -> {
                    registerInfoBean.address = text
                }
                dataBinding.emailTIL.editText -> {
                    if (ed.text.toString() == "") return@OnFocusChangeListener
//                    只允许英文字母、数字、下划线、英文句号、以及中划线组成
                    val p = Pattern.compile("^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+");
                    val group = p.matcher(view.text).matches()
                    if (group) {
                        dataBinding.emailError.visibility = View.GONE
                        registerInfoBean.email = text
                    } else {
                        dataBinding.emailError.visibility = View.VISIBLE
                    }
                }
            }
            upInfo()

        }.apply {
            dataBinding.mobileTIL.editText?.onFocusChangeListener = this
            dataBinding.firstNameTIL.editText?.onFocusChangeListener = this
            dataBinding.middleNameTIL.editText?.onFocusChangeListener = this
            dataBinding.lastNameTIL.editText?.onFocusChangeListener = this
            dataBinding.idNumberTIL.editText?.onFocusChangeListener = this
            dataBinding.addressTIL.editText?.onFocusChangeListener = this
            dataBinding.emailTIL.editText?.onFocusChangeListener = this
        }
    }

    private fun initOnClickListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                if (this@NewUserInfoActivity::curEditText.isInitialized) curEditText.clearFocus()
                when (view) {
                    dataBinding.tvMale -> {
                        dataBinding.tvMale.setTextColor(ResourceHelper.getColor(R.color.common_ye_F3881E))
                        dataBinding.tvMale.background =
                            ResourceHelper.getDrawable(R.drawable.shape_gender_select)
                        dataBinding.tvFemale.setTextColor(ResourceHelper.getColor(R.color.common_text_4E5969))
                        dataBinding.tvFemale.background =
                            ResourceHelper.getDrawable(R.drawable.shape_gender_unselect)
                        registerInfoBean.gender = "2"
                    }

                    dataBinding.tvFemale -> {
                        dataBinding.tvFemale.setTextColor(ResourceHelper.getColor(R.color.common_ye_F3881E))
                        dataBinding.tvFemale.background =
                            ResourceHelper.getDrawable(R.drawable.shape_gender_select)
                        dataBinding.tvMale.setTextColor(ResourceHelper.getColor(R.color.common_text_4E5969))
                        dataBinding.tvMale.background =
                            ResourceHelper.getDrawable(R.drawable.shape_gender_unselect)
                        registerInfoBean.gender = "1"
                    }

                    dataBinding.tvCondition -> {
                        if (this@NewUserInfoActivity::conditionBean.isInitialized)
                            ConditionActivity.jumpCondition(
                                mContext,
                                conditionBean.conditionTitle,
                                conditionBean.conditionContent
                            )
                    }
                    dataBinding.tvNext -> onNext()
                }
            }

        }.apply {
            dataBinding.tvMale.setOnClickListener(this)
            dataBinding.tvFemale.setOnClickListener(this)
            dataBinding.tvCondition.setOnClickListener(this)
            dataBinding.tvNext.setOnClickListener(this)
        }
    }

    private fun initEndIconListener() {
        dataBinding.birthdayTIL.editText?.setOnClickListener {
            DateSelectDialog(mContext!!, mJumpFlag,object : DateSelectDialog.DialogResult {
                override fun onConfirm(year: Int, monthO: Int, date: Int) {
                    val date = Date(year - 1900, monthO - 1, date)
                    dataBinding.birthdayTIL.editText?.setText(TimeUtils.date2StringDMY(date))
                    registerInfoBean.dateOfBirth = TimeUtils.date2StringDMY(date).toString()
                    upInfo()
                }
            }).show()
        }

        dataBinding.idTypeTIL.editText?.setOnClickListener {
            var stringArray = if (BaseConstants.JumpFlag.Register_junior_Account==mJumpFlag) {
                mContext.resources.getStringArray(om.rrtx.mobile.rrtxcommon1.R.array.id_type_junior)
            }else{
                mContext.resources.getStringArray(om.rrtx.mobile.rrtxcommon1.R.array.id_type)
            }
            val entries = listOf<String>(stringArray[0],stringArray[1])

            val mOldCurrencyDialog =
                CommonMoreDataDialog(
                    this@NewUserInfoActivity, "1", entries
                )
            mOldCurrencyDialog.setOnCurrencyClickListener(CommonMoreDataDialog.OnCurrencyClickListener { position, value ->
                dataBinding.idTypeTIL.editText?.setText(value)
                if (position == 0) {
                    if (BaseConstants.JumpFlag.Register_junior_Account==mJumpFlag){
                        registerInfoBean.idType = "30"
                    }else{
                        registerInfoBean.idType = "10"
                        dataBinding.idNumberEd.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(
                            12))
                    }

                } else {
                    registerInfoBean.idType = "20"
                    dataBinding.idNumberEd.filters = arrayOf<InputFilter>(InputFilter.LengthFilter(
                        50))
                }
                dataBinding.idNumberEd.setText("")
            })
            mOldCurrencyDialog.show()
        }

        dataBinding.areaTIL.editText?.setOnClickListener {
            if (!this::appDictCodeList.isInitialized) return@setOnClickListener

            val entries = arrayListOf<String>()
            for (bean in appDictCodeList) {
                entries.add(bean.dictLabel)
            }

            val mOldCurrencyDialog =
                CommonMoreDataDialog(
                    this@NewUserInfoActivity, "1", entries
                )
            mOldCurrencyDialog.setOnCurrencyClickListener(CommonMoreDataDialog.OnCurrencyClickListener { position, value ->
                dataBinding.areaTIL.editText?.setText(value)
                registerInfoBean.cityCode = appDictCodeList[position].dictValue
                upInfo()
            })
            mOldCurrencyDialog.show()
        }
    }

    private fun initVResultListener() {
        viewModel.registerResult.observe(this) {
            if (it == null) return@observe
            if (it.isWalletUser == "1") {
                when (it.userStatus) {
                    // 正常 冻结
                    "0", "1" -> {
                        val aloneDialog = AloneDialog(mContext)
                        aloneDialog.show()
                        aloneDialog.setTitleStr(ResourceHelper.getString(R.string.notice))
                        if (BaseConstants.JumpFlag.Register_junior_Account==mJumpFlag){
                            aloneDialog.setContentStr(ResourceHelper.getString(R.string.notice_registered, it.userName))
                        }else{
                            aloneDialog.setContentStr(ResourceHelper.getString(R.string.notice_registered2, it.userName))
                        }
                    }
                    // 待认证
                    "4" -> {
                        if (it.accountType=="1")
                        {
                            //待认证亲子账户
                            val aloneDialog = AloneDialog(mContext)
                            aloneDialog.show()
                            aloneDialog.setTitleStr(ResourceHelper.getString(R.string.notice))
                            if (BaseConstants.JumpFlag.Register_junior_Account==mJumpFlag){
                                aloneDialog.setContentStr(ResourceHelper.getString(R.string.notice_go_authentication))
                            }else{
                                aloneDialog.setContentStr(ResourceHelper.getString(R.string.notice_registered1))
                            }
                        }else{
                            val aloneDialog = AloneDialog(mContext)
                            aloneDialog.show()
                            aloneDialog.setTitleStr(ResourceHelper.getString(R.string.notice))
                            if (BaseConstants.JumpFlag.Register_junior_Account==mJumpFlag){
                                aloneDialog.setContentStr(ResourceHelper.getString(R.string.notice_registered))
                            }else{
                                aloneDialog.setContentStr(ResourceHelper.getString(R.string.notice_registered1))
                            }
                        }

                    }
                }
                if (it.isCardIdUsed == "1"){
                    //证件号重复
                    val aloneDialog = AloneDialog(mContext)
                    aloneDialog.show()
                    aloneDialog.setTitleStr(ResourceHelper.getString(R.string.notice))
                    aloneDialog.setContentStr(ResourceHelper.getString(R.string.notice_idcard))

                }
            } else {
                // 下一步发送验证码
                if (it.checkToken!=null) registerInfoBean.checkToken = it.checkToken
                SharedPreferencesUtils.setParam(mContext,
                    BaseConstants.SaveParameter.REGISTER_INFO,
                    Gson().toJson(registerInfoBean));
                SharedPreferencesUtils.setParam(mContext,
                    BaseConstants.SaveParameter.CONDITION_INFO,
                    Gson().toJson(conditionBean));
                RegisterVerCodeActivity.jump(mContext, registerInfoBean.mobile, mJumpFlag)
            }
        }
        viewModel.dictCodeListResult.observe(this) {
            appDictCodeList = it.appDictCodeList
        }

        viewModel.conditionBeanResult.observe(this) {
            conditionBean = it
        }
    }

    private fun initTextChangedListener() {
        object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                val toString = p0.toString()
                if (toString.length == 9) {
                    mobileFormatCheck(toString)
                    upInfo()
                }else{
                    mobileFormatCheck(toString)
                }
            }
        }.apply {
            dataBinding.mobileTIL.editText?.addTextChangedListener(this)
        }
    }

    private fun onNext() {
        if (!FormatCheck.mobileFormatCheck(registerInfoBean.mobile)) {
            ToastUtil.show(mContext, getString(R.string.mobile_error))
            return
        }
        when (mJumpFlag) {

            BaseConstants.JumpFlag.Register_junior_Account -> viewModel.requestValidate(
                registerInfoBean,
                1)

            else -> viewModel.requestValidate(registerInfoBean, 0)
        }
    }

    /**
     * 手机号码校验
     */
    fun mobileFormatCheck(text: String) {
        if (FormatCheck.mobileFormatCheck(text)) {
            dataBinding.mobileError.visibility = View.GONE
            registerInfoBean.mobile = text
        } else {
            dataBinding.mobileError.visibility = View.VISIBLE
            registerInfoBean.mobile = ""
        }
    }

    override fun initData() {
        initCondition()
        viewModel.requestDictCodeList()
        var type = "01"
        when (mJumpFlag) {
            BaseConstants.JumpFlag.Register_junior_Account -> type = "06"

        }
        viewModel.getLatestCondition(type)

        SoftKeyBoardListener.setListener(
            mContext,
            object : SoftKeyBoardListener.OnSoftKeyBoardChangeListener {
                override fun keyBoardShow(height: Int) {
                    //键盘显示
                }

                override fun keyBoardHide(height: Int) {
                    upInfo()
                }
            })
    }

    private fun initCondition() {
        val str1 = getString(R.string.read_and_accept)
        val spanString =
            SpannableString(str1 + " " + getString(R.string.terms_and_conditions))
        val s1Color = ForegroundColorSpan(ResourceHelper.getColor(R.color.common_text_86909C))
        spanString.setSpan(s1Color, 0, str1.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        val s2Color = ForegroundColorSpan(ResourceHelper.getColor(R.color.common_ye_F3881E))
        spanString.setSpan(s2Color,
            str1.length,
            spanString.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        dataBinding.tvCondition.text = spanString
    }

    fun upInfo() {
        // 手动触发失焦采集输入信息
        if (this::curEditText.isInitialized) curEditText.clearFocus()
        if (registerInfoBean.mobile.isEmpty()){
            registerInfoBean.mobile = dataBinding.mobileTIL.editText?.text.toString()
        }
        if (registerInfoBean.firstName.isEmpty()){
            registerInfoBean.firstName = dataBinding.firstNameTIL.editText?.text.toString()
        }
        if (registerInfoBean.lastName.isEmpty()){
            registerInfoBean.lastName = dataBinding.lastNameTIL.editText?.text.toString()
        }
        if (registerInfoBean.idNumber.isEmpty()){
            registerInfoBean.idNumber = dataBinding.idNumberTIL.editText?.text.toString()
        }
        if (registerInfoBean.address.isEmpty()){
            registerInfoBean.address = dataBinding.addressTIL.editText?.text.toString()
        }
        val isEnabled = isAgreement && registerInfoBean.verify()
        dataBinding.tvNext.isEnabled = isEnabled
        if (isEnabled) {
            dataBinding.tvNext.background = ResourceHelper.getDrawable(R.drawable.common_usable_btn)
        } else {
            dataBinding.tvNext.background = ResourceHelper.getDrawable(R.drawable.common_unusable_btn)
        }

    }

    override fun onDestroy(){
        super.onDestroy()
        viewModel.registerResult.value = null
        viewModel.registerResult.removeObservers(this)
        viewModel.conditionBeanResult.removeObservers(this)
        viewModel.dictCodeListResult.removeObservers(this)
    }
}