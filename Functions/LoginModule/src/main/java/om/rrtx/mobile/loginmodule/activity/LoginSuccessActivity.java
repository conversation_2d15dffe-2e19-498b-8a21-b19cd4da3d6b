package om.rrtx.mobile.loginmodule.activity;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.launcher.ARouter;
import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.loginmodule.LoginConstants;
import om.rrtx.mobile.loginmodule.R;
import om.rrtx.mobile.loginmodule.R2;
import om.rrtx.mobile.loginmodule.bean.UpDataBean;
import om.rrtx.mobile.rrtxcommon1.bean.LoginBean;
import om.rrtx.mobile.loginmodule.presenter.LoginPresenter;
import om.rrtx.mobile.loginmodule.view.LoginView;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 登录模块的成功页
 */
public class LoginSuccessActivity extends BaseSuperActivity<LoginView, LoginPresenter>
        implements LoginView {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.doneTv)
    TextView mDoneTv;
    @BindView(R2.id.hint)
    TextView mHint;
    private String mUserName;
    private String mPsd;
    private String mJumpFlag;
    private String mMobile;
    private String mMobileAreaCode;

    public static void jumpLoginSuccess(Context context, String user, String psd, String jumpFlag) {
        Intent intent = new Intent(context, LoginSuccessActivity.class);
        intent.putExtra(LoginConstants.Transmit.USERNAME, user);
        intent.putExtra(LoginConstants.Transmit.PASSWORD, psd);
        intent.putExtra(BaseConstants.Transmit.JUMPFLAG, jumpFlag);
        context.startActivity(intent);
    }

    public static void jumpForgetNameSuccess(Context context, String mobileAreaCode, String mobile, String jumpFlag) {
        Intent intent = new Intent(context, LoginSuccessActivity.class);
        intent.putExtra(LoginConstants.Transmit.MOBILE, mobile);
        intent.putExtra(BaseConstants.Transmit.JUMPFLAG, jumpFlag);
        intent.putExtra(BaseConstants.Transmit.MOBILEAREACODE, mobileAreaCode);
        context.startActivity(intent);
    }

    public static void jumpForgetPsdSuccess(Context context, String jumpFlag) {
        Intent intent = new Intent(context, LoginSuccessActivity.class);
        intent.putExtra(BaseConstants.Transmit.JUMPFLAG, jumpFlag);
        context.startActivity(intent);
    }

    public static void jumpNoActiveSuccess(Context context, String jumpFlag, String userName, String psd) {
        Intent intent = new Intent(context, LoginSuccessActivity.class);
        intent.putExtra(BaseConstants.Transmit.JUMPFLAG, jumpFlag);
        intent.putExtra(LoginConstants.Transmit.USERNAME, userName);
        intent.putExtra(LoginConstants.Transmit.PASSWORD, psd);
        context.startActivity(intent);
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        mUserName = getIntent().getStringExtra(LoginConstants.Transmit.USERNAME);
        mPsd = getIntent().getStringExtra(LoginConstants.Transmit.PASSWORD);
        mJumpFlag = getIntent().getStringExtra(BaseConstants.Transmit.JUMPFLAG);
        mMobile = getIntent().getStringExtra(BaseConstants.Transmit.MOBILE);
        mMobileAreaCode = getIntent().getStringExtra(BaseConstants.Transmit.MOBILEAREACODE);
    }

    @Override
    protected int createContentView() {
        return R.layout.login_activity_login_success;
    }

    @Override
    protected LoginPresenter createPresenter() {
        return new LoginPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setVisibility(View.GONE);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));

        if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.REGISTRATION)) {
            mTitleTv.setText(getString(R.string.register_title_user_registration));
            mHint.setText(getString(R.string.register_label_registration_tip));
            mDoneTv.setText(R.string.done);
        } else if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.FORGETUSERNAME)) {
            mTitleTv.setText(getString(R.string.retrieve_title_forgot_username));
            String hint = getString(R.string.retrieve_label_send_username_tip) + "(" + StringUtils.stringMask(mMobileAreaCode, mMobile) + ")";
            mHint.setText(hint);
            mDoneTv.setText(R.string.done);
        } else if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.FORGETPASSWORD)) {
            mTitleTv.setText(getString(R.string.retrieve_title_set_login_pwd_success));
            mHint.setText(getString(R.string.retrieve_label_set_login_pwd_success));
        } else if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.USERNOACTIVE)) {
            mTitleTv.setText(R.string.retrieve_title_set_login_pwd);
            mHint.setText(R.string.retrieve_label_set_login_pwd_success);
            mDoneTv.setText(R.string.done);
        }
    }

    @Override
    public void initListener() {
        super.initListener();
        mDoneTv.setOnClickListener(successCustom);
    }

    private CustomClickListener successCustom = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.doneTv) {
                if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.REGISTRATION) || TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.USERNOACTIVE)) {
                    //登录
                    String registrationId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.REGISTRATIONID, "");
                    String token = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.DEVICETOKEN, "");
                    mPresenter.requestLogin(mUserName, mPsd, registrationId,token);
                } else if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.FORGETUSERNAME) ||
                        TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.FORGETPASSWORD)) {
                    LoginActivity.jumpTopLogin(mContext);
                }
            }
        }
    };

    @Override
    public void loginSuccess(LoginBean loginBean) {
        //保存设置支付密码状态
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PAYMENTPASSWORDSETUPFLAG, loginBean.getPinSetupFlag());
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.REALNAME, loginBean.getRealName());
        //保存用户名称
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.MASKNAME, loginBean.getMaskName());
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERNAME, loginBean.getNickName());
        //保存用户电话
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERMOBILE, loginBean.getMobile());
        //保存用户id
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERCARDID, loginBean.getIdCard());
        //保存用户头像
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERHEARD, loginBean.getUserAvatar());
        //保存用户ID
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERID, loginBean.getUserId());
        //nric
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.IDCARD, loginBean.getIdCard());
        //记录区号
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.MOBILEAREACODE, loginBean.getMobileAreaCode());
        //记录币种
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.SAVECURRENCY, TextUtils.isEmpty(loginBean.getCurrency()) ? "" : loginBean.getCurrency());
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.LASTLOGINTIME, TextUtils.isEmpty(loginBean.getLastLoginTime()) ? "" :loginBean.getLastLoginTime());
        //这里应该把之前的用户指纹状态关闭
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.ISFINGER, false);

        //账户类型 0-常规账户 1-亲子账户
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.ACCOUNTTYPE, loginBean.getAccountType());

        ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity).withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();

        BaseApp.setIsPromotion(false);
    }

    @Override
    public void requestFail(BaseBean<LoginBean> sResMsg) {
        if (TextUtils.equals(sResMsg.getStatus(), "RRB-********")) {
            //表示临时设备登录 走授权码验证流程
        } else {
            ToastUtil.show(mContext, sResMsg.getMessage());
        }
    }

    @Override
    public void upDataSuccess(UpDataBean sResData) {

    }

    @Override
    public void onBackPressed() {

        if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.REGISTRATION)) {
            //登录
            String registrationId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.REGISTRATIONID, "");
            String token = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.DEVICETOKEN, "");
            mPresenter.requestLogin(mUserName, mPsd, registrationId,token);
        } else if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.FORGETUSERNAME) || TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.FORGETPASSWORD)) {
            LoginActivity.jumpTopLogin(mContext);
        }
    }
}
