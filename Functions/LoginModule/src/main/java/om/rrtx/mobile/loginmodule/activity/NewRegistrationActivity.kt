package om.rrtx.mobile.loginmodule.activity

import android.content.Context
import android.content.Intent
import android.content.res.Resources
import android.view.View
import com.alibaba.android.arouter.launcher.ARouter
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.activity_registration.tv_all_step
import kotlinx.android.synthetic.main.activity_registration.tv_cur_page
import kotlinx.android.synthetic.main.activity_registration.tv_cur_step
import kotlinx.android.synthetic.main.login_base_title.backIv
import kotlinx.android.synthetic.main.login_base_title.titleTv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.loginmodule.R
import om.rrtx.mobile.loginmodule.databinding.ActivityRegistrationNewBinding
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity
import om.rrtx.mobile.rrtxcommon1.utils.AdaptScreenUtils
import om.rrtx.mobile.rrtxcommon1.utils.LocaleManager
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper

/**
 * 常规账户注册1
 * */
class NewRegistrationActivity : BaseActivity<ActivityRegistrationNewBinding>() {

    var mJumpFlag = ""
    var selectLan = 0

    data class lanBean(val view: View, val lan: Int)
    companion object {
        @JvmStatic
        fun jump(context: Context,flag:String) {
            val intent=Intent(context, NewRegistrationActivity::class.java)
            intent.putExtra(BaseConstants.Transmit.JUMPFLAG,flag)
            context.startActivity(intent)
        }
    }

    override fun createContentView() = R.layout.activity_registration_new

    override fun initWorkspaceAction() {
        mJumpFlag = intent.getStringExtra(BaseConstants.Transmit.JUMPFLAG).toString()
    }

    override fun getResources(): Resources? {
        return AdaptScreenUtils.adaptWidth(super.getResources(), BaseConstants.DESIGNWIDTHINPX)
    }

    override fun initView() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true)
            .init()

        backIv.background = ResourceHelper.getDrawable(R.drawable.login_ic_back)
        titleTv.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
        titleTv.setTextColor(ResourceHelper.getColor(R.color.color_131313))
        if (BaseConstants.JumpFlag.Register_junior_Account==mJumpFlag)
        {
            titleTv.text = getString(R.string.register_junior)
        }else
        {
            titleTv.text = getString(R.string.register_title_user_registration)
        }

        backIv.setOnClickListener {
           //onBackPressed()
            // 返回登录页
            ARouter.getInstance()
                .build(ARouterPath.LoginPath.LoginActivity)
                .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                .navigation()
        }
    }

    override fun initData() {
        tv_cur_step.text = getString(R.string.preferred_language)
        tv_cur_page.text = "1"
        tv_all_step.text = "/6"

        val selectIconMap = mapOf(
            R.id.bg_lan0 to lanBean(dataBinding.ivEnglish, 0),
            R.id.bg_lan1 to lanBean(dataBinding.ivShona, 1),
            R.id.bg_lan2 to lanBean(dataBinding.ivNdebele, 2)
        )

        View.OnClickListener {
            for (id in selectIconMap.keys) {
                val lanBean = selectIconMap.get(id)
                if (lanBean != null) {
                    if (it.id == id) {
                        lanBean.view.visibility = View.VISIBLE
                        selectLan = lanBean.lan
                    } else {
                        lanBean.view.visibility = View.GONE
                    }
                }
            }
        }.apply {
            dataBinding.bgLan0.setOnClickListener(this)
            dataBinding.bgLan1.setOnClickListener(this)
            dataBinding.bgLan2.setOnClickListener(this)
        }

        dataBinding.tvNext.setOnClickListener {
            when (selectLan) {
                0 -> {
                    //切换成英文
                    LocaleManager.getInstance()
                        .setNewLocale(this, LocaleManager.LANGUAGE_ENGLISH)
                }

                1 -> {
                    LocaleManager.getInstance()
                        .setNewLocale(this, LocaleManager.LANGUAGE_SHONA)
                }

                2 -> {
                    LocaleManager.getInstance()
                        .setNewLocale(this, LocaleManager.LANGUAGE_NDEBELE)
                }
            }
            ReguarPhoneInfoActivity.jump(mContext,mJumpFlag)
            finish()
        }
    }

    override fun onBackPressed() {
        //super.onBackPressed()
        // 返回登录页
        ARouter.getInstance()
            .build(ARouterPath.LoginPath.LoginActivity)
            .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_SINGLE_TOP or Intent.FLAG_ACTIVITY_CLEAR_TOP)
            .navigation()
    }
    override fun initListener() {
    }

}