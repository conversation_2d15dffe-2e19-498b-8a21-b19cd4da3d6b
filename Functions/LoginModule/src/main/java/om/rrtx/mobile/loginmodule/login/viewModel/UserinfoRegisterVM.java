package om.rrtx.mobile.loginmodule.login.viewModel;

import android.app.Activity;
import android.app.Application;
import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.MutableLiveData;

import java.io.File;

import om.rrtx.mobile.loginmodule.bean.UploadBean;
import om.rrtx.mobile.rrtxcommon1.bean.CheckAccountResultBean;
import om.rrtx.mobile.rrtxcommon1.bean.ConditionBean;
import om.rrtx.mobile.loginmodule.bean.PubBean;
import om.rrtx.mobile.loginmodule.bean.RegisterInfoBean;
import om.rrtx.mobile.loginmodule.bean.RegisterResultBean;
import om.rrtx.mobile.loginmodule.model.LoginModel;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.bean.AppDictListBean;
import om.rrtx.mobile.rrtxcommon1.bean.UpgradeInfoBean;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserverNoError;
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

public class UserinfoRegisterVM extends AndroidViewModel {

    private final LoginModel mLoginModel;
    public Context mContext = ActivityController.getInstance().currentActivity();

    public MutableLiveData<UploadBean> uploadResult = new MutableLiveData<>();
    public MutableLiveData<RegisterResultBean> registerResult = new MutableLiveData<>();
    public MutableLiveData<AppDictListBean> dictCodeListResult = new MutableLiveData<>();
    public MutableLiveData<CheckAccountResultBean> checkUpGradleResult = new MutableLiveData<>();
    public MutableLiveData<ConditionBean> conditionBeanResult = new MutableLiveData<>();

    public UserinfoRegisterVM(@NonNull Application application) {
        super(application);
        mLoginModel = new LoginModel(mContext);
    }

    public void uploadPhoto(String type,File userPhoto) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mLoginModel.uploadPhoto(userPhoto, new BaseObserver<UploadBean>(mContext) {
                @Override
                public void requestSuccess(UploadBean uploadBean) {
                    uploadBean.setPhotoType(type);
                    uploadResult.setValue(uploadBean);
                }

                @Override
                public void requestFail(String sResMsg) {
                }
            });
        }else {
            mLoginModel.commonPub(new BaseNoDialogObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mLoginModel.uploadPhoto(userPhoto, new BaseObserver<UploadBean>(mContext) {
                        @Override
                        public void requestSuccess(UploadBean uploadBean) {
                            uploadResult.setValue(uploadBean);
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    public void requestValidate(RegisterInfoBean registerInfoBean, int type) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mLoginModel.requestValidate(registerInfoBean, type, new BaseObserver<RegisterResultBean>(mContext) {
                @Override
                public void requestSuccess(RegisterResultBean bean) {
                    registerResult.setValue(bean);
                }

                @Override
                public void requestFail(String sResMsg) {
//                    registerResult.setValue(sResMsg);
                    ToastUtil.show(mContext, sResMsg);
                }
            });
        } else {
            mLoginModel.commonPub(new BaseNoDialogObserver<PubBean>(mContext) {
                @Override
                public void requestFail(String sResMsg) {

                }

                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mLoginModel.requestValidate(registerInfoBean, type, new BaseObserver<RegisterResultBean>(mContext) {
                        public void requestSuccess(RegisterResultBean bean) {
                            registerResult.setValue(bean);
                        }

                        @Override
                        public void requestFail(String sResMsg) {
//                    registerResult.setValue(sResMsg);
                            ToastUtil.show(mContext, sResMsg);
                        }
                    });
                }
            });
        }
    }

    public void requestPhoneValidate(String areaCode, String mobile) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mLoginModel.requestPhoneValidate(areaCode, mobile, new BaseObserver<RegisterResultBean>(mContext) {
                @Override
                public void requestSuccess(RegisterResultBean bean) {
                    registerResult.setValue(bean);
                }

                @Override
                public void requestFail(String sResMsg) {
                    ToastUtil.show(mContext, sResMsg);
                }
            });
        } else {
            mLoginModel.commonPub(new BaseNoDialogObserver<PubBean>(mContext) {
                @Override
                public void requestFail(String sResMsg) {

                }

                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mLoginModel.requestPhoneValidate(areaCode, mobile, new BaseObserver<RegisterResultBean>(mContext) {
                        public void requestSuccess(RegisterResultBean bean) {
                            registerResult.setValue(bean);
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            ToastUtil.show(mContext, sResMsg);
                        }
                    });
                }
            });
        }
    }

    public void requestDictCodeList() {
        mLoginModel.requestDictCodeList(new BaseNoDialogObserver<AppDictListBean>(mContext) {
            @Override
            public void requestSuccess(AppDictListBean sResData) {
                dictCodeListResult.setValue(sResData);
            }

            @Override
            public void requestFail(String sResMsg) {
                ToastUtil.show(mContext, sResMsg);
            }
        });
    }

    public void requestCheckUpGradeInfo(UpgradeInfoBean bean) {
        Activity activity = ActivityController.getInstance().currentActivity();
        mLoginModel.requestCheckUpGradeInfo(bean, new BaseObserverNoError<CheckAccountResultBean>(activity) {
            @Override
            public void requestSuccess(CheckAccountResultBean bean) {
                checkUpGradleResult.setValue(bean);
            }
        });
    }

    /**
     * 获取最新协议
     */
    public void getLatestCondition(String type) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
//        Log.e("RegistrationPresenter>>>", "zfw getLatestCondition>>>pubLick :"+pubLick );
        if (!TextUtils.isEmpty(pubLick)) {
            mLoginModel.getLatestCondition(type,new BaseObserver<ConditionBean>(mContext) {
                @Override
                public void requestSuccess(ConditionBean bean) {
                    conditionBeanResult.setValue(bean);
                }

                @Override
                public void requestFail(String sResMsg) {
                    ToastUtil.show(mContext, sResMsg);
                }
            });
        } else {
            mLoginModel.commonPub(new BaseNoDialogObserver<PubBean>(mContext) {
                @Override
                public void requestFail(String sResMsg) {
                    ToastUtil.show(mContext, sResMsg);
                }

                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mLoginModel.getLatestCondition(type,new BaseObserver<ConditionBean>(mContext) {
                        @Override
                        public void requestSuccess(ConditionBean bean) {
                            conditionBeanResult.setValue(bean);
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            ToastUtil.show(mContext, sResMsg);
                        }
                    });
                }
            });
        }
    }

}
