package om.rrtx.mobile.loginmodule.presenter;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import om.rrtx.mobile.loginmodule.bean.PubBean;
import om.rrtx.mobile.loginmodule.bean.UpDataBean;
import om.rrtx.mobile.loginmodule.model.LoginModel;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 */
public class SplashPresenter extends BasePresenter<SplashView> {

    private LoginModel mLoginModel;
    private Context mContext;

    public SplashPresenter(Context context) {
        mLoginModel = new LoginModel(context);
        mContext = context;
    }

    public void requestUpdate(String versionName) {
       String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
       if (TextUtils.isEmpty(pubLick)){
           mLoginModel.commonPub(new BaseNoDialogObserver<PubBean>(mContext) {
               @Override
               public void requestSuccess(PubBean sResData) {
                   SharedPreferencesUtils.setParam(mContext,BaseConstants.SaveParameter.PUBLICKEY,sResData.getPubKeyBase64());
                   mLoginModel.requestUpData(userId,versionName, new BaseNoDialogObserver<UpDataBean>(mContext) {
                       @Override
                       public void requestSuccess(UpDataBean sResData) {
                           Log.e("SplashPresenter>>>", "zfw requestSuccess>>>sResData :"+sResData );
                           if (getView() != null) {
                               getView().upDataSuccess(sResData);
                           }
                       }

                       @Override
                       public void requestFail(String sResMsg) {
                           Log.e("SplashPresenter>>>", "zfw requestFail>>> 000:"+sResMsg );
                           if (getView() != null) {
                               getView().requestFail(sResMsg);
                           }
                       }
                   });
               }

               @Override
               public void requestFail(String sResMsg) {
                   Log.e("SplashPresenter>>>", "zfw requestFail>>> 111:"+sResMsg );
                   if (getView() != null) {
                       getView().requestFail(sResMsg);
                   }
               }
           });
       }else {
           mLoginModel.requestUpData(userId,versionName, new BaseNoDialogObserver<UpDataBean>(mContext) {
               @Override
               public void requestSuccess(UpDataBean sResData) {
                   Log.e("SplashPresenter>>>", "zfw requestSuccess>>>sResData.toString() :"+sResData.toString() );
                   Log.e("SplashPresenter>>>", "zfw requestSuccess>>>getView() == null :"+(getView() == null) );
                   if (getView() != null) {
                       getView().upDataSuccess(sResData);
                   }
               }

               @Override
               public void requestFail(String sResMsg) {
                   Log.e("SplashPresenter>>>", "zfw requestFail>>> 222:"+sResMsg );
                   if (getView() != null) {
                       if ("RRB-05009001".equals(sResMsg)) {
                           requestUpdate(versionName);
                       } else {
                           getView().requestFail(sResMsg);
                       }
                   }
               }
           });
       }

    }
}
