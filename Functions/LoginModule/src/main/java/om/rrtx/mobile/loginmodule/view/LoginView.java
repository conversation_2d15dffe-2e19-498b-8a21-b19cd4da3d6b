package om.rrtx.mobile.loginmodule.view;

import om.rrtx.mobile.loginmodule.bean.UpDataBean;
import om.rrtx.mobile.rrtxcommon1.bean.LoginBean;
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean;

public interface LoginView {
    /**
     * 登录成功
     *
     * @param loginBean 成功结果
     */
    void loginSuccess(LoginBean loginBean);

    /**
     * 请求失败
     *
     * @param sResMsg 失败信息
     */
    void requestFail(BaseBean<LoginBean> sResMsg);

    void upDataSuccess(UpDataBean sResData);

}
