package om.rrtx.mobile.loginmodule.presenter;

import android.content.Context;
import android.text.TextUtils;

import om.rrtx.mobile.rrtxcommon1.bean.ConditionBean;
import om.rrtx.mobile.loginmodule.bean.PubBean;
import om.rrtx.mobile.loginmodule.bean.RegistrationBean;
import om.rrtx.mobile.loginmodule.model.LoginModel;
import om.rrtx.mobile.loginmodule.view.SetLoginPsdView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * 设置密码P层
 */
public class SetLoginPsdPresenter extends BasePresenter<SetLoginPsdView> {

    private LoginModel mLoginModel;
    private Context mContext;

    public SetLoginPsdPresenter(Context context) {
        mLoginModel = new LoginModel();
        mContext = context;
    }


    public void requestRegistration(RegistrationBean registrationBean, ConditionBean conditionBean) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mLoginModel.requestRegistration(userId,registrationBean,conditionBean, new BaseObserver<Object>(mContext) {
                @Override
                public void requestSuccess(Object object) {
                    if (getView() != null) {
                        getView().registerSuccess();
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        } else {
            mLoginModel.commonPub(new BaseNoDialogObserver<PubBean>(mContext) {
                @Override
                public void requestFail(String sResMsg) {

                }

                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mLoginModel.requestRegistration(userId,registrationBean,conditionBean, new BaseObserver<Object>(mContext) {
                        @Override
                        public void requestSuccess(Object object) {
                            if (getView() != null) {
                                getView().registerSuccess();
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }
            });
        }
    }

    /**
     * 设置密码页面
     *
     * @param psdOneStr 密码
     * @param userName  用户名
     * @param idCard    卡号
     */
    public void setUpLoginPsd(String psdOneStr, String userName, String idCard) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mLoginModel.setUpLoginPsd(userId,psdOneStr, userName, idCard, new BaseObserver<Object>(mContext) {
                @Override
                public void requestSuccess(Object object) {
                    if (getView() != null) {
                        getView().upLoginPsdSuccess();
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        } else {
            mLoginModel.commonPub(new BaseNoDialogObserver<PubBean>(mContext) {
                @Override
                public void requestFail(String sResMsg) {

                }

                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mLoginModel.setInviteFriendsPsd(userId,psdOneStr, new BaseObserver<Object>(mContext) {
                        @Override
                        public void requestSuccess(Object object) {
                            if (getView() != null) {
                                getView().inviteFriendPsdSuccess();
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }
            });
        }
    }

    public void setInviteFriendsPsd(String psdOneStr) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mLoginModel.setInviteFriendsPsd(userId,psdOneStr, new BaseObserver<Object>(mContext) {
                @Override
                public void requestSuccess(Object object) {
                    if (getView() != null) {
                        getView().inviteFriendPsdSuccess();
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        } else {
            mLoginModel.commonPub(new BaseNoDialogObserver<PubBean>(mContext) {
                @Override
                public void requestFail(String sResMsg) {

                }

                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mLoginModel.setInviteFriendsPsd(userId,psdOneStr, new BaseObserver<Object>(mContext) {
                        @Override
                        public void requestSuccess(Object object) {
                            if (getView() != null) {
                                getView().inviteFriendPsdSuccess();
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }
            });
        }
    }
}
