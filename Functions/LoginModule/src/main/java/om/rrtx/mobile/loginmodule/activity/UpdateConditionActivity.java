package om.rrtx.mobile.loginmodule.activity;


import android.content.Context;
import android.content.Intent;
import android.text.Html;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.CheckBox;
import android.widget.CompoundButton;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import org.json.JSONException;
import org.json.JSONObject;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functionapi.bean.OutJumpBean;
import om.rrtx.mobile.functioncommon.activity.ConditionActivity;
import om.rrtx.mobile.loginmodule.R;
import om.rrtx.mobile.loginmodule.R2;
import om.rrtx.mobile.rrtxcommon1.bean.LoginBean;
import om.rrtx.mobile.loginmodule.presenter.ConditionPresenter;
import om.rrtx.mobile.loginmodule.view.ConditionView;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR> zfw
 * @time   : 2023/3/21 9:01
 * @desc   : 更新协议
 */
public class UpdateConditionActivity extends BaseSuperActivity<ConditionView, ConditionPresenter>
        implements ConditionView{
    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.cb)
    CheckBox mCb;
    @BindView(R2.id.agreeTv)
    TextView mAgreeTv;
    @BindView(R2.id.termTv)
    TextView mTermTv;
    @BindView(R2.id.disAgreeTv)
    TextView mDisAgreeTv;
    static String LOGINBEAN = "loginBean";
    static String JUMPFLG = "jumpFlg";
    static String OUTJUMPDATEJSON = "outJumpDateJson";
    static String OUTJPUSHJSON = "outJpushJson";
    LoginBean mLoginBean;
    private String mJumpFlag,mJpushJson;
    private String mOutJumpDateJson;

    public static void jumpUpdateCondition(Context context,String loginBean,String jumpFlg,String outJumpDateJson) {
        Intent intent = new Intent(context, UpdateConditionActivity.class);
        intent.putExtra(LOGINBEAN, loginBean);
        intent.putExtra(JUMPFLG, jumpFlg);
        intent.putExtra(OUTJUMPDATEJSON, outJumpDateJson);
        context.startActivity(intent);
    }

    @Override
    protected int createContentView() {
        return R.layout.activity_update_condition;
    }


    @Override
    protected ConditionPresenter createPresenter() {
        return new ConditionPresenter(this);
    }


    @Override
    public void doGetExtra() {
        mJumpFlag = getIntent().getStringExtra(JUMPFLG);
        mJpushJson = getIntent().getStringExtra(OUTJPUSHJSON);
        mOutJumpDateJson = getIntent().getStringExtra(OUTJUMPDATEJSON);
        String loginBeanStr = getIntent().getStringExtra(LOGINBEAN);
        mLoginBean = new Gson().fromJson(loginBeanStr, LoginBean.class);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarColor(R.color.common_ye_F3881E)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.login_ic_back_w);
        mTitleTv.setText(getString(R.string.register_label_terms_condition));
        mTitleTv.setBackgroundColor(getResources().getColor(R.color.common_ye_F3881E));
        mTitleTv.setTextColor(getResources().getColor(R.color.color_FFFFFF));
        mLeftBg.setOnClickListener(v -> finish());
        if (TextUtils.isEmpty(mLoginBean.getConditionTitle())) {
            mTermTv.setText(getString(R.string.underline_terms_condition));
        } else {
            mTermTv.setText(Html.fromHtml("<u>"+mLoginBean.getConditionTitle()+"</u>"));
        }
    }

    @Override
    public void initListener() {
        mCb.setOnCheckedChangeListener(new CompoundButton.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
                if (isChecked) {
                    mAgreeTv.setBackgroundResource(R.drawable.login_drawable_btn_select);
                    mAgreeTv.setEnabled(true);
                } else {
                    mAgreeTv.setBackgroundResource(R.drawable.login_drawable_btn_unselect);
                    mAgreeTv.setEnabled(false);
                }
            }
        });
        mDisAgreeTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERNAME, "");
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.AUTHORIZATION, "");
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.ISFINGER, false);
                ActivityController.getInstance().finishAllActivity();
            }
        });
        mAgreeTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                mPresenter.agreeCondition(mLoginBean.getMobile(),mLoginBean.getConditionTitle(),mLoginBean.getConditionVersionNo());
            }
        });
        mTermTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                ConditionActivity.jumpCondition(UpdateConditionActivity.this,mLoginBean.getConditionTitle(),mLoginBean.getConditionContent());
            }
        });
    }

    /**
     * 同意协议成功
     */
    @Override
    public void agreeSuccess() {
        String userStatus = mLoginBean.getUserStatus();
        //登录成功
        //保存设置支付密码状态
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PAYMENTPASSWORDSETUPFLAG, mLoginBean.getPinSetupFlag());
        //保存用户名称
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.MASKNAME, mLoginBean.getMaskName());
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERNAME, mLoginBean.getNickName());
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.REALNAME, mLoginBean.getRealName());
        //保存用户电话
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERMOBILE, mLoginBean.getMobile());
        //保存用户id
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERCARDID, mLoginBean.getIdCard());
        //保存用户头像
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERHEARD, mLoginBean.getUserAvatar());
        //保存用户ID
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERID, mLoginBean.getUserId());
        //nric
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.IDCARD, mLoginBean.getIdCard());
        //记录区号
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.MOBILEAREACODE, mLoginBean.getMobileAreaCode());
        //记录币种
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.SAVECURRENCY, TextUtils.isEmpty(mLoginBean.getCurrency()) ? "" : mLoginBean.getCurrency());

        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.LASTLOGINTIME, TextUtils.isEmpty(mLoginBean.getLastLoginTime()) ? "" :mLoginBean.getLastLoginTime());
        //账户类型 0-常规账户 1-亲子账户
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.ACCOUNTTYPE, mLoginBean.getAccountType());

        if (TextUtils.equals(BaseConstants.UserStatus.NORMAL, userStatus)) {
            if (TextUtils.isEmpty(mJumpFlag)) {
                //正常跳转
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .navigation();
            } else {
                //外部跳转
                if (!TextUtils.isEmpty(mOutJumpDateJson)) {
                    externalJump(new Gson().fromJson(mOutJumpDateJson, OutJumpBean.class));
                } else if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.JPUSHJUMP)) {
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                            .navigation();

                    if (getIntent() != null) {
                        handlerJpush(mJpushJson);
                    }
                } else {
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity).navigation();
                    ARouter.getInstance().build(ARouterPath.Cashier.CashierActivity)
                            .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                            .navigation();
                    finish();
                }
            }
        } else if (TextUtils.equals(BaseConstants.UserStatus.NOTACTIVE, userStatus)) {
            ARouter.getInstance()
                    .build(ARouterPath.LoginPath.SetLoginPsdActivity)
                    .withString(BaseConstants.Transmit.USERNAME, mLoginBean.getNickName())
                    .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.USERNOACTIVE)
                    .navigation();
        } else {
            if (TextUtils.isEmpty(mJumpFlag)) {
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .navigation();
            } else {
                if (!TextUtils.isEmpty(mOutJumpDateJson)) {
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity).navigation();
                    ARouter.getInstance().build(ARouterPath.Cashier.ScanCashierActivity)
                            .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                            .withString(BaseConstants.Transmit.XWALLETPAYBEANJSON, mOutJumpDateJson)
                            .navigation();
                    finish();
                } else {
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity).navigation();
                    ARouter.getInstance().build(ARouterPath.Cashier.CashierActivity)
                            .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                            .navigation();
                    finish();
                }
            }
        }
        BaseApp.setIsLock(false);
        BaseApp.setIsPromotion(true);
    }

    /**
     * 同意协议失败
     *
     * @param errMsg 失败信息
     */
    @Override
    public void conditionFail(String errMsg) {
        ToastUtil.showCenter(this,errMsg);
    }

    /**
     * 处理外部跳转
     *
     * @param outJumpBean 外部传递的参数
     */
    private void externalJump(OutJumpBean outJumpBean) {
        if (TextUtils.equals(outJumpBean.getSource(), BaseConstants.ExternalJumpFlag.INVITE)) {
            //邀请好友无需处理,只要跳转到首页就好了
            ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                    .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    .navigation();
        } else if (TextUtils.equals(outJumpBean.getSource(), BaseConstants.ExternalJumpFlag.ORDERQR) ||
                TextUtils.equals(outJumpBean.getSource(), BaseConstants.ExternalJumpFlag.HTML)) {
            ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity).navigation();
            ARouter.getInstance().build(ARouterPath.Cashier.ScanCashierActivity)
                    .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    .withString(BaseConstants.Transmit.XWALLETPAYBEANJSON, mOutJumpDateJson)
                    .navigation();
        } else if (TextUtils.equals(outJumpBean.getSource(), BaseConstants.ExternalJumpFlag.SMS)) {
            //短信
            String address = outJumpBean.getAddress();
        }
    }

    /**
     * 处理推送的接口
     *
     * @param extras 推送的数据
     */
    private void handlerJpush(String extras) {
        Log.e("LoginActivity>>>", "zfw handlerJpush>>> extras:"+ extras);
        try {
            JSONObject jsonObject = new JSONObject(extras);
            //目标页面
            String target = jsonObject.optString("target");
            String[] targetSplit = target.split("/");
            if (targetSplit.length < 2) {
                return;
            }
            if (TextUtils.equals(targetSplit[1], BaseConstants.MessageType.HOME)) {
                //跳转到首页
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                        .navigation();
            } else if (TextUtils.equals(targetSplit[1], BaseConstants.MessageType.TRANSACTION)) {
                //转账类型
                if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONLIST)) {
                    //历史账单列表
                    ARouter.getInstance().build(ARouterPath.TransferPath.HistoryActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .navigation();
                } else if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONDETAIL)) {
                    String orderId = jsonObject.optString(BaseConstants.Transmit.ORDERID);
                    String transType = jsonObject.optString(BaseConstants.Transmit.ORDERTYPE);

                    if (TextUtils.equals(transType, BaseConstants.HistoryType.PAYTYPE)) {
                        ARouter.getInstance().build(ARouterPath.TransferPath.HistoryPaymentActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    } else if (TextUtils.equals(transType, BaseConstants.HistoryType.WITHDRAWALTYPE)) {
                        ARouter.getInstance().build(ARouterPath.TransferPath.WithdrawalTypeActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    } else if (TextUtils.equals(transType, BaseConstants.HistoryType.AGENTTOPUP)||TextUtils.equals(transType, BaseConstants.HistoryType.AGENTWITH)) {
                        ARouter.getInstance().build(ARouterPath.TransferPath.AgentTopUpActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    } else if (TextUtils.equals(transType, BaseConstants.HistoryType.AGENTTRAN)) {
                        ARouter.getInstance().build(ARouterPath.TransferPath.HistoryAgentTranActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    } else if (TextUtils.equals(transType, BaseConstants.HistoryType.AGENTMOBILE)) {
                        ARouter.getInstance().build(ARouterPath.TransferPath.HistoryAgentMobileActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    } else {
                        ARouter.getInstance().build(ARouterPath.TransferPath.HistoryDetailsActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    }
                } else if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONSPILTRECLIST)) {
                    //AA我发起的列表
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEAALAUNCH)
                            .navigation();
                } else if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONSPILTPAYLIST)) {
                    //AA我支付的列表
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEAARECEIVED)
                            .navigation();
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}