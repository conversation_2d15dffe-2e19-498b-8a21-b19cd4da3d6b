package om.rrtx.mobile.loginmodule.activity;

import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.gson.Gson;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.HashMap;
import java.util.Set;

import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functionapi.bean.OutJumpBean;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.bean.ExtJumpBean;
import om.rrtx.mobile.loginmodule.R;
import om.rrtx.mobile.loginmodule.bean.UpDataBean;
import om.rrtx.mobile.loginmodule.presenter.SplashPresenter;
import om.rrtx.mobile.loginmodule.presenter.SplashView;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController;
import om.rrtx.mobile.rrtxcommon1.utils.AppInfoUtils;
import om.rrtx.mobile.rrtxcommon1.utils.LogUtil;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;


/**
 * <AUTHOR>
 * 闪屏页面
 * 这里弹出升级框的逻辑先处理成
 * splash请求,然后在第一个页面进行显示.用状态进行保存(这里的状态保存在application中)
 */
@Route(path = ARouterPath.LoginPath.SplashActivity)
public class SplashActivity extends BaseSuperActivity<SplashView, SplashPresenter> implements SplashView {

    private String mXWallerPayBeanJson;
    private boolean mIsOutJump;
    private String mJumpFlag, mJpushJson;

    /**
     * 处理外部跳转
     *
     * @return 是否处理
     */
    private boolean handlerOutJump(Uri data) {
        //这里应该先判断登录,
        String userName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERNAME, "");
        String authorization = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.AUTHORIZATION, "");
        Log.e("SplashActivity>>>", "zfw handlerOutJump>>> userName:" + userName);
        Log.e("SplashActivity>>>", "zfw handlerOutJump>>> authorization:" + authorization);
        //理论上这个type是判断类型的,但是现在这两个类型的处理是一样的就没有区分
        String source = data.getQueryParameter("source");
        Log.e("SplashActivity>>>", "zfw handlerOutJump>>> source:" + source);
        if (TextUtils.isEmpty(source)) {
            return false;
        }

        //这里设置相应的参数
        // 这个只有短信的时候有
        String address = data.getQueryParameter("address");
        // 这个只有支付的时候有
        String payToken = data.getQueryParameter("payToken");
        Log.e("SplashActivity>>>", "zfw handlerOutJump>>> address:" + address);
        Log.e("SplashActivity>>>", "zfw handlerOutJump>>> payToken:" + payToken);
        OutJumpBean outJumpBean = new OutJumpBean();
        outJumpBean.setAddress(address);
        outJumpBean.setSource(source);
        outJumpBean.setIsOutJump(BaseConstants.QrType.WEBTOKEN);
        outJumpBean.setPayToken(payToken);

        //处理相应的跳转
        if (TextUtils.isEmpty(userName)) {
            ARouter.getInstance().build(ARouterPath.LoginPath.LoginActivity)
                    .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.HomeJumpType.HomeHomeJump)
                    .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                    .withString(BaseConstants.Transmit.OUTJUMPDATEJSON, new Gson().toJson(outJumpBean))
                    .navigation();
        } else {
            if (!TextUtils.isEmpty(authorization)) {
                //直接处理跳转
                externalJump(outJumpBean);
            } else {
                //这里是因为没有用户名,需要重新登录一下
                boolean isFinger = (boolean) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ISFINGER, false);
                if (isFinger) {
                    ARouter.getInstance().build(ARouterPath.SecurityPath.LoginFingerLockActivity)
                            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.HomeJumpType.OutCashierJump)
                            .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .withString(BaseConstants.Transmit.OUTJUMPDATEJSON, new Gson().toJson(outJumpBean))
                            .withBoolean(BaseConstants.Transmit.ISPSDBACK, false)
                            .navigation();
                } else {
                    ARouter.getInstance().build(ARouterPath.SecurityPath.LoginPasswordLockActivity)
                            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.HomeJumpType.OutCashierJump)
                            .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .withString(BaseConstants.Transmit.OUTJUMPDATEJSON, new Gson().toJson(outJumpBean))
                            .withBoolean(BaseConstants.Transmit.ISPSDBACK, false)
                            .navigation();
                }
            }
        }

        return true;
    }

    /**
     * 处理外部跳转
     *
     * @param outJumpBean 外部传递的参数
     */
    private void externalJump(OutJumpBean outJumpBean) {
        if (TextUtils.equals(outJumpBean.getSource(), BaseConstants.ExternalJumpFlag.INVITE)) {
            if (ActivityController.getInstance().getStackActivitySize() > 1) {
                finish();
            } else {
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .navigation();
            }
        } else if (TextUtils.equals(outJumpBean.getSource(), BaseConstants.ExternalJumpFlag.ORDERQR) ||
                TextUtils.equals(outJumpBean.getSource(), BaseConstants.ExternalJumpFlag.HTML)) {
            ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                    .navigation();
            ARouter.getInstance().build(ARouterPath.Cashier.ScanCashierActivity)
                    .withString(BaseConstants.Transmit.XWALLETPAYBEANJSON, new Gson().toJson(outJumpBean))
                    .navigation();
        } else {
            //其他的先按照主页处理就好了.
            finish();
        }
    }

    @Override
    protected int createContentView() {
        return R.layout.login_activity_splash;
    }

    @Override
    protected SplashPresenter createPresenter() {
        return new SplashPresenter(mContext);
    }

    @Override
    protected void initView() {
        //解决oppo手机重新走启动页的问题
        /*if (!isTaskRoot()) {
            finish();
            return;
        }*/

        BaseApp.setIsLock(true);
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        if (getIntent() != null) {
            if (getIntent().getExtras() != null) {
                mJumpFlag = BaseConstants.JumpFlag.JPUSHJUMP;
                Log.e("SplashActivity>>>", "zfw doGetExtra>>> 通知数据:" + getIntent().getExtras().keySet());
                Bundle bundle = getIntent().getExtras();
                Set<String> keys = getIntent().getExtras().keySet();
                HashMap<String, Object> map = new HashMap<String, Object>();
                for (String key : keys) {
                    Log.e("SplashActivity>>>", "zfw doGetExtra>>> key:" + key + "value" + bundle.get(key));
                    map.put(key, bundle.get(key));
                }
                mJpushJson = com.alibaba.fastjson.JSONObject.toJSONString(map, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullStringAsEmpty);
                Log.e("SplashActivity>>>", "zfw doGetExtra>>> 通知数据:" + mJpushJson);
            }
        }

    }

    @Override
    public void initDate() {
        super.initDate();
        //mPresenter.requestUpdate(AppInfoUtils.getVersionName(mContext));
        handJump();
    }

    @Override
    public void upDataSuccess(UpDataBean sResData) {
        Log.e("SplashActivity>>>", "zfw upDataSuccess>>>sResData :" + sResData);
        if (!TextUtils.equals(sResData.getIsLatestVersion(), "1") &&
                TextUtils.equals(sResData.getUpdateFlag(), "0")) {
            BaseApp.setIsHasDownLoadUrl(sResData.getDownloadUrl());
        }
    }

    private void handJump() {
        //这里的逻辑是先处理外部在处理内部的逻辑
        Log.e("SplashActivity>>>", "zfw upDataSuccess>>>getIntent() == null :" + (getIntent() == null));
        if (getIntent() != null) {
            //这个处理的是sdk下单的情况
            mXWallerPayBeanJson = getIntent().getStringExtra(BaseConstants.Transmit.XWALLETPAYBEANJSON);
            if (!TextUtils.isEmpty(mXWallerPayBeanJson)) {
                SharedPreferencesUtils.setParam(mContext, BaseConstants.Transmit.XWALLETPAYBEANJSON, mXWallerPayBeanJson);
            }

            //这里处理的是外部跳转的情况
            Uri data = getIntent().getData();
            LogUtil.e("TAG", "doGetExtra: " + data);
            if (data != null) {
                mIsOutJump = handlerOutJump(data);
            }
        }
        Log.e("SplashActivity>>>", "zfw upDataSuccess>>> mIsOutJump:" + mIsOutJump);
        if (mIsOutJump) {
            //外部处理了,就直接关闭这个页面就可以了
            finish();
        } else {
            //处理内部逻辑
            jumpDeal();
        }
    }

    private void jumpDeal() {
        //这里有三种情况
        //1. 用户名为空
        //2. 用户名不为空 token为空
        //3. 用户名不为空 token不为空
        String userName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERNAME, "");
        String authorization = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.AUTHORIZATION, "");
        if (TextUtils.isEmpty(userName)) {
            if (!TextUtils.isEmpty(authorization)) {
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .navigation();
                if (!TextUtils.isEmpty(mXWallerPayBeanJson)) {
                    handleExtJump();
                } else if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.JPUSHJUMP)) {
                    if (getIntent() != null) {
                        handlerJpush(mJpushJson);
                    }
                }
            } else {
                if (!TextUtils.isEmpty(mXWallerPayBeanJson)) {
                    Intent intent = new Intent(this, LoginActivity.class);
                    intent.putExtra(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP);
                    startActivity(intent);
                } else if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.JPUSHJUMP)) {
                    ARouter.getInstance().build(ARouterPath.LoginPath.LoginActivity)
                            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.JPUSHJUMP)
                            .withString(BaseConstants.Transmit.JPUSHJSON, mJpushJson)
                            .navigation();
                } else {
                    LoginActivity.jumpLogin(mContext);
                }
            }
        } else {
            if (!TextUtils.isEmpty(authorization)) {
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .navigation();
                if (!TextUtils.isEmpty(mXWallerPayBeanJson)) {
                    handleExtJump();

                } else if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.JPUSHJUMP)) {
                    if (getIntent() != null) {
                        handlerJpush(mJpushJson);
                    }
                }
            } else {
                boolean isFinger = (boolean) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ISFINGER, false);
                if (isFinger) {
                    if (!TextUtils.isEmpty(mXWallerPayBeanJson)) {
                        ARouter.getInstance().build(ARouterPath.SecurityPath.LoginFingerLockActivity)
                                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.HomeJumpType.CashierJump)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withBoolean(BaseConstants.Transmit.ISPSDBACK, false)
                                .navigation();
                    } else if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.JPUSHJUMP)) {
                        ARouter.getInstance().build(ARouterPath.SecurityPath.LoginFingerLockActivity)
                                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.JPUSHJUMP)
                                .withString(BaseConstants.Transmit.JPUSHJSON, mJpushJson)
                                .withBoolean(BaseConstants.Transmit.ISPSDBACK, false)
                                .navigation();
                    } else {
                        ARouter.getInstance().build(ARouterPath.SecurityPath.LoginFingerLockActivity)
                                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP)
                                .withBoolean(BaseConstants.Transmit.ISPSDBACK, false)
                                .navigation();
                    }
                } else {
                    if (!TextUtils.isEmpty(mXWallerPayBeanJson)) {
                        ARouter.getInstance().build(ARouterPath.SecurityPath.LoginPasswordLockActivity)
                                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.HomeJumpType.CashierJump)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withBoolean(BaseConstants.Transmit.ISPSDBACK, false)
                                .navigation();
                    } else if (TextUtils.equals(mJumpFlag, BaseConstants.JumpFlag.JPUSHJUMP)) {
                        ARouter.getInstance().build(ARouterPath.SecurityPath.LoginPasswordLockActivity)
                                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.JPUSHJUMP)
                                .withString(BaseConstants.Transmit.JPUSHJSON, mJpushJson)
                                .withBoolean(BaseConstants.Transmit.ISPSDBACK, false)
                                .navigation();
                    } else {
                        ARouter.getInstance().build(ARouterPath.SecurityPath.LoginPasswordLockActivity)
                                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP)
                                .withBoolean(BaseConstants.Transmit.ISPSDBACK, false)
                                .navigation();
                    }
                }
            }
        }

        BaseApp.setIsPromotion(true);
        finish();
    }

    @Override
    public void requestFail(String sResData) {
        Log.e("SplashActivity>>>", "zfw requestFail>>>sResData :" + sResData);
        if (!mIsOutJump) {
            jumpDeal();
        } else {
            finish();
        }
    }

    private void handleExtJump() {
        ExtJumpBean extJumpBean = new Gson().fromJson(mXWallerPayBeanJson, ExtJumpBean.class);
        switch (extJumpBean.getJumpType()) {
            case CommonConstants.ExtJumpType.Jump_App_Pay:
                ARouter.getInstance().build(ARouterPath.Cashier.CashierActivity)
                        .withString(CommonConstants.Transmit.XWALLETPAYBEANJSON, mXWallerPayBeanJson)
                        .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .navigation();
                break;
            case CommonConstants.ExtJumpType.AUTO_Debit_Sign:
                ARouter.getInstance().build(ARouterPath.SecurityPath.AutoDebitActivity)
                        .withString(CommonConstants.Transmit.XWALLETPAYBEANJSON, mXWallerPayBeanJson)
                        .addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP)
                        .navigation();
                break;
        }
    }

    /**
     * 处理推送的接口
     *
     * @param extras 推送的数据
     */
    private void handlerJpush(String extras) {
        Log.e("SplashActivity>>>", "zfw handlerJpush>>> extras:" + extras);
        try {
            JSONObject jsonObject = new JSONObject(extras);
            //目标页面
            String target = jsonObject.optString("target");
            String[] targetSplit = target.split("/");
            if (targetSplit.length < 2) {
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                        .navigation();
            } else {
                if (TextUtils.equals(targetSplit[1], BaseConstants.MessageType.HOME)) {
                    //跳转到首页
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .navigation();
                } else if (TextUtils.equals(targetSplit[1], BaseConstants.MessageType.TRANSACTION)) {
                    //转账类型
                    if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONLIST)) {
                        //历史账单列表
                        ARouter.getInstance().build(ARouterPath.TransferPath.HistoryActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .navigation();
                    } else if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONDETAIL)) {
                        String orderId = jsonObject.optString(BaseConstants.Transmit.ORDERID);
                        String transType = jsonObject.optString(BaseConstants.Transmit.ORDERTYPE);

                        if (TextUtils.equals(transType, BaseConstants.HistoryType.PAYTYPE)) {
                            ARouter.getInstance().build(ARouterPath.TransferPath.HistoryPaymentActivity)
                                    .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                    .withString(BaseConstants.Transmit.ORDERID, orderId)
                                    .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                    .navigation();
                        } else if (TextUtils.equals(transType, BaseConstants.HistoryType.WITHDRAWALTYPE)) {
                            ARouter.getInstance().build(ARouterPath.TransferPath.WithdrawalTypeActivity)
                                    .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                    .withString(BaseConstants.Transmit.ORDERID, orderId)
                                    .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                    .navigation();
                        } else if (TextUtils.equals(transType, BaseConstants.HistoryType.AGENTTOPUP) || TextUtils.equals(transType, BaseConstants.HistoryType.AGENTWITH)) {
                            ARouter.getInstance().build(ARouterPath.TransferPath.AgentTopUpActivity)
                                    .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                    .withString(BaseConstants.Transmit.ORDERID, orderId)
                                    .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                    .navigation();
                        } else if (TextUtils.equals(transType, BaseConstants.HistoryType.AGENTTRAN)) {
                            ARouter.getInstance().build(ARouterPath.TransferPath.HistoryAgentTranActivity)
                                    .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                    .withString(BaseConstants.Transmit.ORDERID, orderId)
                                    .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                    .navigation();
                        } else if (TextUtils.equals(transType, BaseConstants.HistoryType.AGENTMOBILE)) {
                            ARouter.getInstance().build(ARouterPath.TransferPath.HistoryAgentMobileActivity)
                                    .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                    .withString(BaseConstants.Transmit.ORDERID, orderId)
                                    .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                    .navigation();
                        } else {
                            ARouter.getInstance().build(ARouterPath.TransferPath.HistoryDetailsActivity)
                                    .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                    .withString(BaseConstants.Transmit.ORDERID, orderId)
                                    .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                    .navigation();
                        }
                    } else if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONSPILTRECLIST)) {
                        //AA我发起的列表
                        ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEAALAUNCH)
                                .navigation();
                    } else if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONSPILTPAYLIST)) {
                        //AA我支付的列表
                        ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEAARECEIVED)
                                .navigation();
                    }
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }
}
