package om.rrtx.mobile.loginmodule.activity

import android.content.Context
import android.content.Intent
import android.view.Gravity
import android.view.ViewGroup
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.activity_registration.tv_all_step
import kotlinx.android.synthetic.main.activity_registration.tv_cur_page
import kotlinx.android.synthetic.main.activity_registration.tv_cur_step
import kotlinx.android.synthetic.main.login_base_title.backIv
import kotlinx.android.synthetic.main.login_base_title.titleTv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.loginmodule.R
import om.rrtx.mobile.loginmodule.databinding.LoginRegisterResultBinding
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2sp
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper


@Route(path = ARouterPath.LoginPath.RegisterResultActivity)
class RegisterResultActivity : BaseActivity<LoginRegisterResultBinding>() {

    var mJumpFlag = ""
    var mUserStatus = ""

    companion object {
        @JvmStatic
        fun jump(context: Context, flag: String) {
            val intent = Intent(context, RegisterResultActivity::class.java)
            intent.putExtra(BaseConstants.Transmit.JUMPFLAG, flag)
            context.startActivity(intent)
        }
    }

    override fun doGetExtra() {
        super.doGetExtra()
        mJumpFlag = intent.getStringExtra(BaseConstants.Transmit.JUMPFLAG).toString()
        mUserStatus = intent.getStringExtra(BaseConstants.Transmit.USERSTATUS).toString()
    }

    override fun createContentView() = R.layout.login_register_result

    override fun initWorkspaceAction() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true)
            .init()

        backIv.background = ResourceHelper.getDrawable(R.drawable.login_ic_back)
        titleTv.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
        titleTv.setTextColor(ResourceHelper.getColor(R.color.color_131313))
        if (BaseConstants.JumpFlag.Register_junior_Account == mJumpFlag) {
            titleTv.text = getString(R.string.register_junior)
            tv_cur_page.text = "5"
            tv_all_step.text = "/5"
        } else {
            titleTv.text = getString(R.string.register_title_user_registration)
            tv_cur_page.text = "6"
            tv_all_step.text = "/6"
        }

        backIv.setOnClickListener {
            if (BaseConstants.JumpFlag.Register_junior_Account == mJumpFlag) {
                ARouter.getInstance().build(ARouterPath.HomePath.JuniorAccountManagementActivity)
                    .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    .navigation()
            } else {
                ARouter.getInstance().build(ARouterPath.LoginPath.LoginActivity)
                    .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    .navigation()
            }
        }
        dataBinding.tvSend.setOnClickListener {
            if (BaseConstants.JumpFlag.Register_junior_Account == mJumpFlag) {
                ARouter.getInstance().build(ARouterPath.HomePath.JuniorAccountManagementActivity)
                    .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    .navigation()
            } else {
                ARouter.getInstance().build(ARouterPath.LoginPath.LoginActivity)
                    .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP)
                    .navigation()
            }
        }
    }

    override fun initView() {
        if ("0" == mUserStatus){
            tv_cur_step.text = getString(R.string.completed)
            dataBinding.hintTv.apply {
                text = ResourceHelper.getString(R.string.register_success)
                textSize = 32f.pt2sp()
                setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
                gravity = Gravity.CENTER_HORIZONTAL
            }
            dataBinding.phoneTv.text = ""
        }else{
            tv_cur_step.text = getString(R.string.authentication)
            // 注册完成
            dataBinding.hintTv.apply {
                text = if (BaseConstants.JumpFlag.Register_junior_Account == mJumpFlag){
                    ResourceHelper.getString(R.string.have_received_registration1)
                }else{
                    ResourceHelper.getString(R.string.have_received_registration)
                }
                textSize = 32f.pt2sp()
                setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
            }

            dataBinding.phoneTv.apply {
                text = if (BaseConstants.JumpFlag.Register_junior_Account == mJumpFlag){
                    ResourceHelper.getString(R.string.go_authentication1)
                }else{
                    ResourceHelper.getString(R.string.go_authentication)
                }
                textSize = 28f.pt2sp()
                setTextColor(ResourceHelper.getColor(R.color.common_text_4E5969))
            }
        }

        if (BaseConstants.JumpFlag.Register_Account == mJumpFlag && "0" == mUserStatus){
            dataBinding.tvSend.text = ResourceHelper.getString(R.string.common_btn_login)
        }else{
            dataBinding.tvSend.text = ResourceHelper.getString(R.string.done)
        }

        val textView = dataBinding.tvSend
        val params = textView.layoutParams as ViewGroup.MarginLayoutParams
        params.topMargin = 698
        textView.layoutParams = params

    }
}