package om.rrtx.mobile.loginmodule.viewmodel

import android.app.Application
import android.util.Log
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import om.rrtx.mobile.functioncommon.model.CommonModel
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean
import om.rrtx.mobile.rrtxcommon1.bean.UserInfoBean
import om.rrtx.mobile.rrtxcommon1.net.BaseObserverNoError
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController

class LoginViewModel(application: Application) : AndroidViewModel(application) {

    val mCheckUserLd = MutableLiveData<UserInfoBean>()
    val mCheckUserErrorLd = MutableLiveData<String>()
    private val mComModel = CommonModel()

    fun getUserInfoByIdCardAndMobile(mobile: String,id: String) {
        val activity = ActivityController.getInstance().currentActivity()
        mComModel.getUserInfoByIdCardAndMobile(mobile, id,
            object : BaseObserverNoError<UserInfoBean>(activity) {
                override fun requestSuccess(bean: UserInfoBean) {
                    mCheckUserLd.value = bean
                }
            })
    }

    fun requestCheckUser(mobile: String, name: String, id: String) {
        val activity = ActivityController.getInstance().currentActivity()
        mComModel.requestCheckUser(mobile,
            name,
            id,
            object : BaseObserverNoError<UserInfoBean>(activity) {
                override fun requestSuccess(bean: UserInfoBean) {
                    mCheckUserLd.value = bean
                }

                override fun requestErrorBody(body: BaseBean<UserInfoBean>) {
                    mCheckUserErrorLd.value = body.status
                }
            })
    }

    fun requestCheckJuniorAccount(juniorUserId: String, idNumber: String, userName: String) {
        val activity = ActivityController.getInstance().currentActivity()
        mComModel.requestCheckJuniorAccount(juniorUserId,
            idNumber,
            userName,
            object : BaseObserverNoError<UserInfoBean>(activity) {
                override fun requestSuccess(bean: UserInfoBean) {
                    mCheckUserLd.value = bean
                }
            })
    }
}