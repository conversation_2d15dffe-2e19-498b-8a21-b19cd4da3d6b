package om.rrtx.mobile.loginmodule.bean;

/**
 * <AUTHOR>
 */
public class UpDataBean {

    /**
     * isLatestVersion : 1
     * updateFlag :
     * downloadUrl :
     */

    private String isLatestVersion;
    private String updateFlag;
    private String downloadUrl;

    public String getIsLatestVersion() {
        return isLatestVersion;
    }

    public void setIsLatestVersion(String isLatestVersion) {
        this.isLatestVersion = isLatestVersion;
    }

    public String getUpdateFlag() {
        return updateFlag;
    }

    public void setUpdateFlag(String updateFlag) {
        this.updateFlag = updateFlag;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    @Override
    public String toString() {
        return "UpDataBean{" +
                "isLatestVersion='" + isLatestVersion + '\'' +
                ", updateFlag='" + updateFlag + '\'' +
                ", downloadUrl='" + downloadUrl + '\'' +
                '}';
    }
}
