package om.rrtx.mobile.loginmodule.presenter;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import om.rrtx.mobile.loginmodule.bean.UpDataBean;
import om.rrtx.mobile.rrtxcommon1.bean.LoginBean;
import om.rrtx.mobile.loginmodule.bean.PubBean;
import om.rrtx.mobile.loginmodule.model.LoginModel;
import om.rrtx.mobile.loginmodule.view.LoginView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 登录页面P层
 */
public class LoginPresenter extends BasePresenter<LoginView> {

    private LoginModel mLoginModel;
    private Context mContext;

    public LoginPresenter(Context context) {
        mLoginModel = new LoginModel();
        mContext = context;
    }

    public void requestUpdate(String versionName) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (TextUtils.isEmpty(pubLick)){
            mLoginModel.commonPub(new BaseNoDialogObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext,BaseConstants.SaveParameter.PUBLICKEY,sResData.getPubKeyBase64());
                    mLoginModel.requestUpData(userId,versionName, new BaseNoDialogObserver<UpDataBean>(mContext) {
                        @Override
                        public void requestSuccess(UpDataBean sResData) {
                            Log.e("SplashPresenter>>>", "zfw requestSuccess>>>sResData :"+sResData );
                            if (getView() != null) {
                                getView().upDataSuccess(sResData);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            Log.e("SplashPresenter>>>", "zfw requestFail>>> 000:"+sResMsg );
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {
                    Log.e("SplashPresenter>>>", "zfw requestFail>>> 111:"+sResMsg );
                }
            });
        }else {
            mLoginModel.requestUpData(userId,versionName, new BaseNoDialogObserver<UpDataBean>(mContext) {
                @Override
                public void requestSuccess(UpDataBean sResData) {
                    Log.e("SplashPresenter>>>", "zfw requestSuccess>>>sResData.toString() :"+sResData.toString() );
                    Log.e("SplashPresenter>>>", "zfw requestSuccess>>>getView() == null :"+(getView() == null) );
                    if (getView() != null) {
                        getView().upDataSuccess(sResData);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    Log.e("SplashPresenter>>>", "zfw requestFail>>> 222:"+sResMsg );
                    if (getView() != null) {
                        if ("RRB-05009001".equals(sResMsg)) {
                            requestUpdate(versionName);
                        }
                    }
                }
            });
        }

    }

    /**
     * 请求登录接口
     *
     * @param userName       用户名称
     * @param passwprd       用户密码
     * @param registrationId
     */
    public void requestLogin(String userName, String passwprd, String registrationId, String token) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
//        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mLoginModel.requestLogin1(userName, passwprd, registrationId,token, new BaseObserver<LoginBean>(mContext) {
                @Override
                public void requestSuccess(LoginBean loginBean) {
                    if (getView() != null) {
                        getView().loginSuccess(loginBean);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                }

                @Override
                public void requestErrorBody(BaseBean<LoginBean> body) {
                    if (getView() != null) {
                        getView().requestFail(body);
                    }
                }
            });
        } else {
            mLoginModel.commonPub(new BaseNoDialogObserver<PubBean>(mContext) {
                @Override
                public void requestFail(String sResMsg) {
                    ToastUtil.show(mContext,sResMsg);
                }

                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mLoginModel.requestLogin1(userName, passwprd, registrationId,token, new BaseObserver<LoginBean>(mContext) {
                        @Override
                        public void requestSuccess(LoginBean loginBean) {
                            if (getView() != null) {
                                getView().loginSuccess(loginBean);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                        }

                        @Override
                        public void requestErrorBody(BaseBean<LoginBean> body) {
                            if (getView() != null) {
                                getView().requestFail(body);
                            }
                        }
                    });
                }
            });
        }

    }
}
