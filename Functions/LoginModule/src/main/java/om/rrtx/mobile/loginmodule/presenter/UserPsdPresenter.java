package om.rrtx.mobile.loginmodule.presenter;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.loginmodule.bean.LocalRegionBean;
import om.rrtx.mobile.loginmodule.bean.PubBean;
import om.rrtx.mobile.loginmodule.model.LoginModel;
import om.rrtx.mobile.loginmodule.view.UserPsdView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.bean.UserInfoBean;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * 登录页面P层
 */
public class UserPsdPresenter extends BasePresenter<UserPsdView> {

    private LoginModel mLoginModel;
    private Context mContext;

    public UserPsdPresenter(Context context) {
        mLoginModel = new LoginModel();
        mContext = context;
    }

    /**
     * 请求时区接口
     */
    public void requestRegion() {
        try {
            InputStream isMac = mContext.getAssets().open("json/nation_english.json");
            //一波读写操作
            String tempStr;
            BufferedReader bis = new BufferedReader(new InputStreamReader(isMac));
            StringBuilder sbData = new StringBuilder();
            while ((tempStr = bis.readLine()) != null) {
                sbData.append(tempStr);
            }
            bis.close();
            isMac.close();

            LocalRegionBean localRegionBean = new Gson().fromJson(sbData.toString(), LocalRegionBean.class);
            if (getView() != null) {
                getView().regionSuccess(localRegionBean.getData());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 忘记用户名
     *
     * @param idCard 卡号
     * @param mobile 电话
     */
    public void requestForgetName(String idCard, String mobile) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mLoginModel.requestForgetName(userId, idCard, mobile, new BaseObserver<Object>(mContext) {
                @Override
                public void requestSuccess(Object object) {
                    if (getView() != null) {
                        getView().forgetNameSuccess();
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        } else {
            mLoginModel.commonPub(new BaseNoDialogObserver<PubBean>(mContext) {
                @Override
                public void requestFail(String sResMsg) {

                }

                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mLoginModel.requestForgetName(userId, idCard, mobile, new BaseObserver<Object>(mContext) {
                        @Override
                        public void requestSuccess(Object object) {
                            if (getView() != null) {
                                getView().forgetNameSuccess();
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }
            });
        }
    }

    /**
     * 查询用户信息
     *
     * @param nric     卡号
     * @param userName 用户名
     */
    public void requestQueryUserInfo(String nric, String userName) {
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        String mobile = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERMOBILE, "");
        new CommonModel().requestUserInfo(mobile, nric, userName, new BaseObserver<UserInfoBean>(mContext) {
            @Override
            public void requestSuccess(UserInfoBean personalInfoBean) {
                if (getView() != null) {
                    getView().getUserInfoSuccess(personalInfoBean);
                }
            }

            @Override
            public void requestFail(String sResMsg) {
                if (getView() != null) {
                    getView().requestFail(sResMsg);
                }
            }
        });

    }
}
