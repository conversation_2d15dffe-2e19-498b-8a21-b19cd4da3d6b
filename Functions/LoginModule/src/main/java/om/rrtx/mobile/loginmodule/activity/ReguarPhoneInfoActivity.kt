package om.rrtx.mobile.loginmodule.activity

import android.content.Context
import android.content.Intent
import android.text.Editable
import android.text.Spannable
import android.text.SpannableString
import android.text.TextUtils
import android.text.TextWatcher
import android.text.style.ForegroundColorSpan
import android.view.View
import android.widget.EditText
import com.google.gson.Gson
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.login_base_title.backIv
import kotlinx.android.synthetic.main.login_base_title.titleTv
import kotlinx.android.synthetic.main.login_reguar_phone.tv_all_step
import kotlinx.android.synthetic.main.login_reguar_phone.tv_cur_page
import kotlinx.android.synthetic.main.login_reguar_phone.tv_cur_step
import om.rrtx.mobile.functioncommon.activity.ConditionActivity
import om.rrtx.mobile.functioncommon.activity.RegisterVerCodeActivity
import om.rrtx.mobile.loginmodule.R
import om.rrtx.mobile.loginmodule.bean.RegisterInfoBean
import om.rrtx.mobile.loginmodule.databinding.LoginReguarPhoneBinding
import om.rrtx.mobile.loginmodule.login.viewModel.UserinfoRegisterVM
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseVVMActivity
import om.rrtx.mobile.rrtxcommon1.bean.ConditionBean
import om.rrtx.mobile.rrtxcommon1.dialog.AloneDialog
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.FormatCheck
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil

/**
 * 常规账户注册2
 * */
class ReguarPhoneInfoActivity : BaseVVMActivity<UserinfoRegisterVM, LoginReguarPhoneBinding>() {

    var mJumpFlag = ""
    private lateinit var curEditText: EditText

    // 协议选择
    private var isAgreement: Boolean = false
    val registerInfoBean: RegisterInfoBean = RegisterInfoBean()
    private lateinit var conditionBean: ConditionBean

    companion object {
        @JvmStatic
        fun jump(context: Context, flag: String) {
            val intent = Intent(context, ReguarPhoneInfoActivity::class.java)
            intent.putExtra(BaseConstants.Transmit.JUMPFLAG, flag)
            context.startActivity(intent)
        }
    }

    override fun doGetExtra() {
        super.doGetExtra()
        mJumpFlag = intent.getStringExtra(BaseConstants.Transmit.JUMPFLAG).toString()
    }

    override fun createContentView() = R.layout.login_reguar_phone

    override fun initView() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true)
            .init()

        backIv.background = ResourceHelper.getDrawable(R.drawable.login_ic_back)
        titleTv.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
        titleTv.setTextColor(ResourceHelper.getColor(R.color.color_131313))
        titleTv.text = getString(R.string.register_title_user_registration)

        backIv.setOnClickListener {
            NewRegistrationActivity.jump(mContext, BaseConstants.JumpFlag.Register_Account)
        }

        tv_cur_step.text = getString(R.string.mobile_No)
        tv_cur_page.text = "2"
        tv_all_step.text = "/6"
    }

    override fun onBackPressed() {
        NewRegistrationActivity.jump(mContext, BaseConstants.JumpFlag.Register_Account)
    }

    override fun initListener() {
        initFocusListener()
        initOnClickListener()
        initVResultListener()
        initTextChangedListener()

        dataBinding.cbOk.setOnCheckedChangeListener { bt, b ->
            viewModel.requestDictCodeList()
            isAgreement = b
            upInfo()
        }
    }

    /**
     * 手机号
     */
    private fun initFocusListener() {
        View.OnFocusChangeListener { view, b ->
            val ed = view as EditText
            if (b) {
                curEditText = ed
                if (dataBinding.mobileTIL.editText == curEditText) {
                    dataBinding.mobileTIL.hint = " "
                    dataBinding.mobileHint.visibility = View.VISIBLE
                }
                return@OnFocusChangeListener
            }
            when (ed) {
                dataBinding.mobileTIL.editText -> {
                    if (TextUtils.isEmpty(dataBinding.mobileTIL.editText?.text.toString())) {
                        dataBinding.mobileTIL.hint = getString(R.string.common_label_mobile)
                        dataBinding.mobileHint.visibility = View.GONE
                        dataBinding.mobileError.visibility = View.GONE

                    } else {
                        dataBinding.mobileTIL.hint = " "
                        dataBinding.mobileHint.visibility = View.VISIBLE
                        mobileFormatCheck(dataBinding.mobileTIL.editText!!.text.toString())

                    }
                }
            }
            upInfo()

        }.apply {
            dataBinding.mobileTIL.editText?.onFocusChangeListener = this
        }
    }

    private fun initOnClickListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                if (this@ReguarPhoneInfoActivity::curEditText.isInitialized) curEditText.clearFocus()
                when (view) {
                    dataBinding.tvCondition -> {
                        if (this@ReguarPhoneInfoActivity::conditionBean.isInitialized)
                            ConditionActivity.jumpCondition(
                                mContext,
                                conditionBean.conditionTitle,
                                conditionBean.conditionContent
                            )
                    }

                    dataBinding.tvNext -> onNext()
                }
            }

        }.apply {
            dataBinding.tvCondition.setOnClickListener(this)
            dataBinding.tvNext.setOnClickListener(this)
        }
    }

    private fun initVResultListener() {
        viewModel.registerResult.observe(this) {
            if (it == null) return@observe
            if (it.isWalletUser == "1") {
                when (it.userStatus) {
                    // 正常 冻结
                    "0", "1" -> {
                        val aloneDialog = AloneDialog(mContext)
                        aloneDialog.show()
                        aloneDialog.setTitleStr(ResourceHelper.getString(R.string.notice))
                        if (BaseConstants.JumpFlag.Register_junior_Account == mJumpFlag) {
                            aloneDialog.setContentStr(
                                ResourceHelper.getString(
                                    R.string.notice_registered,
                                    it.userName
                                )
                            )
                        } else {
                            aloneDialog.setContentStr(
                                ResourceHelper.getString(
                                    R.string.notice_registered2,
                                    it.userName
                                )
                            )
                        }
                    }
                    // 待认证
                    "4" -> {
                        val aloneDialog = AloneDialog(mContext)
                        aloneDialog.show()
                        aloneDialog.setTitleStr(ResourceHelper.getString(R.string.notice))
                        if (BaseConstants.JumpFlag.Register_junior_Account == mJumpFlag) {
                            aloneDialog.setContentStr(ResourceHelper.getString(R.string.notice_registered))
                        } else {
                            aloneDialog.setContentStr(ResourceHelper.getString(R.string.notice_registered1))
                        }
                    }
                }

            } else {
                SharedPreferencesUtils.setParam(
                    mContext,
                    BaseConstants.SaveParameter.REGISTER_INFO,
                    Gson().toJson(registerInfoBean)
                );
                SharedPreferencesUtils.setParam(
                    mContext,
                    BaseConstants.SaveParameter.CONDITION_INFO,
                    Gson().toJson(conditionBean)
                );
                RegisterVerCodeActivity.jump(mContext, registerInfoBean.mobile, mJumpFlag)
            }
        }

        viewModel.conditionBeanResult.observe(this) {
            conditionBean = it
        }
    }

    private fun initTextChangedListener() {
        object : TextWatcher {
            override fun beforeTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun onTextChanged(p0: CharSequence?, p1: Int, p2: Int, p3: Int) {
            }

            override fun afterTextChanged(p0: Editable?) {
                val toString = p0.toString()
                if (toString.length == 9) {
                    mobileFormatCheck(toString)
                    upInfo()
                } else {
                    mobileFormatCheck(toString)
                }
            }
        }.apply {
            dataBinding.mobileTIL.editText?.addTextChangedListener(this)
        }
    }

    private fun onNext() {
        if (!FormatCheck.mobileFormatCheck(registerInfoBean.mobile)) {
            ToastUtil.show(mContext, getString(R.string.mobile_error))
            return
        }
        viewModel.requestPhoneValidate(registerInfoBean.areaCode, registerInfoBean.mobile)
    }

    /**
     * 手机号码校验
     */
    fun mobileFormatCheck(text: String) {
        if (FormatCheck.mobileFormatCheck(text)) {
            dataBinding.mobileError.visibility = View.GONE
            registerInfoBean.mobile = text
        } else {
            dataBinding.mobileError.visibility = View.VISIBLE
            registerInfoBean.mobile = ""
        }
    }

    override fun initData() {
        initCondition()
        viewModel.getLatestCondition("01")
    }

    private fun initCondition() {
        val str1 = getString(R.string.read_and_accept)
        val spanString =
            SpannableString(str1 + " " + getString(R.string.terms_and_conditions))
        val s1Color = ForegroundColorSpan(ResourceHelper.getColor(R.color.common_text_86909C))
        spanString.setSpan(s1Color, 0, str1.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
        val s2Color = ForegroundColorSpan(ResourceHelper.getColor(R.color.common_ye_F3881E))
        spanString.setSpan(
            s2Color,
            str1.length,
            spanString.length,
            Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
        )
        dataBinding.tvCondition.text = spanString
    }

    fun upInfo() {
        // 手动触发失焦采集输入信息
        if (this::curEditText.isInitialized) curEditText.clearFocus()
        val isEnabled = isAgreement && registerInfoBean.mobile.isNotEmpty()
        dataBinding.tvNext.isEnabled = isEnabled
        if (isEnabled) {
            dataBinding.tvNext.background = ResourceHelper.getDrawable(R.drawable.common_usable_btn)
        } else {
            dataBinding.tvNext.background =
                ResourceHelper.getDrawable(R.drawable.common_unusable_btn)
        }

    }

    override fun onDestroy() {
        super.onDestroy()
        viewModel.registerResult.value = null
        viewModel.registerResult.removeObservers(this)
        viewModel.conditionBeanResult.removeObservers(this)
    }

}