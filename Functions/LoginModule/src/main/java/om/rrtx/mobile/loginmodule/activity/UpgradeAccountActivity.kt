package om.rrtx.mobile.loginmodule.activity

import android.text.TextUtils
import android.util.Log
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.google.gson.Gson
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.home_activity_upgrade_account.hint_tv
import kotlinx.android.synthetic.main.home_activity_upgrade_account.info_rv
import kotlinx.android.synthetic.main.home_activity_upgrade_account.next_tv
import kotlinx.android.synthetic.main.home_activity_upgrade_account.tv_condition
import kotlinx.android.synthetic.main.login_base_title.backIv
import kotlinx.android.synthetic.main.login_base_title.leftBg
import kotlinx.android.synthetic.main.login_base_title.titleTv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.activity.ConditionActivity
import om.rrtx.mobile.functioncommon.activity.VerCodeActivity
import om.rrtx.mobile.loginmodule.R
import om.rrtx.mobile.loginmodule.adapter.UpgradeInfoAdapter
import om.rrtx.mobile.loginmodule.databinding.HomeActivityUpgradeAccountBinding
import om.rrtx.mobile.loginmodule.login.viewModel.UserinfoRegisterVM
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseAdapterCallback
import om.rrtx.mobile.rrtxcommon1.base.BaseAdapterDataCallback
import om.rrtx.mobile.rrtxcommon1.base.BaseVVMActivity
import om.rrtx.mobile.rrtxcommon1.bean.ConditionBean
import om.rrtx.mobile.rrtxcommon1.bean.UpgradeInfoBean
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil
import om.rrtx.mobile.rrtxcommon1.widget.SpacesItemDecoration
import java.util.regex.Pattern

@Route(path = ARouterPath.LoginPath.UpgradeAccountActivity)
class UpgradeAccountActivity :
    BaseVVMActivity<UserinfoRegisterVM, HomeActivityUpgradeAccountBinding>() {

    private lateinit var mAdapter: UpgradeInfoAdapter
    private val mBean = UpgradeInfoBean()
    private lateinit var conditionBean: ConditionBean
    private var isNull: Boolean = false

    override fun createContentView() = R.layout.home_activity_upgrade_account

    override fun doGetExtra() {
        super.doGetExtra()
        mBean.juniorUserId = intent.getStringExtra(BaseConstants.Transmit.JUNIOR_ID).toString()
    }

    override fun initView() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true, 0.2f)
            .init()
        titleTv.setText(R.string.upgrade_Regular_Account)
        titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
        titleTv.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
        backIv.setBackgroundResource(R.drawable.common_ic_back_black)

        hint_tv.text = getString(R.string.users_must_be_at_least_16_years_old)
        val iconId = R.drawable.ic_hint
        val drawableLeft = ResourceHelper.getDrawable(iconId)
        drawableLeft?.setBounds(0, 0, 24.pt2px(), 24.pt2px())
        hint_tv.setCompoundDrawables(drawableLeft, null, null, null);
        hint_tv.compoundDrawablePadding = 10.pt2px()

        info_rv.apply {
            layoutManager = LinearLayoutManager(mContext)
            mAdapter = UpgradeInfoAdapter(mBean, object : BaseAdapterDataCallback<UpgradeInfoBean> {
                override fun callBack(position: Int,mBean: UpgradeInfoBean) {
                    Log.e("bean===",mBean.toString())
                    isNull = mBean.verify()
                    //next_tv.isEnabled = dataBinding.cbOk.isChecked && mBean.verify()
                }

            })
            adapter = mAdapter
            addItemDecoration(SpacesItemDecoration(24.pt2px()))
        }
    }

    override fun initData() {
        super.initData()
        viewModel.requestDictCodeList()
        viewModel.getLatestCondition("01")
        dataBinding.cbOk.setOnCheckedChangeListener { compoundButton, b ->
            next_tv.isEnabled = b && isNull
        }

    }

    override fun initVMListener() {
        viewModel.dictCodeListResult.observe(this) {
            mAdapter.setDictCodeList(it.appDictCodeList)
        }
        viewModel.conditionBeanResult.observe(this) {
            conditionBean = it
        }
        viewModel.checkUpGradleResult.observe(this) {
            mBean.mobile = it.mobile

            mBean.conditionTitle = conditionBean.conditionTitle
            mBean.conditionVersionNo = conditionBean.conditionVersionNo
            mBean.conditionType = conditionBean.conditionType

            SharedPreferencesUtils.setParam(
                mContext,
                BaseConstants.SaveParameter.UPGRADE_INFO,
                Gson().toJson(mBean)
            )
            VerCodeActivity.jump(mContext, it.mobile, BaseConstants.JumpFlag.Upgrade_junior_Account)
        }
    }

    override fun initClickListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    next_tv -> {
                        val p1 =
                            Pattern.compile("^\\d{9}[A-HJ-NP-TV-Za-hj-np-tv-z]\\d{2}\$")
                        val p2 =
                            Pattern.compile("^\\d{8}[A-HJ-NP-TV-Za-hj-np-tv-z]\\d{2}\$")
                        val group1 = p1.matcher(mBean.idNumber).matches()
                        val group2 = p2.matcher(mBean.idNumber).matches()
                        if (mBean.idType == "10") {
                            if (!group1&&!group2) {
                                ToastUtil.show(mContext, getString(R.string.idcard_format))
                                return
                            }
                            /*if (!StringUtils.isValidIdNumber(mBean.idNumber)) {
                                ToastUtil.show(mContext, getString(R.string.idcard_error))
                                return
                            }*/
                        }

                        val p =
                            Pattern.compile("^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\\.[a-zA-Z0-9_-]+)+");
                        val group = p.matcher(mBean.email).matches()
                        if (!TextUtils.isEmpty(mBean.email)&&!group) {
                            ToastUtil.show(mContext, mContext.getString(R.string.email_error))
                            return
                        }

                        viewModel.requestCheckUpGradeInfo(mBean)
                    }

                    tv_condition -> jumpCondition()

                    leftBg -> onBackPressed()
                }
            }
        }.apply {
            next_tv.setOnClickListener(this)
            tv_condition.setOnClickListener(this)
            leftBg.setOnClickListener(this)
        }
    }

    private fun jumpCondition() {
        if (this@UpgradeAccountActivity::conditionBean.isInitialized) ConditionActivity.jumpCondition(
            mContext,
            conditionBean.conditionTitle,
            conditionBean.conditionContent
        )
    }
}