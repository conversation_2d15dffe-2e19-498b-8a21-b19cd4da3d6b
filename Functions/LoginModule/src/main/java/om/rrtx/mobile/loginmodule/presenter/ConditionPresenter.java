package om.rrtx.mobile.loginmodule.presenter;

import android.content.Context;
import android.text.TextUtils;

import om.rrtx.mobile.loginmodule.bean.PubBean;
import om.rrtx.mobile.loginmodule.bean.UpDataBean;
import om.rrtx.mobile.loginmodule.model.LoginModel;
import om.rrtx.mobile.loginmodule.view.ConditionView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR> zfw
 * @time   : 2023/3/21 9:16
 * @desc   :
 */
public class ConditionPresenter extends BasePresenter<ConditionView> {

    private LoginModel mLoginModel;
    private Context mContext;

    public ConditionPresenter(Context context) {
        mLoginModel = new LoginModel(context);
        mContext = context;
    }

    public void agreeCondition(String mobile,String title,String versionNo) {
       String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
       if (TextUtils.isEmpty(pubLick)){
           mLoginModel.commonPub(new BaseNoDialogObserver<PubBean>(mContext) {
               @Override
               public void requestSuccess(PubBean sResData) {
                   SharedPreferencesUtils.setParam(mContext,BaseConstants.SaveParameter.PUBLICKEY,sResData.getPubKeyBase64());
                   mLoginModel.agreeCondition(mobile,title,versionNo, new BaseObserver<Object>(mContext) {
                       @Override
                       public void requestSuccess(Object object) {
                           if (getView() != null) {
                               getView().agreeSuccess();
                           }
                       }

                       @Override
                       public void requestFail(String errMsg) {
                           if (getView() != null) {
                               getView().conditionFail(errMsg);
                           }
                       }
                   });
               }

               @Override
               public void requestFail(String sResMsg) {
                   if (getView() != null) {
                       getView().conditionFail(sResMsg);
                   }
               }
           });
       }else {
           mLoginModel.agreeCondition(mobile,title,versionNo, new BaseObserver<Object>(mContext) {
               @Override
               public void requestSuccess(Object object) {
                   if (getView() != null) {
                       getView().agreeSuccess();
                   }
               }

               @Override
               public void requestFail(String errMsg) {
                   if (getView() != null) {
                       getView().conditionFail(errMsg);
                   }
               }
           });
       }

    }
}
