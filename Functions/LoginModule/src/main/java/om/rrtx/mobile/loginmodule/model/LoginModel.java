package om.rrtx.mobile.loginmodule.model;

import android.content.Context;
import android.text.TextUtils;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.loginmodule.LoginConstants;
import om.rrtx.mobile.loginmodule.LoginConstants1;
import om.rrtx.mobile.loginmodule.LoginService;
import om.rrtx.mobile.loginmodule.bean.UploadBean;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.bean.CheckAccountResultBean;
import om.rrtx.mobile.loginmodule.bean.PubBean;
import om.rrtx.mobile.loginmodule.bean.RegionBean;
import om.rrtx.mobile.loginmodule.bean.RegisterInfoBean;
import om.rrtx.mobile.loginmodule.bean.RegisterResultBean;
import om.rrtx.mobile.loginmodule.bean.RegistrationBean;
import om.rrtx.mobile.loginmodule.bean.UpDataBean;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.bean.AppDictListBean;
import om.rrtx.mobile.rrtxcommon1.bean.CodeBean;
import om.rrtx.mobile.rrtxcommon1.bean.ConditionBean;
import om.rrtx.mobile.rrtxcommon1.bean.LoginBean;
import om.rrtx.mobile.rrtxcommon1.bean.UpgradeInfoBean;
import om.rrtx.mobile.rrtxcommon1.model.CommonModel;
import om.rrtx.mobile.rrtxcommon1.net.BaseLoader;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.net.RetrofitServiceManager;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 登录模块的整天请求
 */
public class LoginModel extends BaseLoader {


    private Context mContext;
    private LoginService mLoginService;

    public LoginModel() {
        mLoginService = RetrofitServiceManager.getInstance().create(LoginService.class);
    }

    public LoginModel(Context context) {
        this.mContext = context;
        mLoginService = RetrofitServiceManager.getInstance().create(LoginService.class);
    }

    public void commonPub(BaseNoDialogObserver<PubBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        observe(mLoginService.requestPub(map)).subscribe(baseObserver);
    }

    /**
     * 请求登录接口
     *
     * @param userName       用户名
     * @param password       密码
     * @param registrationId
     * @param baseObserver   回调
     */
    public void requestLogin1(String userName, String password, String registrationId, String token, BaseObserver<LoginBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(LoginConstants.Parameter.PASSWORD, password);
        map.put("mobileNo", userName);
        map.put(LoginConstants.Parameter.REGISTRATIONID, registrationId);
        map.put(LoginConstants.Parameter.DEVICETOKEN, token);
        map.put("passwordType", "1");
        map.put("mobileAreaCode", "263");
        observe(mLoginService.requestLogin(map)).subscribe(baseObserver);
    }

    /**
     * 获取时区接口
     */
    public void requestRegion(BaseObserver<List<RegionBean>> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        observe(mLoginService.requestRegion(map)).subscribe(baseObserver);
    }

    /**
     * 检查更新
     *
     * @param versionName
     * @param baseObserver 回调
     */
    public void requestUpData(String userId, String versionName, BaseNoDialogObserver<UpDataBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(LoginConstants.Parameter.OSTYPE, "1");
        map.put(LoginConstants.Parameter.VERSIONNO, String.valueOf(versionName));
        map.put(LoginConstants.Parameter.USERID, userId);
        observe(mLoginService.requestUpData(map)).subscribe(baseObserver);
    }

    /**
     * 上传图片
     *
     * @param userPhoto    头像文件
     * @param baseObserver 回调
     */
    public void uploadPhoto(File userPhoto, BaseObserver<UploadBean> baseObserver) {
        RequestBody requestFile =
                RequestBody.create(MediaType.parse("multipart/form-data"), userPhoto);

        MultipartBody.Part body =
                MultipartBody.Part.createFormData("file", userPhoto.getName(), requestFile);

        String authorization = (String) SharedPreferencesUtils.getParam(BaseApp.getAPPContext(), BaseConstants.SaveParameter.AUTHORIZATION, "");

        observe(mLoginService.uploadPhoto(body)).subscribe(baseObserver);
    }

    /**
     * 校验注册信息
     */
    public void requestValidate(RegisterInfoBean bean, int type, BaseObserver<RegisterResultBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(LoginConstants1.Parameter.AREACODE, bean.getAreaCode());
        map.put(LoginConstants1.Parameter.MOBILE, bean.getMobile());
        map.put(LoginConstants1.Parameter.FIRSTNAME, bean.getFirstName());
        if (!TextUtils.isEmpty(bean.getMiddleName())) {
            map.put(LoginConstants1.Parameter.MIDDLENAME, bean.getMiddleName());
        }
        map.put(LoginConstants1.Parameter.LASTNAME, bean.getLastName());
        map.put(LoginConstants1.Parameter.GENDER, bean.getGender());
        map.put(LoginConstants1.Parameter.BIRTHDAY, bean.getDateOfBirth());
        map.put(LoginConstants1.Parameter.IDTYPE, bean.getIdType());
        map.put(LoginConstants1.Parameter.IDNUMBER, bean.getIdNumber());
        map.put(LoginConstants1.Parameter.CITYCODE, bean.getCityCode());
        map.put(LoginConstants1.Parameter.ADDRESS, bean.getAddress());
        map.put(LoginConstants1.Parameter.EMAIL, bean.getEmail());
        map.put(LoginConstants1.Parameter.RELATION_SHIP, bean.getRelationShip() + "");

        if (type == 0) {
            observe(mLoginService.requestValidate(map)).subscribe(baseObserver);
        } else {
            observe(mLoginService.requestJuniorValidate(map)).subscribe(baseObserver);
        }

    }

    public void requestPhoneValidate(String areaCode, String mobile, BaseObserver<RegisterResultBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(LoginConstants1.Parameter.AREACODE, areaCode);
        map.put(LoginConstants1.Parameter.MOBILE, mobile);
        observe(mLoginService.requestPhoneValidate(map)).subscribe(baseObserver);
    }

    /**
     * 请求注册接口
     */
    public void requestRegistration(String userId, RegistrationBean registrationBean, ConditionBean conditionBean, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(LoginConstants.Parameter.USERNAME, registrationBean.getUserName());
        map.put(LoginConstants.Parameter.PASSWORD, registrationBean.getPassword());
        map.put(LoginConstants.Parameter.REALNAME, registrationBean.getFullName());
        map.put(LoginConstants.Parameter.IDCARD, registrationBean.getNric());
        map.put(LoginConstants.Parameter.MOBILE, registrationBean.getMobile());
        map.put(LoginConstants.Parameter.SMSCODE, registrationBean.getCode());
        map.put(LoginConstants.Parameter.MOBILEAREACODE, registrationBean.getMobileAreaCode());
        map.put(LoginConstants.Parameter.USERID, userId);
        map.put(LoginConstants.Parameter.CONDITIONVERSIONNO, conditionBean.getConditionVersionNo());
        map.put(LoginConstants.Parameter.CONDITIONTITLE, conditionBean.getConditionTitle());
        map.put(LoginConstants.Parameter.CONDITIONTYPE, "01");
        observe(mLoginService.requestRegistration(map)).subscribe(baseObserver);
    }

    /**
     * 请求注册接口
     */
    public void requestRegistration1(RegisterInfoBean bean, ConditionBean conditionBean, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(LoginConstants1.Parameter.AREACODE, bean.getAreaCode());
        map.put(LoginConstants.Parameter.MOBILE, bean.getMobile());
        map.put(LoginConstants1.Parameter.FIRSTNAME, bean.getFirstName());
        if (!TextUtils.isEmpty(bean.getMiddleName())) {
            map.put(LoginConstants1.Parameter.MIDDLENAME, bean.getMiddleName());
        }
        map.put(LoginConstants1.Parameter.LASTNAME, bean.getLastName());
        map.put(LoginConstants1.Parameter.GENDER, bean.getGender());
        map.put(LoginConstants1.Parameter.BIRTHDAY, bean.getDateOfBirth());
        map.put(LoginConstants1.Parameter.IDTYPE, bean.getIdType());
        map.put(LoginConstants1.Parameter.IDNUMBER, bean.getIdNumber());
        map.put(LoginConstants1.Parameter.CITYCODE, bean.getCityCode());
        map.put(LoginConstants1.Parameter.ADDRESS, bean.getAddress());
        map.put(LoginConstants1.Parameter.EMAIL, bean.getEmail());
        map.put(LoginConstants1.Parameter.PASSWORD, bean.getPassword());
        map.put(LoginConstants.Parameter.CONDITIONVERSIONNO, conditionBean.getConditionVersionNo());
        map.put(LoginConstants.Parameter.CONDITIONTITLE, conditionBean.getConditionTitle());
        map.put(LoginConstants.Parameter.CONDITIONTYPE, "01");
        observe(mLoginService.requestRegistration(map)).subscribe(baseObserver);
    }

    public void requestJuniorRegistration(RegisterInfoBean bean, ConditionBean conditionBean, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(LoginConstants1.Parameter.RELATION_SHIP, bean.getRelationShip() + "");
        map.put(LoginConstants1.Parameter.AREACODE, bean.getAreaCode());
        map.put(LoginConstants.Parameter.MOBILE, bean.getMobile());
        map.put(LoginConstants1.Parameter.FIRSTNAME, bean.getFirstName());
        if (!TextUtils.isEmpty(bean.getMiddleName())) {
            map.put(CommonConstants.RegisterParameter.MIDDLENAME, bean.getMiddleName());
        }
        map.put(LoginConstants1.Parameter.LASTNAME, bean.getLastName());
        map.put(LoginConstants1.Parameter.GENDER, bean.getGender());
        map.put(LoginConstants1.Parameter.BIRTHDAY, bean.getDateOfBirth());
        map.put(LoginConstants1.Parameter.IDTYPE, bean.getIdType());
        map.put(LoginConstants1.Parameter.IDNUMBER, bean.getIdNumber());
        map.put(LoginConstants1.Parameter.CITYCODE, bean.getCityCode());
        map.put(LoginConstants1.Parameter.ADDRESS, bean.getAddress());
        map.put(CommonConstants.Parameter.CHECK_TOKEN, bean.getCheckToken());
        map.put(CommonConstants.Parameter.PIN, bean.getPassword());

        map.put(LoginConstants.Parameter.CONDITIONVERSIONNO, conditionBean.getConditionVersionNo());
        map.put(LoginConstants.Parameter.CONDITIONTITLE, conditionBean.getConditionTitle());
        map.put(LoginConstants.Parameter.CONDITIONTYPE, conditionBean.getConditionType());
        observe(mLoginService.requestJuniorRegistration(map)).subscribe(baseObserver);
    }

    /**
     * 忘记用户名
     */
    public void requestForgetName(String userId, String idCard, String mobile, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(LoginConstants.Parameter.IDCARD, idCard);
        map.put(LoginConstants.Parameter.MOBILE, mobile);
        map.put(LoginConstants.Parameter.USERID, userId);
        observe(mLoginService.requestForgetName(map)).subscribe(baseObserver);
    }


    /**
     * 设置密码
     *
     * @param psdOneStr    密码
     * @param userName     用户名
     * @param idCard       卡号
     * @param baseObserver 回调
     */
    public void setUpLoginPsd(String userId, String psdOneStr, String userName, String idCard, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(LoginConstants.Parameter.IDCARD, idCard);
        map.put(LoginConstants.Parameter.PASSWORD, psdOneStr);
        map.put(LoginConstants.Parameter.USERNAME, userName);
        map.put(LoginConstants.Parameter.USERID, userId);
        observe(mLoginService.setUpLoginPsd(map)).subscribe(baseObserver);
    }

    /**
     * 设置密码
     *
     * @param psdOneStr    密码
     * @param baseObserver 回调
     */
    public void setInviteFriendsPsd(String userId, String psdOneStr, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(LoginConstants.Parameter.PASSWORD, psdOneStr);
        map.put(LoginConstants.Parameter.USERID, userId);
        observe(mLoginService.setInviteFriendsPsd(map)).subscribe(baseObserver);
    }

    /**
     * 验证验证码接口
     *
     * @param userName     用户名
     * @param password     密码
     * @param authCode     授权码
     * @param baseObserver 回调
     */
    public void requestValidateCode(String userId, String userName, String password, String passwordType, String authCode, String registrationId, String token, BaseObserver<LoginBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        if (TextUtils.equals(passwordType, LoginConstants.LoginType.FingerprintType)) {
            map.put(LoginConstants.Parameter.BIOPWD, password);
        } else if (TextUtils.equals(passwordType, LoginConstants.LoginType.GestureType)) {
            map.put(LoginConstants.Parameter.GESTUREPWD, password);
        } else {
            map.put(LoginConstants.Parameter.PASSWORD, password);
        }
        map.put(LoginConstants.Parameter.PASSWORDTYPE, passwordType);
        map.put(LoginConstants.Parameter.MOBILENO, userName);
        map.put(LoginConstants.Parameter.AUTHCODE, authCode);
        map.put(LoginConstants.Parameter.REGISTRATIONID, registrationId);
        map.put(LoginConstants.Parameter.DEVICETOKEN, token);
        map.put(LoginConstants.Parameter.USERID, userId);
        map.put(LoginConstants.Parameter.MOBILEAREACODE, "263");
        observe(mLoginService.requestValidateCode(map)).subscribe(baseObserver);
    }

    /**
     * 获取最新的协议
     */
    public void getLatestCondition(String type,BaseObserver<ConditionBean> baseObserver) {
        CommonModel.getLatestCondition(type, baseObserver);
    }

    /**
     * 同意协议
     */
    public void agreeCondition(String mobile, String title, String versionNo, BaseObserver<Object> baseObserver) {
        CommonModel.agreeCondition(mobile, title, versionNo, baseObserver);
    }

    /**
     * 获取区域信息
     *
     * @param baseObserver 回调
     */
    public void requestDictCodeList(BaseNoDialogObserver<AppDictListBean> baseObserver) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        if (TextUtils.isEmpty(pubLick)) {
            commonPub(new BaseNoDialogObserver<PubBean>(mContext) {
                @Override
                public void requestFail(String sResMsg) {
                    ToastUtil.show(mContext, sResMsg);
                }

                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    executeRequestDictCodeList(baseObserver);
                }
            });
        } else {
            executeRequestDictCodeList(baseObserver);
        }
    }

    public void executeRequestDictCodeList(BaseNoDialogObserver<AppDictListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
//        map.put("dictCode", "currency_type");
        map.put("dictCode", "register_address");
        observe(mLoginService.requestDictCodeList(map)).subscribe(baseObserver);
    }

    public void requestCheckUpGradeInfo(UpgradeInfoBean bean, BaseObserver<CheckAccountResultBean> baseObserver) {
        observe(mLoginService.requestCheckUpGradeInfo(bean)).subscribe(baseObserver);
    }


    /**
     * 获取验证码接口
     */
    public void requestCode(String mobile, String messageTemplateType, String mobileAreaCode, BaseObserver<CodeBean> baseObserver) {
        new om.rrtx.mobile.functioncommon.model.CommonModel().requestCode(mobile, messageTemplateType, mobileAreaCode, baseObserver);
    }


}
