<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="om.rrtx.mobile.loginmodule">

    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <application>
        <activity
            android:name=".activity.UpdateConditionActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateVisible" />
        <activity
            android:name=".activity.SplashActivity"
            android:exported="true"
            android:launchMode="singleTask"
            android:screenOrientation="portrait"
            android:theme="@style/login_welcomeTheme"
            android:windowSoftInputMode="adjustPan|stateHidden">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
            <intent-filter>
                <action android:name="om.rrtx.mobile.loginmodul.SplashActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />

                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />

                <data android:scheme="mfswallet" />
            </intent-filter>
        </activity> <!-- 登录页面 -->
        <activity
            android:name=".activity.LoginActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:theme="@style/MaterialTheme"
            android:windowSoftInputMode="adjustResize|stateHidden">
            <intent-filter>
                <action android:name="om.rrtx.mobile.loginmodule.LoginActivity" />

                <category android:name="android.intent.category.DEFAULT" />
            </intent-filter>
        </activity> <!-- 上面的adjustResize可以让键盘不影响屏幕尺寸 -->

        <activity
            android:name=".activity.NewRegistrationActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateVisible" />
        <activity
            android:name=".activity.NewUserInfoActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateVisible" />
        <activity
            android:name=".activity.ReguarUserInfoActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateVisible" />
        <activity
            android:name=".activity.ReguarPhoneInfoActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateVisible" />
        <activity
            android:name=".activity.RegisterResultActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateVisible" />

        <activity
            android:name=".activity.LoginSuccessActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.SetLoginPsdActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateVisible" />
        <activity
            android:name=".activity.TemporaryActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateVisible" />
        <activity
            android:name=".activity.ForgetPinActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateVisible" />
        <activity
            android:name=".activity.ResetPinActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateVisible" />

        <activity
            android:name="om.rrtx.mobile.loginmodule.activity.UpgradeAccountActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />

    </application>

</manifest>