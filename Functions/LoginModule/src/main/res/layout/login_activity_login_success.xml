<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.LoginSuccessActivity">

    <include
        android:id="@+id/include_title"
        layout="@layout/login_base_title" />

    <ImageView
        android:id="@+id/icon"
        android:layout_width="88pt"
        android:layout_height="88pt"
        android:layout_marginTop="120pt"
        android:contentDescription="@string/common_app_name"
        android:src="@drawable/login_ic_success"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_title" />

    <TextView
        android:id="@+id/success"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="60pt"
        android:text="@string/success"
        android:textColor="@color/color_17904B"
        android:textSize="36pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/icon" />

    <TextView
        android:id="@+id/hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20pt"
        android:text="@string/register_label_registration_tip"
        android:textColor="@color/color_131313"
        android:textSize="32pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/success" />

    <TextView
        android:id="@+id/doneTv"
        android:layout_width="690pt"
        android:layout_height="80pt"
        android:layout_marginTop="60pt"
        android:background="@drawable/login_drawable_btn_select"
        android:gravity="center"
        android:text="@string/complete"
        android:textColor="@color/color_FFFFFF"
        android:textSize="34pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/hint" />
</androidx.constraintlayout.widget.ConstraintLayout>