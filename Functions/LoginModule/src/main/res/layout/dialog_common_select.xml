<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="584pt"
    android:paddingLeft="32pt"
    android:paddingRight="32pt"
    android:background="@drawable/bg_date_select_dialog"
    >

    <TextView
        android:id="@+id/tv_cancel"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/common_alert_cancel"
        android:textColor="@color/color_000000"
        android:textSize="28pt"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="32pt"
        android:text="@string/location"
        android:textColor="@color/color_000000"
        android:textSize="36pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv_confirm"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/common_btn_confirm"
        android:textColor="@color/common_ye_F3881E"
        android:textSize="28pt"
        app:layout_constraintBottom_toBottomOf="@id/tv_title"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/tv_title" />

    <com.cncoderx.wheelview.WheelView
        android:id="@+id/mv_common"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:lineSpace="104pt"
        app:selectedColor="@color/color_131313"
        app:textSize="40pt"
        app:unselectedColor="@color/color_707070"
        app:visibleItems="3"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        />

</androidx.constraintlayout.widget.ConstraintLayout>