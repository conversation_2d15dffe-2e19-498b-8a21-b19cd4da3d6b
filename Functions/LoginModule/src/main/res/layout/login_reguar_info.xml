<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="om.rrtx.mobile.loginmodule.R" />

        <import type="om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="ResourceName">

        <include
            android:id="@+id/include_title"
            layout="@layout/login_base_title" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/middle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/include_title">

            <View
                android:id="@+id/view_lin"
                android:layout_width="match_parent"
                android:layout_height="1pt"
                android:background="@color/color_E5E6EB"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/view_tab"
                android:layout_width="match_parent"
                android:layout_height="77pt"
                android:background="@color/color_FFFFFF"
                app:layout_constraintTop_toBottomOf="@id/view_lin" />

            <ImageView
                android:id="@+id/cv_dot"
                android:layout_width="10pt"
                android:layout_height="10pt"
                android:layout_marginLeft="30pt"
                android:background="@drawable/ic_dot"
                android:gravity="center"
                android:textColor="@color/common_text_1d2129"
                android:textSize="100pt"
                app:layout_constraintBottom_toBottomOf="@id/view_tab"
                app:layout_constraintLeft_toLeftOf="@id/view_tab"
                app:layout_constraintTop_toTopOf="@id/view_tab" />

            <TextView
                android:id="@+id/tv_cur_step"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="13pt"
                android:paddingRight="100pt"
                android:textColor="@color/common_text_1d2129"
                android:textSize="30pt"
                app:layout_constraintBottom_toBottomOf="@id/view_tab"
                app:layout_constraintLeft_toRightOf="@id/cv_dot"
                app:layout_constraintTop_toTopOf="@id/view_tab" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="@id/view_tab"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/view_tab">

                <TextView
                    android:id="@+id/tv_cur_page"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:textColor="@color/common_ye_F3881E"
                    android:textSize="40pt" />

                <TextView
                    android:id="@+id/tv_all_step"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginRight="30pt"
                    android:text="/6"
                    android:textColor="@color/common_ye_F3881E"
                    android:textSize="28pt" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottom"
            android:layout_width="match_parent"
            android:layout_height="255pt"
            android:layout_alignParentBottom="true"
            android:background="@color/color_FFFFFF"
            android:paddingLeft="35pt"
            android:paddingRight="35pt">

            <TextView
                android:id="@+id/tv_next"
                android:layout_width="match_parent"
                android:layout_height="80pt"
                android:background="@drawable/common_unusable_btn"
                android:enabled="false"
                android:gravity="center"
                android:text="@string/next"
                android:textColor="@color/color_FFFFFF"
                android:textSize="32pt"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_above="@id/bottom"
            android:layout_below="@id/middle"
            android:layout_gravity="fill_vertical"
            android:scrollbars="none"
            tools:ignore="ScrollViewCount">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/common_bg_f7f8fa"
                android:orientation="vertical"
                android:paddingBottom="64pt">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="30pt"
                    android:paddingRight="30pt">

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/conHint"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:id="@+id/hint_tv"
                            cornerBackgroundColor="@{R.color.color_FFF7E8}"
                            cornerBackgroundRadius="@{8}"
                            android:layout_width="match_parent"
                            android:layout_height="64pt"
                            android:layout_marginTop="24pt"
                            android:gravity="left|center"
                            android:paddingLeft="24pt"
                            android:paddingRight="24pt"
                            android:textColor="@color/common_ye_F3881E"
                            android:textSize="24pt"
                            app:layout_constraintTop_toTopOf="parent" />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/first_name_TIL"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="21pt"
                        android:hint="@string/first_name"
                        app:hintAnimationEnabled="true"
                        app:hintEnabled="true"
                        app:hintTextAppearance="@style/register_hintAppearance"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/conHint">

                        <com.google.android.material.textfield.TextInputEditText
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@null"
                            android:digits="qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM"
                            android:maxLength="50"
                            android:paddingTop="15pt"
                            android:theme="@style/register_inputEditText_Appearance" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1pt"
                        android:layout_marginTop="10pt"
                        android:background="@color/color_C9CDD4"
                        app:layout_constraintTop_toBottomOf="@id/first_name_TIL" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/middle_name_TIL"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="42pt"
                        android:hint="@string/middle_name"
                        app:hintAnimationEnabled="true"
                        app:hintEnabled="true"
                        app:hintTextAppearance="@style/register_hintAppearance"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/first_name_TIL">

                        <com.google.android.material.textfield.TextInputEditText
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@null"
                            android:digits="qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM"
                            android:maxLength="50"
                            android:paddingTop="15pt"
                            android:theme="@style/register_inputEditText_Appearance" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1pt"
                        android:layout_marginTop="10pt"
                        android:background="@color/color_C9CDD4"
                        app:layout_constraintTop_toBottomOf="@id/middle_name_TIL" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/last_name_TIL"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="42pt"
                        android:hint="@string/last_name"
                        app:hintAnimationEnabled="true"
                        app:hintEnabled="true"
                        app:hintTextAppearance="@style/register_hintAppearance"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/middle_name_TIL">

                        <com.google.android.material.textfield.TextInputEditText
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@null"
                            android:digits="qwertyuiopasdfghjklzxcvbnmQWERTYUIOPASDFGHJKLZXCVBNM"
                            android:maxLength="50"
                            android:paddingTop="15pt"
                            android:theme="@style/register_inputEditText_Appearance" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1pt"
                        android:layout_marginTop="10pt"
                        android:background="@color/color_C9CDD4"
                        app:layout_constraintTop_toBottomOf="@id/last_name_TIL" />

                    <TextView
                        android:id="@+id/tv_gender"
                        android:layout_width="98pt"
                        android:layout_height="33pt"
                        android:layout_marginTop="40pt"
                        android:text="@string/gender"
                        android:textColor="@color/common_text_86909C"
                        android:textSize="28pt"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/last_name_TIL" />

                    <TextView
                        android:id="@+id/tv_male"
                        android:layout_width="0pt"
                        android:layout_height="80pt"
                        android:layout_marginTop="24pt"
                        android:layout_marginRight="24pt"
                        android:background="@drawable/shape_gender_unselect"
                        android:gravity="center"
                        android:text="@string/male"
                        android:textColor="@color/common_text_4E5969"
                        android:textSize="32pt"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@id/tv_female"
                        app:layout_constraintTop_toBottomOf="@id/tv_gender" />

                    <TextView
                        android:id="@+id/tv_female"
                        android:layout_width="0pt"
                        android:layout_height="80pt"
                        android:background="@drawable/shape_gender_unselect"
                        android:gravity="center"
                        android:text="@string/female"
                        android:textColor="@color/common_text_4E5969"
                        android:textSize="32pt"
                        app:layout_constraintLeft_toRightOf="@id/tv_male"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="@id/tv_male" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/birthday_TIL"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="42pt"
                        android:focusable="false"
                        android:focusableInTouchMode="false"
                        android:hint="@string/date_of_birth"
                        app:hintAnimationEnabled="true"
                        app:hintEnabled="true"
                        app:hintTextAppearance="@style/register_hintAppearance"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_male">

                        <com.google.android.material.textfield.TextInputEditText
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@null"
                            android:focusable="false"
                            android:focusableInTouchMode="false"
                            android:paddingTop="15pt"
                            android:theme="@style/register_inputEditText_Appearance" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1pt"
                        android:layout_marginTop="10pt"
                        android:background="@color/color_C9CDD4"
                        app:layout_constraintTop_toBottomOf="@id/birthday_TIL" />

                    <ImageView
                        android:layout_width="40pt"
                        android:layout_height="40pt"
                        android:src="@drawable/calendar"
                        app:layout_constraintBottom_toBottomOf="@+id/birthday_TIL"
                        app:layout_constraintRight_toRightOf="@+id/birthday_TIL" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/id_type_TIL"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="42pt"
                        android:hint="@string/id_type"
                        app:hintAnimationEnabled="true"
                        app:hintEnabled="true"
                        app:hintTextAppearance="@style/register_hintAppearance"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/birthday_TIL">

                        <com.google.android.material.textfield.TextInputEditText
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@null"
                            android:focusable="false"
                            android:focusableInTouchMode="false"
                            android:paddingTop="15pt"
                            android:theme="@style/register_inputEditText_Appearance" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1pt"
                        android:layout_marginTop="10pt"
                        android:background="@color/color_C9CDD4"
                        app:layout_constraintTop_toBottomOf="@id/id_type_TIL" />

                    <ImageView
                        android:layout_width="24pt"
                        android:layout_height="24pt"
                        android:src="@drawable/ic_sanjiao"
                        app:layout_constraintBottom_toBottomOf="@+id/id_type_TIL"
                        app:layout_constraintRight_toRightOf="@+id/id_type_TIL" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/id_number_TIL"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="42pt"
                        android:hint="@string/id_number"
                        app:hintAnimationEnabled="true"
                        app:hintEnabled="true"
                        app:hintTextAppearance="@style/register_hintAppearance"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/id_type_TIL">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/idNumber_ed"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@null"
                            android:digits="@string/number_AZ"
                            android:maxLength="20"
                            android:paddingTop="15pt"
                            android:theme="@style/register_inputEditText_Appearance" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1pt"
                        android:layout_marginTop="10pt"
                        android:background="@color/color_C9CDD4"
                        app:layout_constraintTop_toBottomOf="@id/id_number_TIL" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/area_TIL"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="42pt"
                        android:hint="@string/location"
                        app:hintAnimationEnabled="true"
                        app:hintEnabled="true"
                        app:hintTextAppearance="@style/register_hintAppearance"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/id_number_TIL">

                        <com.google.android.material.textfield.TextInputEditText
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@null"
                            android:focusable="false"
                            android:focusableInTouchMode="false"
                            android:paddingTop="15pt"
                            android:theme="@style/register_inputEditText_Appearance" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1pt"
                        android:layout_marginTop="10pt"
                        android:background="@color/color_C9CDD4"
                        app:layout_constraintTop_toBottomOf="@id/area_TIL" />

                    <ImageView
                        android:layout_width="24pt"
                        android:layout_height="24pt"
                        android:src="@drawable/ic_sanjiao"
                        app:layout_constraintBottom_toBottomOf="@+id/area_TIL"
                        app:layout_constraintRight_toRightOf="@+id/area_TIL" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/address_TIL"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="42pt"
                        android:hint="@string/address"
                        app:hintAnimationEnabled="true"
                        app:hintEnabled="true"
                        app:hintTextAppearance="@style/register_hintAppearance"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/area_TIL">

                        <com.google.android.material.textfield.TextInputEditText
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@null"
                            android:digits="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ "
                            android:maxLength="100"
                            android:paddingTop="15pt"
                            android:theme="@style/register_inputEditText_Appearance" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1pt"
                        android:layout_marginTop="10pt"
                        android:background="@color/color_C9CDD4"
                        app:layout_constraintTop_toBottomOf="@id/address_TIL" />

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/email_TIL"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="42pt"
                        android:hint="@string/optiona_email"
                        app:hintAnimationEnabled="true"
                        app:hintEnabled="true"
                        app:hintTextAppearance="@style/register_hintAppearance"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/address_TIL">

                        <com.google.android.material.textfield.TextInputEditText
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:background="@null"
                            android:maxLength="50"
                            android:paddingTop="15pt"
                            android:theme="@style/register_inputEditText_Appearance" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="1pt"
                        android:layout_marginTop="10pt"
                        android:background="@color/color_C9CDD4"
                        app:layout_constraintTop_toBottomOf="@id/email_TIL" />

                    <TextView
                        android:id="@+id/email_error"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/email_error"
                        android:textColor="@color/color_DE0000"
                        android:textSize="20pt"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/email_TIL" />

                   <TextView
                       android:id="@+id/tv_face_hint"
                       app:layout_constraintTop_toBottomOf="@id/email_TIL"
                       android:text="Customer Facial Photos"
                       android:layout_marginTop="42pt"
                       android:textSize="28pt"
                       android:textColor="@color/common_text_1d2129"
                       android:layout_width="match_parent"
                       android:layout_height="wrap_content"/>

                    <LinearLayout
                        android:id="@+id/lin_face"
                        android:layout_marginTop="24pt"
                        app:layout_constraintTop_toBottomOf="@id/tv_face_hint"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/face_photos"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal" />
                    </LinearLayout>

                    <TextView
                        android:id="@+id/tv_id_hint"
                        app:layout_constraintTop_toBottomOf="@id/lin_face"
                        android:text="Customer ID Photos"
                        android:layout_marginTop="42pt"
                        android:textSize="28pt"
                        android:textColor="@color/common_text_1d2129"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"/>

                    <LinearLayout
                        android:layout_marginTop="24pt"
                        app:layout_constraintTop_toBottomOf="@id/tv_id_hint"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">
                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/rv_photos"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal" />
                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </RelativeLayout>
</layout>