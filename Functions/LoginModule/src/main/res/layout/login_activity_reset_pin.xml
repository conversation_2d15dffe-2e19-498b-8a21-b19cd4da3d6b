<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        tools:ignore="MissingDefaultResource">

        <include
            android:id="@+id/include_title"
            layout="@layout/login_base_title" />

        <RelativeLayout
            android:id="@+id/rel"
            android:layout_width="match_parent"
            android:layout_height="94pt"
            android:background="@color/color_FEF3E8"
            android:paddingLeft="30pt"
            android:paddingRight="30pt">

            <ImageView
                android:id="@+id/iv_tip"
                android:layout_width="28pt"
                android:layout_height="28pt"
                android:layout_marginTop="21pt"
                android:src="@drawable/reset_tip" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16pt"
                android:layout_marginLeft="15pt"
                android:layout_toRightOf="@id/iv_tip"
                android:text="@string/reset_hint"
                android:textColor="@color/common_ye_F3881E"
                android:textSize="26pt" />

        </RelativeLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="0pt"
            android:layout_weight="1"
            android:background="@color/common_bg_f7f8fa"
            android:paddingLeft="30pt"
            android:paddingRight="30pt">

            <include
                android:id="@+id/mobile_include"
                layout="@layout/com_mobile_layout"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/idTitle_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="32pt"
                android:text="@string/id_number"
                android:textColor="@color/common_text_1d2129"
                android:textSize="28pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/mobile_include" />

            <EditText
                android:id="@+id/id_ed"
                cornerBackgroundRadius="@{10}"
                android:layout_width="match_parent"
                android:layout_height="100pt"
                android:layout_marginTop="16pt"
                android:digits="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
                android:gravity="left|center_vertical"
                android:hint="@string/id_number"
                android:inputType="number"
                android:maxLength="20"
                android:paddingLeft="24pt"
                android:textColor="@color/common_text_1d2129"
                android:textCursorDrawable="@drawable/common_cursor"
                android:textSize="28pt"
                app:layout_constraintTop_toBottomOf="@id/idTitle_tv" />

            <TextView
                android:id="@+id/next_tv"
                android:layout_width="match_parent"
                android:layout_height="80pt"
                android:layout_marginTop="64pt"
                android:background="@drawable/common_btn_bg"
                android:enabled="false"
                android:gravity="center"
                android:text="@string/submit"
                android:textColor="@color/common_text_FFFFFF"
                android:textSize="32pt"
                app:layout_constraintTop_toBottomOf="@id/id_ed" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>
</layout>