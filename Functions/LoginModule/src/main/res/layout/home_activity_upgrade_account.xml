<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="om.rrtx.mobile.loginmodule.R" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_bg_f7f8fa">

        <include
            android:id="@+id/include_title"
            layout="@layout/login_base_title" />

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/update_Information"
            android:textColor="@color/common_text_1d2129"
            android:textSize="28pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/include_title"
            android:layout_marginTop="24pt"
            android:layout_marginLeft="30pt"
            />

        <TextView
            android:id="@+id/hint_tv"
            cornerBackgroundColor="@{R.color.color_FFF7E8}"
            cornerBackgroundRadius="@{8}"
            android:layout_width="match_parent"
            android:layout_height="64pt"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:paddingLeft="24pt"
            android:paddingRight="24pt"
            android:textSize="24pt"
            android:textColor="@color/common_ye_F3881E"
            android:layout_marginTop="16pt"
            android:gravity="center_vertical"
            app:layout_constraintTop_toBottomOf="@id/title_tv" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/info_rv"
            android:layout_width="match_parent"
            android:layout_height="0pt"
            app:layout_constraintTop_toBottomOf="@id/hint_tv"
            app:layout_constraintBottom_toTopOf="@id/bottomBg_cl"
            android:layout_marginTop="16pt"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            />
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottomBg_cl"
            android:layout_width="match_parent"
            android:layout_height="265pt"
            android:layout_marginTop="64pt"
            android:background="@color/color_FFFFFF"
            android:paddingLeft="35pt"
            android:paddingRight="35pt"
            app:layout_constraintTop_toBottomOf="@id/info_rv"
            app:layout_constraintBottom_toBottomOf="parent"
            >

            <CheckBox
                android:id="@+id/cb_ok"
                android:layout_width="34pt"
                android:layout_height="34pt"
                android:layout_marginTop="25pt"
                android:background="@drawable/com_checkbox_bg"
                android:button="@null"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/tv_read"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="17pt"
                android:text="@string/read_and_accept"
                android:textColor="@color/common_text_86909C"
                android:textSize="28pt"
                app:layout_constraintBottom_toBottomOf="@id/cb_ok"
                app:layout_constraintLeft_toRightOf="@id/cb_ok"
                app:layout_constraintTop_toTopOf="@id/cb_ok" />

            <TextView
                android:id="@+id/tv_condition"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="17pt"
                android:text="@string/terms_and_conditions"
                android:textColor="@color/common_ye_F3881E"
                android:textSize="28pt"
                app:layout_constraintBottom_toBottomOf="@id/cb_ok"
                app:layout_constraintLeft_toRightOf="@id/tv_read"
                app:layout_constraintTop_toTopOf="@id/cb_ok" />

            <TextView
                android:id="@+id/next_tv"
                android:layout_width="match_parent"
                android:layout_height="80pt"
                android:layout_marginTop="25pt"
                android:layout_marginBottom="24pt"
                android:background="@drawable/common_btn_bg"
                android:enabled="false"
                android:gravity="center"
                android:text="@string/next"
                android:textColor="@color/color_FFFFFF"
                android:textSize="32pt"
                app:layout_constraintTop_toBottomOf="@id/cb_ok" />


        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>