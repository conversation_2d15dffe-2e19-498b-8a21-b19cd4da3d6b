<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="om.rrtx.mobile.loginmodule.R" />
        <import type="om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="ResourceName">

        <include
            android:id="@+id/include_title"
            layout="@layout/login_base_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view_lin"
            android:layout_width="match_parent"
            android:layout_height="1pt"
            android:background="@color/color_E5E6EB"
            app:layout_constraintTop_toBottomOf="@id/include_title" />

        <View
            android:id="@+id/view_tab"
            android:layout_width="match_parent"
            android:layout_height="77pt"
            android:background="@color/color_FFFFFF"
            app:layout_constraintTop_toBottomOf="@id/view_lin" />

        <ImageView
            android:id="@+id/cv_dot"
            android:layout_width="10pt"
            android:layout_height="10pt"
            android:gravity="center"
            android:textSize="100pt"
            android:textColor="@color/common_text_1d2129"
            android:layout_marginLeft="30pt"
            android:background="@drawable/ic_dot"
            app:layout_constraintBottom_toBottomOf="@id/view_tab"
            app:layout_constraintLeft_toLeftOf="@id/view_tab"
            app:layout_constraintTop_toTopOf="@id/view_tab" />

        <TextView
            android:id="@+id/tv_cur_step"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="13pt"
            android:textSize="30pt"
            android:textColor="@color/common_text_1d2129"
            app:layout_constraintBottom_toBottomOf="@id/view_tab"
            app:layout_constraintLeft_toRightOf="@id/cv_dot"
            app:layout_constraintTop_toTopOf="@id/view_tab" />

        <TextView
            android:id="@+id/tv_cur_page"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="40pt"
            android:textColor="@color/common_ye_F3881E"
            app:layout_constraintBottom_toBottomOf="@id/tv_all_step"
            app:layout_constraintRight_toLeftOf="@id/tv_all_step" />

        <TextView
            android:id="@+id/tv_all_step"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="30pt"
            android:text="/6"
            android:textSize="28pt"
            android:textColor="@color/common_ye_F3881E"
            app:layout_constraintBottom_toBottomOf="@id/view_tab"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/view_tab" />

        <FrameLayout
            android:id="@+id/fl_registration"
            android:layout_width="match_parent"
            android:layout_height="0pt"
            app:layout_constraintTop_toBottomOf="@id/view_tab"
            app:layout_constraintBottom_toBottomOf="parent"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>