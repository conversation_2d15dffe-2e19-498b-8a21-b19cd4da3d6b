<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper" />

        <import type="om.rrtx.mobile.loginmodule.R" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_bg_f7f8fa"
        android:paddingLeft="30pt"
        android:paddingRight="30pt">

        <View
            android:id="@+id/bg_lan0"
            cornerBackgroundRadius="@{4}"
            android:layout_width="match_parent"
            android:layout_height="88pt"
            android:layout_marginTop="25pt"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="24pt"
            android:text="@string/english"
            android:textColor="@color/common_text_1d2129"
            android:textSize="30pt"
            app:layout_constraintBottom_toBottomOf="@id/bg_lan0"
            app:layout_constraintLeft_toLeftOf="@id/bg_lan0"
            app:layout_constraintTop_toTopOf="@id/bg_lan0" />

        <View
            android:id="@+id/iv_english"
            android:layout_width="38pt"
            android:layout_height="26pt"
            android:layout_marginRight="24pt"
            android:background="@drawable/ic_ok"
            app:layout_constraintBottom_toBottomOf="@id/bg_lan0"
            app:layout_constraintRight_toRightOf="@id/bg_lan0"
            app:layout_constraintTop_toTopOf="@id/bg_lan0" />


        <View
            android:id="@+id/bg_lan1"
            cornerBackgroundRadius="@{4}"
            android:layout_width="match_parent"
            android:layout_height="88pt"
            android:layout_marginTop="25pt"
            app:layout_constraintTop_toBottomOf="@id/bg_lan0" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="24pt"
            android:text="@string/shona"
            android:textColor="@color/common_text_1d2129"
            android:textSize="30pt"
            app:layout_constraintBottom_toBottomOf="@id/bg_lan1"
            app:layout_constraintLeft_toLeftOf="@id/bg_lan1"
            app:layout_constraintTop_toTopOf="@id/bg_lan1" />

        <View
            android:id="@+id/iv_shona"
            android:layout_width="38pt"
            android:layout_height="26pt"
            android:layout_marginRight="24pt"
            android:background="@drawable/ic_ok"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/bg_lan1"
            app:layout_constraintRight_toRightOf="@id/bg_lan1"
            app:layout_constraintTop_toTopOf="@id/bg_lan1" />


        <View
            android:id="@+id/bg_lan2"
            cornerBackgroundRadius="@{4}"
            android:layout_width="match_parent"
            android:layout_height="88pt"
            android:layout_marginTop="25pt"
            app:layout_constraintTop_toBottomOf="@id/bg_lan1" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="24pt"
            android:text="@string/ndebele"
            android:textColor="@color/common_text_1d2129"
            android:textSize="30pt"
            app:layout_constraintBottom_toBottomOf="@id/bg_lan2"
            app:layout_constraintLeft_toLeftOf="@id/bg_lan2"
            app:layout_constraintTop_toTopOf="@id/bg_lan2" />

        <View
            android:id="@+id/iv_ndebele"
            android:layout_width="38pt"
            android:layout_height="26pt"
            android:layout_marginRight="24pt"
            android:background="@drawable/ic_ok"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/bg_lan2"
            app:layout_constraintRight_toRightOf="@id/bg_lan2"
            app:layout_constraintTop_toTopOf="@id/bg_lan2" />

        <TextView
            android:id="@+id/tv_next"
            android:layout_width="match_parent"
            android:layout_height="88pt"
            android:layout_marginTop="24pt"
            android:background="@drawable/common_usable_btn"
            android:gravity="center"
            android:text="@string/next"
            android:textColor="@color/color_FFFFFF"
            android:textSize="32pt"
            app:layout_constraintTop_toBottomOf="@id/bg_lan2" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>