<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="210pt"
    android:layout_height="150pt"
    android:layout_marginTop="24pt">

    <RelativeLayout
        android:id="@+id/rel_one"
        android:layout_width="210pt"
        android:layout_height="150pt"
        android:visibility="gone">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/photo_hint" />

        <ImageView
            android:id="@+id/iv_hint"
            android:layout_width="56pt"
            android:layout_height="56pt"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:src="@drawable/photo_add" />

        <TextView
            android:id="@+id/tv_num"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/iv_hint"
            android:layout_centerHorizontal="true"
            android:layout_centerVertical="true"
            android:textColor="@color/common_text_86909C"
            android:textSize="26pt" />
    </RelativeLayout>
    <!-- ImageView 用于显示图片或添加图标 -->
    <ImageView
        android:id="@+id/iv_photo"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <!-- 删除按钮，仅在非添加图标时显示 -->
    <ImageButton
        android:id="@+id/ib_delete"
        android:layout_width="40pt"
        android:layout_height="40pt"
        android:layout_gravity="top|end"
        android:contentDescription="@string/delete"
        android:src="@drawable/photo_delete" />
</FrameLayout>
