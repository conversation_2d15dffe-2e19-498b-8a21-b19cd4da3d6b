<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="baseClick"
            type="om.rrtx.mobile.loginmodule.utils.BaseClick" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <View
            android:id="@+id/statusView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/color_FFFFFF"
            app:layout_constraintBottom_toTopOf="@id/titleTv"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/titleTv"
            android:layout_width="match_parent"
            android:layout_height="90pt"
            android:gravity="center"
            android:textColor="@color/color_FFFFFF"
            android:textSize="36pt"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/statusView"
            tools:text="Certification" />

        <View
            android:id="@+id/leftBg"
            android:layout_width="90pt"
            android:layout_height="90pt"
            android:onClick="@{()->baseClick.leftClick()}"
            app:layout_constraintBottom_toBottomOf="@id/titleTv"
            app:layout_constraintLeft_toLeftOf="@id/titleTv"
            app:layout_constraintTop_toTopOf="@id/titleTv" />

        <ImageView
            android:id="@+id/backIv"
            android:layout_width="48pt"
            android:layout_height="48pt"
            android:contentDescription="@string/base_app_name"
            app:layout_constraintBottom_toBottomOf="@id/leftBg"
            app:layout_constraintLeft_toLeftOf="@id/leftBg"
            app:layout_constraintRight_toRightOf="@id/leftBg"
            app:layout_constraintTop_toTopOf="@id/leftBg" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>