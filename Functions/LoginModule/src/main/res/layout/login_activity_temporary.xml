<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="temporaryOpt"
            type="om.rrtx.mobile.loginmodule.activity.TemporaryActivity.TemporaryOpt" />
    </data>


    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".activity.TemporaryActivity">

        <include
            android:id="@+id/include_title"
            layout="@layout/login_jet_base_title"
            app:baseClick="@{temporaryOpt}" />

        <TextView
            android:id="@+id/hintTv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginTop="40pt"
            android:layout_marginRight="30pt"
            android:gravity="center"
            android:textColor="@color/color_131313"
            android:textSize="28pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/include_title"
            tools:text="The system has detected that you have bound other devices. Please log in through the master device to obtain the authorization code." />

        <TextView
            android:id="@+id/hint2Tv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginTop="40pt"
            android:layout_marginRight="30pt"
            android:gravity="center"
            android:textColor="@color/color_9B9B9B"
            android:textSize="28pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/hintTv"
            tools:text="Please enter authorization code" />

        <om.rrtx.mobile.rrtxcommon1.widget.VerificationCodeEditText
            android:id="@+id/code"
            android:layout_width="match_parent"
            android:layout_height="100pt"
            android:layout_marginLeft="60pt"
            android:layout_marginTop="60pt"
            android:layout_marginRight="60pt"
            android:inputType="number"
            android:textSize="48pt"
            app:bottomLineHeight="2pt"
            app:bottomLineNormalColor="@color/color_C9CDD4"
            app:bottomLineSelectedColor="@color/color_C9CDD4"
            app:cursorColor="@android:color/transparent"
            app:figures="6"
            app:layout_constraintTop_toBottomOf="@id/hint2Tv"
            app:selectedBackgroundColor="@android:color/transparent"
            app:unselectedBackgroundColor="@android:color/transparent"
            app:verCodeMargin="18pt" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
