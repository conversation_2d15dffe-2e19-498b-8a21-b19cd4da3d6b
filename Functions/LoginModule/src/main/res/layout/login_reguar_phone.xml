<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="om.rrtx.mobile.loginmodule.R" />

        <import type="om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper" />
    </data>

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        tools:ignore="ResourceName">

        <include
            android:id="@+id/include_title"
            layout="@layout/login_base_title" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/middle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/include_title">

            <View
                android:id="@+id/view_lin"
                android:layout_width="match_parent"
                android:layout_height="1pt"
                android:background="@color/color_E5E6EB"
                app:layout_constraintTop_toTopOf="parent" />

            <View
                android:id="@+id/view_tab"
                android:layout_width="match_parent"
                android:layout_height="77pt"
                android:background="@color/color_FFFFFF"
                app:layout_constraintTop_toBottomOf="@id/view_lin" />

            <ImageView
                android:id="@+id/cv_dot"
                android:layout_width="10pt"
                android:layout_height="10pt"
                android:layout_marginLeft="30pt"
                android:background="@drawable/ic_dot"
                android:gravity="center"
                android:textColor="@color/common_text_1d2129"
                android:textSize="100pt"
                app:layout_constraintBottom_toBottomOf="@id/view_tab"
                app:layout_constraintLeft_toLeftOf="@id/view_tab"
                app:layout_constraintTop_toTopOf="@id/view_tab" />

            <TextView
                android:id="@+id/tv_cur_step"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="13pt"
                android:paddingRight="100pt"
                android:textColor="@color/common_text_1d2129"
                android:textSize="30pt"
                app:layout_constraintBottom_toBottomOf="@id/view_tab"
                app:layout_constraintLeft_toRightOf="@id/cv_dot"
                app:layout_constraintTop_toTopOf="@id/view_tab" />

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="@id/view_tab"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/view_tab">

                <TextView
                    android:id="@+id/tv_cur_page"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:textColor="@color/common_ye_F3881E"
                    android:textSize="40pt" />

                <TextView
                    android:id="@+id/tv_all_step"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="bottom"
                    android:layout_marginRight="30pt"
                    android:text="/6"
                    android:textColor="@color/common_ye_F3881E"
                    android:textSize="28pt" />

            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/scrollView"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/middle"
            android:layout_gravity="fill_vertical"
            android:scrollbars="none"
            tools:ignore="ScrollViewCount">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/common_bg_f7f8fa"
                android:orientation="vertical"
                android:paddingBottom="64pt">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingLeft="30pt"
                    android:paddingRight="30pt">

                    <RelativeLayout
                        android:id="@+id/rel_mobile"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <com.google.android.material.textfield.TextInputLayout
                            android:id="@+id/mobileTIL"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="42pt"
                            android:fillType="evenOdd"
                            android:hint="@string/common_label_mobile"
                            app:hintAnimationEnabled="true"
                            app:hintEnabled="true"
                            app:hintTextAppearance="@style/register_hintAppearance">

                            <com.google.android.material.textfield.TextInputEditText
                                android:layout_width="match_parent"
                                android:layout_height="match_parent"
                                android:background="@null"
                                android:inputType="number"
                                android:maxLength="9"
                                android:paddingLeft="130pt"
                                android:paddingBottom="24pt"
                                android:theme="@style/register_inputEditText_Appearance" />

                        </com.google.android.material.textfield.TextInputLayout>

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1pt"
                            android:layout_below="@id/mobileTIL"
                            android:layout_marginTop="-15pt"
                            android:background="@color/color_C9CDD4" />

                        <TextView
                            android:id="@+id/mobile_hint"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentBottom="true"
                            android:paddingBottom="74pt"
                            android:text="@string/common_label_mobile"
                            android:textColor="@color/color_86909c"
                            android:textSize="28pt"
                            android:visibility="gone" />

                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_alignParentBottom="true"
                            android:orientation="horizontal"
                            android:paddingBottom="24pt">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="@string/jia_263"
                                android:textColor="@color/common_text_1d2129"
                                android:textSize="32pt" />

                            <View
                                android:layout_width="2pt"
                                android:layout_height="36pt"
                                android:layout_marginLeft="21pt"
                                android:layout_marginRight="21pt"
                                android:background="@color/common_text_C9CDD4" />
                        </LinearLayout>

                    </RelativeLayout>

                    <TextView
                        android:id="@+id/mobile_error"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="@string/mobile_error"
                        android:textColor="@color/color_DE0000"
                        android:textSize="20pt"
                        android:visibility="gone"
                        app:layout_constraintTop_toBottomOf="@id/rel_mobile" />

                </androidx.constraintlayout.widget.ConstraintLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottom"
            android:layout_width="match_parent"
            android:layout_height="255pt"
            android:layout_below="@id/scrollView"
            android:background="@color/color_FFFFFF"
            android:paddingLeft="35pt"
            android:paddingRight="35pt">

            <LinearLayout
                android:id="@+id/lin"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <CheckBox
                    android:id="@+id/cb_ok"
                    android:layout_width="34pt"
                    android:layout_height="34pt"
                    android:layout_marginTop="25pt"
                    android:background="@drawable/register_checkbox_bg"
                    android:button="@null" />

                <TextView
                    android:id="@+id/tv_condition"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="17pt"
                    android:layout_marginTop="22pt"
                    android:text="@string/read_and_accept"
                    android:textColor="@color/common_text_86909C"
                    android:textSize="28pt" />

                <TextView
                    android:id="@+id/tv_condition11"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="17pt"
                    android:layout_marginTop="22pt"
                    android:text="@string/terms_and_conditions"
                    android:textColor="@color/common_ye_F3881E"
                    android:textSize="28pt"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/tv_next"
                android:layout_width="match_parent"
                android:layout_height="80pt"
                android:layout_marginTop="25pt"
                android:layout_marginBottom="64pt"
                android:background="@drawable/common_unusable_btn"
                android:enabled="false"
                android:gravity="center"
                android:text="@string/next"
                android:textColor="@color/color_FFFFFF"
                android:textSize="32pt"
                app:layout_constraintTop_toBottomOf="@id/lin" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </RelativeLayout>
</layout>