<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.SetLoginPsdActivity">

    <include
        android:id="@+id/include_title"
        layout="@layout/login_base_title" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/psdOne"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30pt"
        android:layout_marginTop="43pt"
        android:layout_marginRight="30pt"
        android:hint="@string/common_label_pwd"
        app:layout_constraintTop_toBottomOf="@id/include_title">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/psdOneTie"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPassword"
            android:maxLength="20" />
    </com.google.android.material.textfield.TextInputLayout>

    <ImageView
        android:id="@+id/psdOneIv"
        android:layout_width="84pt"
        android:layout_height="84pt"
        android:contentDescription="@string/base_app_name"
        android:padding="21pt"
        android:src="@drawable/login_input_icon_close"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/psdOne"
        app:layout_constraintRight_toRightOf="@+id/psdOne" />

    <com.google.android.material.textfield.TextInputLayout
        android:id="@+id/psdTwo"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30pt"
        android:layout_marginTop="43pt"
        android:layout_marginRight="30pt"
        android:hint="@string/common_label_confirm_pwd"
        app:layout_constraintTop_toBottomOf="@id/psdOne">

        <com.google.android.material.textfield.TextInputEditText
            android:id="@+id/psdTwoTie"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:inputType="textPassword"
            android:maxLength="20" />
    </com.google.android.material.textfield.TextInputLayout>

    <ImageView
        android:id="@+id/psdTwoIv"
        android:layout_width="84pt"
        android:layout_height="84pt"
        android:contentDescription="@string/base_app_name"
        android:padding="21pt"
        android:src="@drawable/login_input_icon_close"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/psdTwo"
        app:layout_constraintRight_toRightOf="@+id/psdTwo" />


    <TextView
        android:id="@+id/submitTv"
        android:layout_width="690pt"
        android:layout_height="80pt"
        android:layout_marginTop="100pt"
        android:background="@drawable/login_drawable_btn_unselect"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/submit"
        android:textColor="@color/color_FFFFFF"
        android:textSize="34pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/psdTwo" />

    <TextView
        android:layout_width="603pt"
        android:layout_height="wrap_content"
        android:layout_marginTop="20pt"
        android:text="@string/common_label_pwd_tip"
        android:textColor="@color/color_C0C0C0"
        android:textSize="24pt"
        app:layout_constraintLeft_toLeftOf="@id/submitTv"
        app:layout_constraintTop_toBottomOf="@id/submitTv" />


</androidx.constraintlayout.widget.ConstraintLayout>