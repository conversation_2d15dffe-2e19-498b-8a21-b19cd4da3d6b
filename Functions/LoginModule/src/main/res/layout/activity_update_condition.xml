<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.UpdateConditionActivity">
    <include
        android:id="@+id/include_title"
        layout="@layout/login_base_title" />

    <TextView
        android:id="@+id/tip1Tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="30pt"
        android:textColor="@color/color_000000"
        android:textSize="28pt"
        android:text="@string/term_condition_tip1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/include_title" />

    <TextView
        android:id="@+id/tip2Tv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="30pt"
        android:layout_marginTop="24pt"
        android:textColor="@color/color_000000"
        android:textSize="28pt"
        android:text="@string/term_condition_tip2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tip1Tv" />
    <TextView
        android:id="@+id/termTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="30pt"
        android:layout_marginTop="8pt"
        android:textColor="@color/common_ye_F3881E"
        android:textSize="28pt"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tip2Tv" />

    <TextView
        android:id="@+id/tip3Tv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="30pt"
        android:layout_marginEnd="30pt"
        android:layout_marginTop="24pt"
        android:textColor="@color/color_000000"
        android:textSize="28pt"
        android:text="@string/term_condition_tip3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/termTv" />

    <CheckBox
        android:id="@+id/cb"
        android:layout_width="34pt"
        android:layout_height="34pt"
        android:layout_marginLeft="35pt"
        android:layout_marginTop="80pt"
        android:background="@drawable/com_checkbox_bg"
        android:button="@null"
        android:layout_marginBottom="25pt"
        android:layout_marginStart="30pt"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/disAgreeTv" />

    <TextView
        android:id="@+id/checkHintTv1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="28pt"
        android:text="@string/condition_label_accept_content"
        android:textColor="@color/color_717171"
        android:textSize="32pt"
        app:layout_constraintBottom_toBottomOf="@id/cb"
        app:layout_constraintLeft_toRightOf="@id/cb"
        app:layout_constraintTop_toTopOf="@id/cb" />

    <TextView
        android:id="@+id/disAgreeTv"
        android:layout_width="330pt"
        android:layout_height="80pt"
        android:layout_marginStart="30pt"
        android:layout_marginBottom="80pt"
        android:background="@drawable/border_blue_btn_bg"
        android:gravity="center"
        android:text="@string/common_btn_disagree_and_exit"
        android:textColor="@color/common_ye_F3881E"
        android:textSize="32pt"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/agreeTv"
        android:layout_width="330pt"
        android:layout_height="80pt"
        android:layout_marginEnd="30pt"
        android:layout_marginBottom="80pt"
        android:background="@drawable/login_drawable_btn_unselect"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/common_btn_agree"
        android:textColor="@color/color_FFFFFF"
        android:textSize="32pt"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>