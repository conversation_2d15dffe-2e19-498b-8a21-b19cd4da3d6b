<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="om.rrtx.mobile.loginmodule.R" />
        <import type="om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:ignore="ResourceName">

        <include
            android:id="@+id/include_title"
            layout="@layout/login_base_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view_lin"
            android:layout_width="match_parent"
            android:layout_height="1pt"
            android:background="@color/color_E5E6EB"
            app:layout_constraintTop_toBottomOf="@id/include_title" />

        <View
            android:id="@+id/view_tab"
            android:layout_width="match_parent"
            android:layout_height="77pt"
            android:background="@color/color_FFFFFF"
            app:layout_constraintTop_toBottomOf="@id/view_lin" />

        <ImageView
            android:id="@+id/cv_dot"
            android:layout_width="10pt"
            android:layout_height="10pt"
            android:gravity="center"
            android:textSize="100pt"
            android:textColor="@color/common_text_1d2129"
            android:layout_marginLeft="30pt"
            android:background="@drawable/ic_dot"
            app:layout_constraintBottom_toBottomOf="@id/view_tab"
            app:layout_constraintLeft_toLeftOf="@id/view_tab"
            app:layout_constraintTop_toTopOf="@id/view_tab" />

        <TextView
            android:id="@+id/tv_cur_step"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="13pt"
            android:textSize="30pt"
            android:textColor="@color/common_text_1d2129"
            app:layout_constraintBottom_toBottomOf="@id/view_tab"
            app:layout_constraintLeft_toRightOf="@id/cv_dot"
            app:layout_constraintTop_toTopOf="@id/view_tab" />

        <TextView
            android:id="@+id/tv_cur_page"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textSize="40pt"
            android:textColor="@color/common_ye_F3881E"
            app:layout_constraintBottom_toBottomOf="@id/tv_all_step"
            app:layout_constraintRight_toLeftOf="@id/tv_all_step" />

        <TextView
            android:id="@+id/tv_all_step"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginRight="30pt"
            android:text="/6"
            android:textSize="28pt"
            android:textColor="@color/common_ye_F3881E"
            app:layout_constraintBottom_toBottomOf="@id/view_tab"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/view_tab" />

        <androidx.constraintlayout.widget.ConstraintLayout
            app:layout_constraintTop_toBottomOf="@id/view_tab"
            android:background="@color/common_bg_f7f8fa"
            android:paddingLeft="30pt"
            android:paddingRight="30pt"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <View
                app:layout_constraintTop_toTopOf="parent"
                android:id="@+id/bg_lan0"
                cornerBackgroundRadius="@{4}"
                android:layout_width="match_parent"
                android:layout_height="88pt"
                android:layout_marginTop="25pt"
                tools:ignore="MissingPrefix" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="24pt"
                android:text="@string/english"
                android:textColor="@color/common_text_1d2129"
                android:textSize="30pt"
                app:layout_constraintBottom_toBottomOf="@id/bg_lan0"
                app:layout_constraintLeft_toLeftOf="@id/bg_lan0"
                app:layout_constraintTop_toTopOf="@id/bg_lan0" />

            <View
                android:id="@+id/iv_english"
                android:layout_width="38pt"
                android:layout_height="26pt"
                android:layout_marginRight="24pt"
                android:background="@drawable/ic_ok"
                app:layout_constraintBottom_toBottomOf="@id/bg_lan0"
                app:layout_constraintRight_toRightOf="@id/bg_lan0"
                app:layout_constraintTop_toTopOf="@id/bg_lan0" />


            <View
                android:id="@+id/bg_lan1"
                cornerBackgroundRadius="@{4}"
                android:layout_width="match_parent"
                android:layout_height="88pt"
                android:layout_marginTop="25pt"
                app:layout_constraintTop_toBottomOf="@id/bg_lan0"
                tools:ignore="MissingPrefix" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="24pt"
                android:text="@string/shona"
                android:textColor="@color/common_text_1d2129"
                android:textSize="30pt"
                app:layout_constraintBottom_toBottomOf="@id/bg_lan1"
                app:layout_constraintLeft_toLeftOf="@id/bg_lan1"
                app:layout_constraintTop_toTopOf="@id/bg_lan1" />

            <View
                android:id="@+id/iv_shona"
                android:layout_width="38pt"
                android:layout_height="26pt"
                android:layout_marginRight="24pt"
                android:background="@drawable/ic_ok"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/bg_lan1"
                app:layout_constraintRight_toRightOf="@id/bg_lan1"
                app:layout_constraintTop_toTopOf="@id/bg_lan1" />


            <View
                android:id="@+id/bg_lan2"
                cornerBackgroundRadius="@{4}"
                android:layout_width="match_parent"
                android:layout_height="88pt"
                android:layout_marginTop="25pt"
                app:layout_constraintTop_toBottomOf="@id/bg_lan1"
                tools:ignore="MissingPrefix" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="24pt"
                android:text="@string/ndebele"
                android:textColor="@color/common_text_1d2129"
                android:textSize="30pt"
                app:layout_constraintBottom_toBottomOf="@id/bg_lan2"
                app:layout_constraintLeft_toLeftOf="@id/bg_lan2"
                app:layout_constraintTop_toTopOf="@id/bg_lan2" />

            <View
                android:id="@+id/iv_ndebele"
                android:layout_width="38pt"
                android:layout_height="26pt"
                android:layout_marginRight="24pt"
                android:background="@drawable/ic_ok"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/bg_lan2"
                app:layout_constraintRight_toRightOf="@id/bg_lan2"
                app:layout_constraintTop_toTopOf="@id/bg_lan2" />

            <TextView
                android:id="@+id/tv_next"
                android:layout_width="match_parent"
                android:layout_height="80pt"
                android:layout_marginTop="24pt"
                android:background="@drawable/common_usable_btn"
                android:gravity="center"
                android:text="@string/next"
                android:textColor="@color/color_FFFFFF"
                android:textSize="32pt"
                app:layout_constraintTop_toBottomOf="@id/bg_lan2" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>