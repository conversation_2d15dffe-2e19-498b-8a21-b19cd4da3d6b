<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:background="@color/color_F5F7F8"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/include_title"
            layout="@layout/login_base_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/view_lin"
            android:layout_width="match_parent"
            android:layout_height="1pt"
            android:background="@color/color_E5E6EB"
            app:layout_constraintTop_toBottomOf="@id/include_title" />

        <View
            android:id="@+id/view_tab"
            android:layout_width="match_parent"
            android:layout_height="77pt"
            android:background="@color/color_FFFFFF"
            app:layout_constraintTop_toBottomOf="@id/view_lin" />

        <ImageView
            android:id="@+id/cv_dot"
            android:layout_width="10pt"
            android:layout_height="10pt"
            android:gravity="center"
            android:textSize="100pt"
            android:textColor="@color/common_text_1d2129"
            android:layout_marginLeft="30pt"
            android:background="@drawable/ic_dot"
            app:layout_constraintBottom_toBottomOf="@id/view_tab"
            app:layout_constraintLeft_toLeftOf="@id/view_tab"
            app:layout_constraintTop_toTopOf="@id/view_tab" />

        <TextView
            android:id="@+id/tv_cur_step"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="13pt"
            android:paddingRight="100pt"
            android:textSize="30pt"
            android:textColor="@color/common_text_1d2129"
            app:layout_constraintBottom_toBottomOf="@id/view_tab"
            app:layout_constraintLeft_toRightOf="@id/cv_dot"
            app:layout_constraintTop_toTopOf="@id/view_tab" />

        <LinearLayout
            android:orientation="horizontal"
            app:layout_constraintBottom_toBottomOf="@id/view_tab"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/view_tab"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">
            <TextView
                android:layout_gravity="bottom"
                android:id="@+id/tv_cur_page"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/common_ye_F3881E"
                android:textSize="40pt"/>

            <TextView
                android:layout_gravity="bottom"
                android:id="@+id/tv_all_step"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginRight="30pt"
                android:text="/6"
                android:textColor="@color/common_ye_F3881E"
                android:textSize="28pt" />

        </LinearLayout>

        <TextView
            android:id="@+id/hintTv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="32pt"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:ellipsize="none"
            android:scrollHorizontally="false"
            android:textColor="#4E5969"
            android:textSize="28pt"
            android:gravity="left"
            app:layout_constraintTop_toBottomOf="@id/view_tab"/>

        <TextView
            android:id="@+id/phoneTv"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:ellipsize="none"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:gravity="left"
            android:scrollHorizontally="false"
            android:textColor="@color/common_text_1d2129"
            android:textSize="28pt"
            android:layout_marginTop="20pt"
            app:layout_constraintTop_toBottomOf="@id/hintTv" />

        <TextView
            android:id="@+id/tv_send"
            android:layout_width="match_parent"
            android:layout_height="80pt"
            android:layout_marginLeft="30pt"
            android:layout_marginBottom="60pt"
            android:layout_marginRight="30pt"
            android:background="@drawable/common_usable_btn"
            android:gravity="center"
            android:textColor="@color/color_FFFFFF"
            android:textSize="32pt"
            android:text="@string/done"
            app:layout_constraintBottom_toBottomOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>