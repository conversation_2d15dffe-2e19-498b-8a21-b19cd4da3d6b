<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/rootView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <TextView
            android:id="@+id/title_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/common_text_1d2129"
            android:textSize="28pt"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            />
        
        <EditText
            android:id="@+id/text_ed"
            android:layout_width="match_parent"
            android:layout_height="100pt"
            app:layout_constraintTop_toBottomOf="@id/title_tv"
            android:gravity="left|center_vertical"
            android:textSize="32pt"
            android:textCursorDrawable="@drawable/common_cursor"
            android:textColor="@color/common_text_1d2129"
            cornerBackgroundRadius="@{10}"
            android:paddingLeft="24pt"
            android:layout_marginTop="12pt"
            android:paddingRight="80pt"
            />
        <ImageView
            android:id="@+id/more_iv"
            android:layout_width="48pt"
            android:layout_height="48pt"
            app:layout_constraintTop_toTopOf="@id/text_ed"
            app:layout_constraintBottom_toBottomOf="@id/text_ed"
            app:layout_constraintRight_toRightOf="@id/text_ed"
            android:layout_marginRight="24pt"
            android:background="@drawable/icon_more"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>