<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true"
    android:overScrollMode="never"
    android:scrollbars="none"
    tools:context=".activity.LoginActivity">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/logoTv"
            android:layout_width="275pt"
            android:layout_height="165pt"
            android:layout_marginTop="256pt"
            android:contentDescription="@string/base_app_name"
            android:scaleType="fitXY"
            android:src="@drawable/ic_app"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <LinearLayout
            android:id="@+id/userNameTIL"
            android:layout_width="650pt"
            android:layout_height="100pt"
            android:layout_marginTop="138pt"
            android:background="@drawable/ed_usname_bg"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingLeft="20pt"
            android:paddingRight="20pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/logoTv">

            <TextView
                android:id="@+id/tv_mobile_area"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/jia_263"
                android:textColor="@color/common_text_1d2129"
                android:textSize="32pt" />

            <View
                android:layout_width="1pt"
                android:layout_height="70pt"
                android:layout_marginLeft="21pt"
                android:layout_marginRight="21pt"
                android:background="@color/common_text_C9CDD4" />


            <EditText
                android:id="@+id/nameEt"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@null"
                android:hint="@string/common_label_mobile"
                android:textSize="32pt"
                android:textCursorDrawable="@drawable/common_cursor"
                android:textColor="@color/common_text_1d2129"
                android:inputType="number"
                android:maxLength="9" />


        </LinearLayout>

        <LinearLayout
            android:id="@+id/psdEt_bg"
            android:layout_width="650pt"
            android:layout_height="100pt"
            android:layout_marginTop="33pt"
            android:background="@drawable/ed_usname_bg"
            app:layout_constraintLeft_toLeftOf="@id/userNameTIL"
            app:layout_constraintRight_toRightOf="@id/userNameTIL"
            app:layout_constraintTop_toBottomOf="@id/userNameTIL">

            <EditText
                android:id="@+id/psdEt"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginStart="30pt"
                android:background="@null"
                android:hint="@string/login_PIN"
                android:inputType="numberPassword"
                android:maxLength="4"
                android:textSize="32pt"
                android:textCursorDrawable="@drawable/common_cursor"
                android:textColor="@color/common_text_1d2129"
                />

        </LinearLayout>

        <TextView
            android:id="@+id/forgetNameTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32pt"
            android:text="@string/login_btn_forgot_username"
            android:visibility="gone"
            android:textColor="@color/color_188F86"
            android:textSize="28pt"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintLeft_toLeftOf="@id/psdEt_bg"
            app:layout_constraintRight_toLeftOf="@id/forgetPsdTv"
            app:layout_constraintTop_toBottomOf="@id/psdEt_bg"
            />

        <TextView
            android:id="@+id/loginTv"
            android:layout_width="650pt"
            android:layout_height="100pt"
            android:layout_marginTop="110pt"
            android:background="@drawable/common_unusable_btn"
            android:gravity="center"
            android:enabled="false"
            android:text="@string/common_btn_login"
            android:textColor="@color/color_FFFFFF"
            android:textSize="32pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/psdEt_bg" />


        <TextView
            android:id="@+id/forgetPsdTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32pt"
            android:text="@string/forgot"
            android:textColor="@color/common_text_86909C"
            android:textSize="28pt"
            app:layout_constraintLeft_toLeftOf="@id/loginTv"
            app:layout_constraintTop_toBottomOf="@id/loginTv"
            />

        <TextView
            android:id="@+id/signTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="32pt"
            android:text="@string/login_btn_sign_up"
            android:textColor="@color/common_ye_F3881E"
            android:textSize="28pt"
            app:layout_constraintRight_toRightOf="@id/loginTv"
            app:layout_constraintTop_toBottomOf="@id/loginTv"
            />

        <TextView
            android:id="@+id/signHint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/login_label_no_account_tip"
            android:textColor="@color/color_AEB3C0"
            android:textSize="28pt"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="@id/signTv"
            app:layout_constraintHorizontal_chainStyle="spread_inside"
            app:layout_constraintLeft_toLeftOf="@id/loginTv"
            app:layout_constraintRight_toLeftOf="@id/signTv"
            app:layout_constraintTop_toTopOf="@id/signTv" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.core.widget.NestedScrollView>
