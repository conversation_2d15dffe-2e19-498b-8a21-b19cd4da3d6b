<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="login_welcomeTheme" parent="APPTheme">
        <item name="android:windowFullscreen">true</item>
        <item name="android:windowBackground">@drawable/ic_launcher_bg</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:windowDisablePreview">true</item>
    </style>

    <!--EditText的文字颜色-->
    <style name="login_hintAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textSize">36pt</item>
        <item name="android:textColor">@color/color_000000</item>
    </style>

    <!--EditText的文字颜色-->
    <style name="register_hintAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textSize">28pt</item>
        <item name="android:textColor">@color/color_86909c</item>
    </style>

    <style name="TextInputLayoutStyle" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorControlNormal">@color/color_86909c</item>
        <item name="colorControlActivated">@color/color_86909c</item>
        <item name="colorControlHighlight">@color/color_86909c</item>
        <item name="colorAccent">@color/color_86909c</item>
        <item name="android:textColorHint">@color/color_86909c</item>
    </style>

    <style name="register_inputEditText_Appearance" parent="Widget.AppCompat.EditText">
        <item name="android:textSize">32pt</item>
        <item name="android:textCursorDrawable">@drawable/common_cursor</item>
        <item name="android:textColor">@color/common_text_1d2129</item>

    </style>



</resources>