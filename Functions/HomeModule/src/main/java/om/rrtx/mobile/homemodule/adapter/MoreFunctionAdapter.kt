package om.rrtx.mobile.homemodule.adapter

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.launcher.ARouter
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.homemodule.HomeConstants
import om.rrtx.mobile.homemodule.R
import om.rrtx.mobile.homemodule.activity.JuniorAccountManagementActivity
import om.rrtx.mobile.homemodule.databinding.ItemMoreFunctionBinding
import om.rrtx.mobile.rrtxcommon1.bean.InfoItemBean
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener

class MoreFunctionAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

    lateinit var context: Context

    private var data = arrayListOf<InfoItemBean>()

    private fun getString(id: Int) = context.getString(id)

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        context = recyclerView.context
        initData()
    }

    private fun initData() {
        data.apply {
            add(InfoItemBean(getString(R.string.nearby_Merchants),
                iconId = R.drawable.ic_mer_1,
                typeStr = HomeConstants.FunctionType.Nearby_Mer))

            add(InfoItemBean(getString(R.string.junior_Account_Management),
                iconId = R.drawable.ic_junior,
                typeStr = HomeConstants.FunctionType.Junior_Account_Management))

            add(InfoItemBean(getString(R.string.zipit_title),
                iconId = R.drawable.zipit_icon,
                typeStr = HomeConstants.FunctionType.ZIPIT))

            add(InfoItemBean(getString(R.string.history_label_agent_with),
                iconId = R.drawable.cashout_icon,
                typeStr = HomeConstants.FunctionType.CASFOUT))
        }
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): RecyclerView.ViewHolder {
        val inflater = LayoutInflater.from(context)
        val binding = ItemMoreFunctionBinding.inflate(
            inflater,
            parent,
            false
        )
        return object : RecyclerView.ViewHolder(binding.root) {}
    }

    override fun getItemCount() = data.size
    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val binding =
            DataBindingUtil.getBinding<ItemMoreFunctionBinding>(holder.itemView) ?: return
        val bean = data[position]
        binding.iconIv.setBackgroundResource(bean.iconId)
        binding.titleTv.text = bean.title

        binding.root.setOnClickListener(object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (bean.typeStr) {
                    HomeConstants.FunctionType.Nearby_Mer -> {
                        ARouter.getInstance().build(ARouterPath.Promotion.MerListActivity)
                            .navigation()
                    }

                    HomeConstants.FunctionType.Junior_Account_Management->{
                        JuniorAccountManagementActivity.jump(context)
                    }

                    HomeConstants.FunctionType.ZIPIT->{
                        ARouter.getInstance().build(ARouterPath.TransferPath.ZipitTransActivity)
                            .navigation()
                    }

                    HomeConstants.FunctionType.CASFOUT->{
                        ARouter.getInstance().build(ARouterPath.TransferPath.CrashOutActivity)
                            .navigation()
                    }
                }
            }
        })
    }
}