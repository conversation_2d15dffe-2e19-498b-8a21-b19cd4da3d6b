package om.rrtx.mobile.homemodule.presenter;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.google.gson.Gson;

import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.bean.CurrencyBean;
import om.rrtx.mobile.functioncommon.bean.DictoryBean;
import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.homemodule.bean.PubBean;
import om.rrtx.mobile.homemodule.bean.UpDataBean;
import om.rrtx.mobile.homemodule.model.HomeModel;
import om.rrtx.mobile.homemodule.utils.LogUtil;
import om.rrtx.mobile.homemodule.view.HomeView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseNoDialogObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * 首页的p层
 */
public class HomePresenter extends BasePresenter<HomeView> {
    private String TAG = HomePresenter.class.getSimpleName();
    private Context mContext;
    private HomeModel mHomeModel;

    public HomePresenter(Context context) {
        mHomeModel = new HomeModel();
        mContext = context;
    }

    /**
     * 请求账户余额接口
     */
    public void requestLogout() {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mHomeModel.requestLogout(userId, new BaseObserver<Object>(UserConstants.URL.LOGOUT,mContext) {
                @Override
                public void requestSuccess(Object object) {
                    if (getView() != null) {
                        getView().logoutSuccess();
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().logoutFail();
                    }
                }
            });
        } else {
            mHomeModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mHomeModel.requestLogout(userId, new BaseObserver<Object>(mContext) {
                        @Override
                        public void requestSuccess(Object object) {
                            if (getView() != null) {
                                getView().logoutSuccess();
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().logoutFail();
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().logoutFail();
                    }
                }
            });
        }
    }

    public void requestUpData(String versionName) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mHomeModel.requestUpData(userId, versionName, new BaseObserver<UpDataBean>(mContext) {
                @Override
                public void requestSuccess(UpDataBean sResData) {
                    if (getView() != null) {
                        getView().upDataSuccess(sResData);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                }
            });
        } else {
            mHomeModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mHomeModel.requestUpData(userId, versionName, new BaseObserver<UpDataBean>(mContext) {
                        @Override
                        public void requestSuccess(UpDataBean sResData) {
                            if (getView() != null) {
                                getView().upDataSuccess(sResData);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    public void requestLanguageSetUp(String language) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mHomeModel.requestLanguageSetUp(userId, language, new BaseObserver<Object>(mContext) {
                @Override
                public void requestSuccess(Object sResData) {
                    if (getView() != null) {
                        getView().languageSetUpSuccess();
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().languageSetUpFail();
                    }
                }
            });
        } else {
            mHomeModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mHomeModel.requestLanguageSetUp(userId, language, new BaseObserver<Object>(mContext) {
                        @Override
                        public void requestSuccess(Object sResData) {
                            if (getView() != null) {
                                getView().languageSetUpSuccess();
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().languageSetUpFail();
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    public void queryDefaultCurrency() {
        new CommonModel().queryDefaultCurrency(new BaseNoDialogObserver<DictoryBean.AppDictBean>(mContext) {
            @Override
            public void requestSuccess(DictoryBean.AppDictBean dictBean) {
                SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.CURCURRENCY,dictBean.dictLabel);
            }

            @Override
            public void requestFail(String s) {

            }
        });
    }

    public void getAvailableCurrency() {
        Activity activity = ActivityController.getInstance().currentActivity();
        new CommonModel().getAvailableCurrency("", new BaseNoDialogObserver<CurrencyBean>(activity) {
            @Override
            public void requestFail(String sResMsg) {

            }

            @Override
            public void requestSuccess(CurrencyBean sResData) {
                String json = new Gson().toJson(sResData.getCurrencyList());
                SharedPreferencesUtils.setParam(activity, BaseConstants.SaveParameter.ACTIVATE_CURRENCY, json);
            }
        });
    }
}
