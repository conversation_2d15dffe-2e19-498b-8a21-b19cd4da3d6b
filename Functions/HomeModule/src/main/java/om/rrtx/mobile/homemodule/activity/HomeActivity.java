package om.rrtx.mobile.homemodule.activity;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.drawerlayout.widget.DrawerLayout;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.widget.ViewPager2;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.alibaba.android.arouter.launcher.ARouter;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import com.gyf.immersionbar.ImmersionBar;
import com.kapp.xmarketing.ShowMarketingManager;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functionapi.ITransferService;
import om.rrtx.mobile.functionapi.ServiceFactory;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.homemodule.HomeConstants;
import om.rrtx.mobile.homemodule.dialog.AloneDialog;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.R2;
import om.rrtx.mobile.homemodule.adapter.BaseVp2Adapter;
import om.rrtx.mobile.homemodule.bean.UpDataBean;
import om.rrtx.mobile.homemodule.dialog.DoubleDialog;
import om.rrtx.mobile.homemodule.fragment.AccountFragment;
import om.rrtx.mobile.homemodule.fragment.HomeFragment;
import om.rrtx.mobile.homemodule.fragment.PendingFragment;
import om.rrtx.mobile.homemodule.presenter.HomePresenter;
import om.rrtx.mobile.homemodule.utils.JumpJudgementUtils;
import om.rrtx.mobile.homemodule.utils.LogUtil;
import om.rrtx.mobile.homemodule.view.HomeView;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.dialog.UpDataDialog;
import om.rrtx.mobile.rrtxcommon1.utils.AppInfoUtils;
import om.rrtx.mobile.rrtxcommon1.utils.AppInitUtil;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.GoToScoreUtils;
import om.rrtx.mobile.rrtxcommon1.utils.LocaleManager;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 首页
 */
@Route(path = ARouterPath.HomePath.HomeActivity)
public class HomeActivity extends BaseSuperActivity<HomeView, HomePresenter>
        implements View.OnLongClickListener,
        BottomNavigationView.OnNavigationItemSelectedListener,
        HomeFragment.HomeCallBack, HomeView {

    @BindView(R2.id.logoutBg)
    View mLogoutBg;
    @BindView(R2.id.languageBg)
    View mLanguageBg;
    @BindView(R2.id.contactsBg)
    View mContactsBg;
    @BindView(R2.id.aboutBg)
    View mAboutBg;
    @BindView(R2.id.languageTv)
    TextView mLanguageTv;
    @BindView(R2.id.upDateBg)
    View mUpDateBg;
    @BindView(R2.id.contactBg)
    View mContactBg;
    @BindView(R2.id.broadcastBg)
    View mBroadcastBg;
    @BindView(R2.id.logoutV)
    View mLogoutV;
    private BottomNavigationView mBnv;
    private View mHomeItem;
    private View mNotificationItem;
    private View mAccountItem;
    private List<Fragment> mFragments;
    private HomeFragment mHomeFragment;
    private ViewPager2 mContentVp2;
    private PendingFragment mNotificationFragment;
    private AccountFragment mAccountFragment;
    private DrawerLayout mHomeDl;
    private long mStartTime = 0;
    private int expectPosition;

    @Override
    protected int createContentView() {
        return R.layout.home_activity_home;
    }

    @Override
    protected HomePresenter createPresenter() {
        return new HomePresenter(mContext);
    }

    @Override
    public void doGetExtra() {
        expectPosition = getIntent().getIntExtra(CommonConstants.Transmit.POSITION, 0);

        String userName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.REALNAME, "");
        String realName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.REALNAME, "");
        String userMobile = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERMOBILE, "");
        String currency = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.SAVECURRENCY, "");
        String language = LocaleManager.getInstance().getLanguage(mContext);

        //获取相应的meta-data标签内容
        String baseURL = HomeConstants.Urls.BASE_MARKET_FAT_URL;
        try {
            ApplicationInfo applicationInfo = getPackageManager().getApplicationInfo(getPackageName(), PackageManager.GET_META_DATA);
            String appType = applicationInfo.metaData.getString("appType");
            if (!TextUtils.isEmpty(appType)) {
                switch (appType) {
                    case "dev":
                        baseURL = HomeConstants.Urls.BASE_MARKET_DEV_URL;
                        break;
                    case "test":
                        baseURL = HomeConstants.Urls.BASE_MARKET_TEST_URL;
                        break;
                    case "uat":
                        baseURL = HomeConstants.Urls.BASE_MARKET_UAT_URL;
                        break;
                    case "fat":
                        baseURL = HomeConstants.Urls.BASE_MARKET_FAT_URL;
                        break;
                    default:
                        baseURL = HomeConstants.Urls.BASE_MARKET_FAT_URL;
                }
            }
        ShowMarketingManager.getInstance().init(this, language, userName, realName, userMobile, baseURL, "", currency,"");

        } catch (PackageManager.NameNotFoundException e) {
            e.printStackTrace();
        }
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarDarkFont(true, 0.2f)
                .init();
        AppInitUtil.initImageLoader();

        mBnv = findViewById(R.id.bnv);
        mHomeItem = findViewById(R.id.menu_home);
        mNotificationItem = findViewById(R.id.menu_notification);
        mAccountItem = findViewById(R.id.menu_account);
        mContentVp2 = findViewById(R.id.contentVp2);
        mHomeDl = findViewById(R.id.homeDl);

        if (!TextUtils.isEmpty(BaseApp.getIsHasDownLoadUrl())) {
            UpDataDialog upDataDialog = new UpDataDialog(mContext);
            upDataDialog.show();
            upDataDialog.setClickCallBack(() -> {
                Uri uri = Uri.parse(BaseApp.getIsHasDownLoadUrl());
                Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                mContext.startActivity(intent);
            });
        }

    }


    @Override
    public void initDate() {
        super.initDate();
        //添加ViewPager的数据
        initViewPagerData();
        mPresenter.queryDefaultCurrency();
        mPresenter.getAvailableCurrency();
    }


    /**
     * 添加ViewPager的数据
     */
    private void initViewPagerData() {
        //添加相应的数据和页面
        mFragments = new ArrayList<>();

        mHomeFragment = new HomeFragment();
        mHomeFragment.setHomeCallBack(this);
        mFragments.add(mHomeFragment);

        mNotificationFragment = new PendingFragment();
        mFragments.add(mNotificationFragment);

        mAccountFragment = new AccountFragment();
        mFragments.add(mAccountFragment);

        BaseVp2Adapter baseVp2Adapter = new BaseVp2Adapter(this, mFragments);
        mContentVp2.setAdapter(baseVp2Adapter);
        mContentVp2.setOffscreenPageLimit(1);
    }

    @Override
    public void initListener() {
        super.initListener();

        //底部Tab的点击事件
        mBnv.setOnNavigationItemSelectedListener(this);

        //每个Tab的长按事件,这里主要是为了去除Toast
        mHomeItem.setOnLongClickListener(this);
        mNotificationItem.setOnLongClickListener(this);
        mAccountItem.setOnLongClickListener(this);

        //禁止滑动
        mContentVp2.setUserInputEnabled(false);

        mLogoutBg.setOnClickListener(mHomeCustom);
        mLanguageBg.setOnClickListener(mHomeCustom);
        mContactsBg.setOnClickListener(mHomeCustom);
        mAboutBg.setOnClickListener(mHomeCustom);
        mUpDateBg.setOnClickListener(mHomeCustom);
        mContactBg.setOnClickListener(mHomeCustom);
        mBroadcastBg.setOnClickListener(mHomeCustom);
        mLogoutV.setOnClickListener(mHomeCustom);
    }

    private CustomClickListener mHomeCustom = new CustomClickListener() {

        private DoubleDialog mDoubleDialog;

        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.logoutBg) {
                if (mDoubleDialog == null) {
                    mDoubleDialog = new DoubleDialog(mContext);
                    mDoubleDialog.setDoubleCallback(new DoubleDialog.DoubleCallback() {
                        @Override
                        public void leftCallback() {
                            mDoubleDialog.dismiss();
                            //退出登录
                            mPresenter.requestLogout();
                        }

                        @Override
                        public void rightCallback() {
                            mDoubleDialog.dismiss();
                        }
                    });
                }
                mDoubleDialog.show();
                mDoubleDialog.setLeftStr(getString(R.string.common_alert_continue))
                        .setLeftColor(getResources().getColor(R.color.color_F85A40))
                        .setRightStr(getString(R.string.common_alert_cancel))
                        .setRightColor(getResources().getColor(R.color.common_ye_F3881E))
                        .setTitle(getString(R.string.common_alert_prompt));

                mDoubleDialog.setContentStr(getString(R.string.slider_alert_logout_tip));

            } else if (view.getId() == R.id.languageBg) {
                String[] LanguageArr = {getString(R.string.english), getString(R.string.shona),getString(R.string.ndebele)};
                ServiceFactory.getInstance().getTransferService().showRegion(mContext, LanguageArr, new ITransferService.RegionCallBack() {
                    @Override
                    public void confirmCallBack(int currentPosition) {
                        if (currentPosition == 0) {
                            //切换成英文
                            LocaleManager.getInstance().setNewLocale(mContext, LocaleManager.LANGUAGE_ENGLISH);
                            mPresenter.requestLanguageSetUp(LocaleManager.LANGUAGE_ENGLISH);
                        } else if (currentPosition == 1) {
                            //切换成Shona
                            LocaleManager.getInstance().setNewLocale(mContext, LocaleManager.LANGUAGE_SHONA);
                            mPresenter.requestLanguageSetUp(LocaleManager.LANGUAGE_SHONA);
                        } else if (currentPosition == 2) {
                            //切换Ndebele
                            LocaleManager.getInstance().setNewLocale(mContext, LocaleManager.LANGUAGE_NDEBELE);
                            mPresenter.requestLanguageSetUp(LocaleManager.LANGUAGE_NDEBELE);
                        }
                    }

                    @Override
                    public void cancelCallBack() {

                    }
                });
                String language = LocaleManager.getInstance().getLanguage(mContext);
                switch (language) {
                    case LocaleManager.LANGUAGE_CHINA:
                        ServiceFactory.getInstance().getTransferService().setSelectLanguage(1);
                        break;
                    case LocaleManager.LANGUAGE_ENGLISH:
                        ServiceFactory.getInstance().getTransferService().setSelectLanguage(0);
                        break;
                    default:
                }
            } else if (view.getId() == R.id.contactsBg) {
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.ContactListJump, () -> {
                    ARouter.getInstance().build(ARouterPath.TransferPath.ContactListActivity)
                            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP)
                            .navigation();
                });
                mHomeDl.closeDrawers();
            } else if (view.getId() == R.id.aboutBg) {
                AboutActivity.jumpAbout(mContext);
                mHomeDl.closeDrawers();
            } else if (view.getId() == R.id.upDateBg) {
                mPresenter.requestUpData(AppInfoUtils.getVersionName(mContext));
            } else if (view.getId() == R.id.contactBg) {
                ContactUsActivity.jumpContactUs(mContext);
                mHomeDl.closeDrawers();
            } else if (view.getId() == R.id.broadcastBg) {
                BroadcastSettingActivity.jumpBroadcastSetting(mContext);
                mHomeDl.closeDrawers();
            } else if (view.getId() == R.id.logoutV) {
                boolean isFinger = (boolean) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ISFINGER, false);
                boolean isGesture = (boolean) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ISGESTURE, false);
                if (isFinger) {
                    ARouter.getInstance().build(ARouterPath.SecurityPath.LoginFingerLockActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .withInt(BaseConstants.Transmit.FROMTYPE, 1)
                            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP)
                            .withBoolean(BaseConstants.Transmit.ISPSDBACK, true)
                            .navigation();
                } else if (isGesture) {
                    ARouter.getInstance().build(ARouterPath.SecurityPath.LoginGestureActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP)
                            .withBoolean(BaseConstants.Transmit.ISPSDBACK, true)
                            .navigation();
                } else {
                    ARouter.getInstance()
                            .build(ARouterPath.SecurityPath.LoginPasswordLockActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .withBoolean(BaseConstants.Transmit.ISPSDBACK, true)
                            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP)
                            .navigation();
                    //SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.AUTHORIZATION, "");
                }
            }
        }
    };

    public void reStart(Context context) {
        Intent intent = new Intent(context, HomeActivity.class);
        //intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TASK | Intent.FLAG_ACTIVITY_NEW_TASK);
        context.startActivity(intent);
    }

    @Override
    protected void onResume() {
        super.onResume();
        boolean appState = BaseApp.isIsLock();
        boolean isFinger = (boolean) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ISFINGER, false);
        boolean isGesture = (boolean) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ISGESTURE, false);
        if (appState) {
            mBnv.setSelectedItemId(mBnv.getMenu().getItem(0).getItemId());
            mContentVp2.setCurrentItem(0);
            expectPosition = 0;
        }

        if (expectPosition != 0) {
            mBnv.setSelectedItemId(mBnv.getMenu().getItem(expectPosition).getItemId());
            mContentVp2.setCurrentItem(expectPosition);
            expectPosition = 0;
        }
    }


    @Override
    public boolean onLongClick(View view) {
        return true;
    }

    @Override
    public boolean onNavigationItemSelected(@NonNull MenuItem item) {
        LogUtil.e("done", "onNavigationItemSelected: ");
        if (item.getItemId() == R.id.menu_home) {
            mContentVp2.setCurrentItem(0);
            mHomeDl.setDrawerLockMode(DrawerLayout.LOCK_MODE_UNDEFINED);
        } else if (item.getItemId() == R.id.menu_notification) {
            JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.HomePendingJump, () -> {
                mContentVp2.setCurrentItem(1);
            });
            mHomeDl.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
        } else if (item.getItemId() == R.id.menu_account) {
            JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.HomeAccountJump, () -> {
                mContentVp2.setCurrentItem(2);
            });
            mHomeDl.setDrawerLockMode(DrawerLayout.LOCK_MODE_LOCKED_CLOSED);
        }
        return true;
    }

    @Override
    public void homeCallBack() {
        mHomeDl.openDrawer(Gravity.LEFT);
    }

    @Override
    public void logoutSuccess() {
//        ARouter.getInstance()
//                .build(ARouterPath.SecurityPath.LoginPasswordLockActivity)
//                .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK)
//                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP)
//                .navigation();
//        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.AUTHORIZATION, "");
//        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.ISFINGER, false);
        mHomeDl.closeDrawers();
        mHomeFragment.logout();
        mLogoutV.setVisibility(View.VISIBLE);
    }

    @Override
    public void logoutFail() {
        mHomeDl.closeDrawers();
        mHomeFragment.logout();
        mLogoutV.setVisibility(View.VISIBLE);
    }

    @Override
    public void upDataSuccess(UpDataBean sResData) {
        if (sResData != null) {
            String isLatestVersion = sResData.getIsLatestVersion();
            if (TextUtils.equals(isLatestVersion, "1")) {
                // 不需要更新
                AloneDialog aloneDialog = new AloneDialog(mContext);
                aloneDialog.setAloneCallBack(new AloneDialog.AloneCallBack() {
                    @Override
                    public void clickEnter() {
                        aloneDialog.dismiss();
                    }
                });
                aloneDialog.show();
                aloneDialog.setTitle(getString(R.string.common_alert_prompt))
                        .setBottomStr(getString(R.string.common_alert_cancel))
                        .setContentStr(getString(R.string.setting_alert_lastversion))
                        .setBottomColor(mContext.getResources().getColor(R.color.common_ye_F3881E));
            } else {
                if (TextUtils.equals("1", sResData.getUpdateFlag())) {
                    //需要更新
                    DoubleDialog doubleDialog = new DoubleDialog(mContext);
                    doubleDialog.setDoubleCallback(new DoubleDialog.DoubleCallback() {
                        @Override
                        public void leftCallback() {
                            //更新操作
                            doubleDialog.dismiss();
                            GoToScoreUtils.goToMarket(mContext, mContext.getPackageName());
                        }

                        @Override
                        public void rightCallback() {
                            //取消
                            doubleDialog.dismiss();
                        }
                    });
                    doubleDialog.show();
                    doubleDialog.setTitle(getString(R.string.common_alert_prompt))
                            .setContentStr(getString(R.string.setting_alert_newversion))
                            .setLeftColor(mContext.getResources().getColor(R.color.color_F85A40))
                            .setLeftStr(getString(R.string.setting_alert_update))
                            .setRightColor(mContext.getResources().getColor(R.color.common_ye_F3881E))
                            .setRightStr(getString(R.string.common_alert_cancel));
                } else {
                    UpDataDialog upDataDialog = new UpDataDialog(mContext);
                    upDataDialog.show();
                    upDataDialog.setClickCallBack(() -> {
//                try {
//                    Log.e("TAG", "initView: ");
//                    Uri uri = Uri.parse("market://details?id=" + BaseApp.getIsHasDownLoadUrl());
//                    Intent goToMarket = new Intent(Intent.ACTION_VIEW, uri);
//                    goToMarket.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
//                    mContext.startActivity(goToMarket);
//                } catch (ActivityNotFoundException e) {
//                    e.printStackTrace();
//                    Uri uri = Uri.parse(BaseApp.getIsHasDownLoadUrl());
//                    Intent intent = new Intent(Intent.ACTION_VIEW, uri);
//                    startActivity(intent);
//                }
                        Uri uri = Uri.parse(sResData.getDownloadUrl());
                        Intent intent = new Intent(Intent.ACTION_VIEW, uri);
                        mContext.startActivity(intent);
                    });
                }
            }
        }
    }

    @Override
    public void requestFail(String sResMsg) {

    }

    @Override
    public void languageSetUpSuccess() {
        reStart(mContext);
    }

    @Override
    public void languageSetUpFail() {
        reStart(mContext);
    }

    @Override
    public void onBackPressed() {
        long secondTime = System.currentTimeMillis();
        if (secondTime - mStartTime > 2000) {
            ToastUtil.show(mContext, getString(R.string.common_tip_double_click_tip));
            mStartTime = secondTime;
        } else {
            moveTaskToBack(true);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        Log.e("HomeActivity>>>", "zfw onNewIntent>>> :");
        if (intent != null) {
            String jumpFlag = intent.getStringExtra(BaseConstants.Transmit.JUMPFLAG);
            if (TextUtils.equals(jumpFlag, BaseConstants.HomeJumpType.HomePendingJump)) {
                mContentVp2.setCurrentItem(1);
            } else if (TextUtils.equals(jumpFlag, BaseConstants.HomeJumpType.HomeAccountJump)) {
                mContentVp2.setCurrentItem(2);
            } else if (TextUtils.equals(jumpFlag, BaseConstants.HomeJumpType.HomeHomeJump)) {
                mContentVp2.setCurrentItem(0);
                mBnv.setSelectedItemId(R.id.menu_home);

                mHomeDl.closeDrawers();
            } else if (TextUtils.equals(jumpFlag, BaseConstants.JumpFlag.HOMEAALAUNCH)) {
                mContentVp2.setCurrentItem(1);
                mBnv.setSelectedItemId(mBnv.getMenu().getItem(1).getItemId());
            } else if (TextUtils.equals(jumpFlag, BaseConstants.JumpFlag.HOMEAARECEIVED)) {
                mContentVp2.setCurrentItem(1);
                mBnv.setSelectedItemId(mBnv.getMenu().getItem(1).getItemId());
                mNotificationFragment.setShowReceived();
            }
            int fromType = intent.getIntExtra(BaseConstants.Transmit.FROMTYPE, 0);
            if (fromType == 1) {
                mLogoutV.setVisibility(View.GONE);
                mHomeFragment.login();
                mContentVp2.setCurrentItem(0);
            }
        }
    }
}




