package om.rrtx.mobile.homemodule.utils;

import android.content.Context;
import android.widget.ImageView;

import com.google.android.material.imageview.ShapeableImageView;
import com.google.android.material.shape.ShapeAppearanceModel;
import com.youth.banner.loader.ImageLoader;

import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.bean.BannerBean;
import om.rrtx.mobile.rrtxcommon1.image.ImageLoaderManager;

public class GlideImageLoader extends ImageLoader {
    @Override
    public void displayImage(Context context, Object path, ImageView imageView) {
        /**
         注意：
         1.图片加载器由自己选择，这里不限制，只是提供几种使用方法
         2.返回的图片路径为Object类型，由于不能确定你到底使用的那种图片加载器，
         传输的到的是什么格式，那么这种就使用Object接收和返回，你只需要强转成你传输的类型就行，
         切记不要胡乱强转！
         */
        //用fresco加载图片简单用法，记得要写下面的createImageView方法
        BannerBean.DetailsBean detailsBean = (BannerBean.DetailsBean) path;

        //Glide 加载图片简单用法
        ImageLoaderManager.getInstance().disPlayImage(context, detailsBean.getImageUrl(), R.drawable.drawable_loader_bg, imageView);
    }

    @Override
    public ImageView createImageView(Context context) {
        ShapeableImageView shapeableImageView = new ShapeableImageView(context);
        ShapeAppearanceModel shapeAppearanceModel = ShapeAppearanceModel.builder(context, 0, R.style.smallRoundedCornerStyle).build();
        shapeableImageView.setShapeAppearanceModel(shapeAppearanceModel);
        return shapeableImageView;
    }
}