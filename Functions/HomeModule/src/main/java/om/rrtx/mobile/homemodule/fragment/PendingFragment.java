package om.rrtx.mobile.homemodule.fragment;

import android.view.View;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.Fragment;
import androidx.viewpager.widget.ViewPager;

import com.flyco.tablayout.SlidingTabLayout;
import com.gyf.immersionbar.ImmersionBar;

import java.util.ArrayList;
import java.util.List;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ServiceFactory;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.R2;
import om.rrtx.mobile.homemodule.adapter.BaseVPAdapter;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.MVPWithLazyBaseFragment;


/**
 * <AUTHOR>
 * pending页面
 * 这个页面的Fragment来自于payment模块
 */
public class PendingFragment extends MVPWithLazyBaseFragment {
    @BindView(R2.id.title)
    TextView mTitle;
    @BindView(R2.id.stl)
    SlidingTabLayout mStl;
    @BindView(R2.id.contentVp2)
    ViewPager mContentVp2;
    @BindView(R2.id.pendingView)
    ConstraintLayout mPendingView;
    private String[] titles;
    private List<Fragment> mFragmentList;

    @Override
    protected int createViewLayoutId() {
        return R.layout.home_fragment_pending;
    }

    @Override
    public BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView(View rootView) {
        ImmersionBar.with(this)
                .statusBarDarkFont(true, 0.2f)
                .statusBarView(R.id.statusView, mPendingView)
                .init();
    }

    @Override
    public void initDate() {
        super.initDate();
        titles = new String[]{getString(R.string.aa_btn_lssued), getString(R.string.aa_btn_receive)};

        //添加数据
        mFragmentList = new ArrayList<>();
        Fragment lssuedFragment = ServiceFactory.getInstance().getPaymentService().LssuedFragment();
        mFragmentList.add(lssuedFragment);
        Fragment receiveFragment = ServiceFactory.getInstance().getPaymentService().ReceiveFragment();
        mFragmentList.add(receiveFragment);

        BaseVPAdapter adapter = new BaseVPAdapter(getChildFragmentManager(), mFragmentList);
        mContentVp2.setAdapter(adapter);

        mStl.setViewPager(mContentVp2, titles);
    }

    public void setShowReceived() {
        if (mContentVp2 != null) {
            mContentVp2.setCurrentItem(1);
        }
    }
}
