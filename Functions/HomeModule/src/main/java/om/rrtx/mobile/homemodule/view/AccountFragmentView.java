package om.rrtx.mobile.homemodule.view;

import om.rrtx.mobile.functioncommon.bean.BalanceBean;
import om.rrtx.mobile.functioncommon.bean.BalanceCertificateBean;
import om.rrtx.mobile.homemodule.bean.ActivityBean;
import om.rrtx.mobile.rrtxcommon1.bean.AppDictListBean;
import om.rrtx.mobile.functioncommon.bean.CurrencyAccountListBean;
import om.rrtx.mobile.homemodule.bean.UserQrCodeBean;

/**
 * <AUTHOR>
 * 我的页面接口
 */
public interface AccountFragmentView {
    /**
     * 获取账户余额成功
     *
     * @param balanceBean 余额实体类
     */
    void currencyAccountSuccess(CurrencyAccountListBean balanceBean);

    void qrCodeSuccess(UserQrCodeBean sResData);

    /**
     * 是否有活动
     */
    void isHasActivitySuccess(ActivityBean sResData);

    /**
     * 获取币种列表
     *
     * @param dictListBean 余额实体类
     */
    void getCurListSuccess(AppDictListBean dictListBean);
    void checkBalanceOrderSuccess(BalanceBean dictListBean);
    void getBankNumSuccess(String num);
    void requestBalancePaySuccess(BalanceCertificateBean dictListBean);
}
