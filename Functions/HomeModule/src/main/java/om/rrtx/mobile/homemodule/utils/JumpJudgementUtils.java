package om.rrtx.mobile.homemodule.utils;

import android.content.Context;
import android.content.Intent;
import android.util.Log;

import com.alibaba.android.arouter.launcher.ARouter;

import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * 跳转判断工具类
 */
public class JumpJudgementUtils {

    /**
     * 这里只是首页的判断
     *
     * @param context      上下文
     * @param targetPage   目标页面
     * @param jumpCallBack 回调
     */
    public static void lockJudgement(Context context, String targetPage, JumpCallBack jumpCallBack) {
        boolean appState = BaseApp.isIsLock();
        boolean isFinger = (boolean) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.ISFINGER, false);
        boolean isGesture = (boolean) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.ISGESTURE, false);

        Log.e("==lockJudgement==","appState="+appState);
        if (appState && isFinger) {
            //从后台回到前台,这里应该校验相应的状态
            ARouter.getInstance().build(ARouterPath.SecurityPath.LoginFingerLockActivity)
                    .withBoolean(BaseConstants.Transmit.ISPSDBACK, true)
                    .withString(BaseConstants.Transmit.JUMPFLAG, targetPage)
                    .navigation();
        } else if (appState && isGesture) {
            //从后台回到前台,这里应该校验相应的状态
            ARouter.getInstance().build(ARouterPath.SecurityPath.LoginGestureActivity)
                    .withBoolean(BaseConstants.Transmit.ISPSDBACK, true)
                    .withString(BaseConstants.Transmit.JUMPFLAG, targetPage)
                    .navigation();
        } else if (appState){
            ARouter.getInstance()
                    .build(ARouterPath.SecurityPath.LoginPasswordLockActivity)
                    .withBoolean(BaseConstants.Transmit.ISPSDBACK, true)
                    .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP)
                    .navigation();
            //SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.AUTHORIZATION, "");
        } else {
            if (jumpCallBack != null) {
                jumpCallBack.normalClick();
            }
        }
    }

    public interface JumpCallBack {
        /**
         * 正常点击
         */
        void normalClick();
    }
}
