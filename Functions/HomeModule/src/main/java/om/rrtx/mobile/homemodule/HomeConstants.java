package om.rrtx.mobile.homemodule;

public class HomeConstants {

    public interface Urls{
        String BASE_MARKET_TEST_URL = "https://msf-xmarking-server.rrtx.vimbug.com/"; // 测试
        String BASE_MARKET_DEV_URL = "http://fap-huxf-xmarketing-service.proxy.vimbug.com/"; // 开发
        String BASE_MARKET_UAT_URL = "https://marketing-server.onemoney.co.zw/"; // 演示
        String BASE_MARKET_FAT_URL = "https://mfs-xmarketing-server-fat.rrtx.vimbug.com/"; // 验收

    }

    public interface FunctionType {
        //充值
        String TOP_UP = "07";
        //提现
        String WITH_DRAW = "08";
        //转账
        String SEND_MONEY = "09";
        //交易
        String HISTORY = "10";
        //ZIPIT
        String ZIPIT = "11";
        String CASFOUT = "12";
        //缴费
        String Bill_Pay = "03";
        String AIRTIME = "04";
        //商户
        String Nearby_Mer = "05";
        //亲子账户
        String Junior_Account_Management = "06";
        //更多
        String MORE = "99";
    }
}
