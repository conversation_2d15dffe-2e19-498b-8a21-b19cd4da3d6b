package om.rrtx.mobile.homemodule.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import de.hdodenhof.circleimageview.CircleImageView;
import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.R2;
import om.rrtx.mobile.homemodule.bean.UserQrCodeBean;
import om.rrtx.mobile.homemodule.dialog.RegionPicker;
import om.rrtx.mobile.homemodule.presenter.JuniorPersenterInfoPresenter;
import om.rrtx.mobile.homemodule.view.JuniorPersonalInfoView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.bean.UserInfoBean;
import om.rrtx.mobile.rrtxcommon1.image.ImageLoaderManager;
import om.rrtx.mobile.rrtxcommon1.kotlin.ExtensionsKt;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.QRCodeEncoder;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.TimeUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 用户中心页面
 */
public class JuniorPersonalInfoActivity extends BaseSuperActivity<JuniorPersonalInfoView, JuniorPersenterInfoPresenter>
        implements JuniorPersonalInfoView, RegionPicker.RagionCallBack {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.headIv)
    CircleImageView mHeadIv;
    @BindView(R2.id.userNameBg)
    View mUserNameBg;
    @BindView(R2.id.userNameTv)
    TextView nameTv;
    @BindView(R2.id.nricBg)
    View mNricBg;
    @BindView(R2.id.nricTv)
    TextView mNricTv;
    @BindView(R2.id.mobileBg)
    View mMobileBg;
    @BindView(R2.id.mobileTv)
    TextView mMobileTv;
    @BindView(R2.id.emailBg)
    View mEmailBg;
    @BindView(R2.id.emailTv)
    TextView mEmailTv;
    private static int EMAILREQUEST = 0x2;
    @BindView(R2.id.statusView)
    View mStatusView;
    @BindView(R2.id.heardBg)
    View mHeardBg;

    @BindView(R2.id.RelationshipTv)
    TextView RelationshipTv;
    @BindView(R2.id.NameTv)
    TextView NameTv;
    @BindView(R2.id.middleNameTv)
    TextView middleNameTv;
    @BindView(R2.id.MobileTv)
    TextView MobileTv;
    @BindView(R2.id.LinkTv)
    TextView LinkTv;
    private String mGender;
    private RegionPicker mRegionPicker;
    private String mEmail;
    private ImageView qrCodeIv;
    private TextView expiringTv;
    private UserInfoBean mUserInfoBean;
    private ImageView qrCodeMore;
    private Disposable qrCodeTask;

    public static void jumpPersonalInfo(Context context) {
        Intent intent = new Intent(context, JuniorPersonalInfoActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int createContentView() {
        return R.layout.home_activity_junior_info;
    }

    @Override
    protected JuniorPersenterInfoPresenter createPresenter() {
        return new JuniorPersenterInfoPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.home_ic_back);

        mStatusView.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));
        mTitleTv.setText(R.string.personal_title_personal_info);

        qrCodeIv = findViewById(R.id.qrCode_Iv);
        expiringTv = findViewById(R.id.expiringTv);
        qrCodeMore = findViewById(R.id.qrCodeMore);
    }

    @Override
    public void initDate() {
        super.initDate();
        mPresenter.requestUserInfoById();
        mPresenter.getRrCode();
    }

    @Override
    public void initListener() {
        super.initListener();
        mLeftBg.setOnClickListener(mClickListener);
        mEmailBg.setOnClickListener(mClickListener);
        mMobileBg.setOnClickListener(mClickListener);
        mHeardBg.setOnClickListener(mClickListener);
        qrCodeMore.setOnClickListener(mClickListener);
        qrCodeIv.setOnClickListener(mClickListener);
    }

    private CustomClickListener mClickListener = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            int id = view.getId();
            if (view.getId() == R.id.leftBg) {
                finish();
            } else if (view.getId() == R.id.emailBg) {
                Intent intent = new Intent(mContext, UserInfoEditActivity.class);
                intent.putExtra(UserConstants.Transmit.EDITTYPE, UserConstants.EditType.EMAILTYPE);
                intent.putExtra(UserConstants.Transmit.PAGERINFO, mEmail);
                startActivityForResult(intent, EMAILREQUEST);
            } else if (view.getId() == R.id.mobileBg) {
                //UserInfoEditMobileActivity.jumpUserInfoEditMobile(mContext);
            } else if (view.getId() == R.id.heardBg) {
                PortraitSettingActivity.jumpPortraitSetting(mContext);
            } else if (id == R.id.qrCodeMore || id == R.id.qrCode_Iv) {
                MyQrCodeActivity.jump(mContext, mUserInfoBean);
            }
        }
    };

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == EMAILREQUEST && resultCode == RESULT_OK) {
            if (data != null) {
                mEmail = data.getStringExtra(UserConstants.Transmit.EMAIL);
                mEmailTv.setText(mEmail);
            }
        }
    }

    @Override
    public void userInfoSuccess(UserInfoBean userInfoBean) {
        if (userInfoBean != null) {
            this.mUserInfoBean = userInfoBean;
            //头像
            SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERHEARD, userInfoBean.getUserAvatar());
            ImageLoaderManager.getInstance().disPlayImage(mContext, userInfoBean.getUserAvatar(), R.drawable.ic_contact_head, mHeadIv);

            //用户名
            nameTv.setText(userInfoBean.getMaskName());

            //middleNameTv.setText(userInfoBean.getRealName());
            //nric
            mNricTv.setText(userInfoBean.getNationalityName());

            //电话
            mMobileTv.setText(StringUtils.stringMask(userInfoBean.getMobileAreaCode(), userInfoBean.getMobile()));

            if (!TextUtils.isEmpty(userInfoBean.getIdExpiredDate())) {
                expiringTv.setText(TimeUtils.getTime(userInfoBean.getIdExpiredDate()));
            }else{
                expiringTv.setText("-");
            }
            //email
            mEmail = userInfoBean.getEmail();
            if (StringUtils.isValidString(mEmail)) {
                mEmailTv.setText(mEmail);
            } else {
                mEmailTv.setText(R.string.to_be_inputted);
            }

            //新增关联的父母/监护人账户信息
            UserInfoBean.ParentUserInfo parentUserInfo = userInfoBean.getParentUserInfo();
            if (parentUserInfo != null) {
                //0 Mother，1 Father，2 Legal Guardian
                switch (parentUserInfo.getRelationShip()) {
                    case "0":
                        RelationshipTv.setText(R.string.mother);
                        break;
                    case "1":
                        RelationshipTv.setText(R.string.father);
                        break;
                    default:
                        RelationshipTv.setText(R.string.legal_Guardian);
                        break;
                }

                NameTv.setText(parentUserInfo.getMaskName());
                MobileTv.setText(parentUserInfo.getMaskMobile());
                LinkTv.setText(TimeUtils.getTime(parentUserInfo.getLinkDate()));
            }
        }
    }

    @Override
    public void editSuccess(String gender) {
        if (TextUtils.equals(gender, UserConstants.Gender.Male)) {
            mGender = getResources().getStringArray(R.array.gender_select)[1];
        } else if (TextUtils.equals(gender, UserConstants.Gender.Female)) {
            mGender = getResources().getStringArray(R.array.gender_select)[2];
        } else {
            mGender = getResources().getStringArray(R.array.gender_select)[0];
        }
//        mGenderTv.setText(mGender);
    }

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    public void qrCodeSuccess(UserQrCodeBean bean) {
        qrCodeTask = Observable.create((ObservableOnSubscribe<Bitmap>) emitter -> {
                    Bitmap qrCodeBitmap = QRCodeEncoder.syncEncodeQRCode(bean.getQrCode(), ExtensionsKt.pt2px(48));
                    emitter.onNext(qrCodeBitmap);

                }).subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(bitmap -> {
                    qrCodeIv.setImageBitmap(bitmap);
                    if (mUserInfoBean != null) mUserInfoBean.setQrCode(bean.getQrCode());
                    qrCodeTask.dispose();
                });
    }

    @Override
    public void getRegion(int currentPosition) {
        if (mRegionPicker != null) {
            mRegionPicker.dismiss();
        }

        String genderStr = "";
        if (currentPosition == 0) {
            genderStr = "0";
        } else if (currentPosition == 1) {
            genderStr = "2";
        } else if (currentPosition == 2) {
            genderStr = "1";
        }
        mPresenter.requestEditInfo("", "", genderStr, "", "");
    }

    @Override
    public void cancel() {
        if (mRegionPicker != null) {
            mRegionPicker.dismiss();
        }
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent != null) {
            String mobile = intent.getStringExtra(UserConstants.Transmit.MOBILE);
            String mobileAreaCode = intent.getStringExtra(UserConstants.Transmit.MOBILEAREACODE);
            mMobileTv.setText(StringUtils.stringMask(mobileAreaCode, mobile));
        }
    }
}
