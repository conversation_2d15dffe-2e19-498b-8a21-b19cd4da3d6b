package om.rrtx.mobile.homemodule.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import java.util.ArrayList;
import java.util.List;

import om.rrtx.mobile.homemodule.HomeConstants;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.bean.HomeTabBean;
import om.rrtx.mobile.rrtxcommon.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseHolder;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * 首页显示Tab的布局
 */
public class HomeRvAdapter extends RecyclerView.Adapter<BaseHolder> {
    private List<HomeTabBean> mList;
    private Context mContext;
    private RVAdapterItemClickListener<HomeTabBean> mItemClickListener;


    public HomeRvAdapter(Context context) {
        mContext = context;
        initData(context);
    }

    /**
     * 初始化数据
     *
     * @param context 上下文
     */
    private void initData(Context context) {
        mList = new ArrayList<>();
        //账户类型 0-常规账户 1-亲子账户
        String accountType= (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ACCOUNTTYPE,"0");
        if (!"1".equals(accountType))
        {
            //充值
            mList.add(new HomeTabBean(R.drawable.home_ic_topup,
                    context.getResources().getString(R.string.home_bank_to_OneMoney),HomeConstants.FunctionType.TOP_UP));

            //提现
            mList.add(new HomeTabBean(R.drawable.home_ic_withdraw,
                    context.getResources().getString(R.string.home_oneMoney_to_Bank),HomeConstants.FunctionType.WITH_DRAW));
        }

        //转账
        mList.add(new HomeTabBean(R.drawable.home_ic_send_money,
                context.getResources().getString(R.string.send_money),HomeConstants.FunctionType.SEND_MONEY));

        //aa
//        mList.add(new HomeTabBean(R.drawable.icon_aa_svg,
//                context.getResources().getString(R.string.aa_title_split_bill)));

        mList.add(new HomeTabBean(R.drawable.home_ic_buy_airtime,
                context.getResources().getString(R.string.home_buy_Airtime_Bundle), HomeConstants.FunctionType.AIRTIME));

        //缴费
        mList.add(new HomeTabBean(R.drawable.home_ic_bill_pay_ic,
                context.getResources().getString(R.string.home_btn_billpayment),HomeConstants.FunctionType.Bill_Pay));

        if (!"1".equals(accountType))
        {
            mList.add(new HomeTabBean(R.drawable.home_ic_more,
                    context.getResources().getString(R.string.more), HomeConstants.FunctionType.MORE));
        }

        if ("1".equals(accountType))
        {
            //商户
            mList.add(new HomeTabBean(R.drawable.ic_merchant,
                    context.getResources().getString(R.string.home_btn_merchant),HomeConstants.FunctionType.Nearby_Mer));
        }

        //历史
//        mList.add(new HomeTabBean(R.drawable.home_tab_hiatory,
//                context.getResources().getString(R.string.home_btn_history)));
    }

    @NonNull
    @Override
    public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View rootView = LayoutInflater.from(mContext).inflate(R.layout.home_item_home_tab, parent, false);
        return new BaseHolder(rootView);
    }

    @Override
    public void onBindViewHolder(@NonNull BaseHolder holder, int position) {
        HomeTabBean homeTabBean = mList.get(position);

        ImageView icon = holder.getView(R.id.icon);
        icon.setImageResource(homeTabBean.getTabRes());

        TextView titleTv = holder.getView(R.id.titleTv);
        titleTv.setText(homeTabBean.getTabName());

        holder.itemView.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                if (mItemClickListener != null) {
                    mItemClickListener.itemClickListener(homeTabBean, position);
                }
            }
        });
    }

    public void setItemClickListener(RVAdapterItemClickListener<HomeTabBean> itemClickListener) {
        mItemClickListener = itemClickListener;
    }

    @Override
    public int getItemCount() {
        return mList == null ? 0 : mList.size();
    }
}
