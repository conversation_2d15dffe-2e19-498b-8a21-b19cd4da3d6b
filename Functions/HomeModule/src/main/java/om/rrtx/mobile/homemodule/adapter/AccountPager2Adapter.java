package om.rrtx.mobile.homemodule.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.RecyclerView;

import java.util.List;

import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.functioncommon.bean.BalanceBean;
import om.rrtx.mobile.rrtxcommon.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseHolder;
import om.rrtx.mobile.rrtxcommon1.utils.CurrencyUtils;
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.TimeUtils;

/**
 * <AUTHOR> zfw
 * @time : 2023/9/11 15:33
 * @desc : 我的账户--》账户资产
 */
public class AccountPager2Adapter extends RecyclerView.Adapter<BaseHolder> {
    private List<BalanceBean> mList;
    private Context mContext;

    public AccountPager2Adapter(Context context, List list) {
        mList = list;
        mContext = context;
    }

    @NonNull
    @Override
    public BaseHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View inflate = LayoutInflater.from(parent.getContext()).inflate((R.layout.home_account_balance_item), parent, false);
        DataBindingUtil.bind(inflate);
        return new BaseHolder(inflate);
    }

    @Override
    public void onBindViewHolder(@NonNull BaseHolder holder, int position) {
        BalanceBean bean = mList.get(position);
        LinearLayout lin = holder.getView(R.id.lin);
        ConstraintLayout constraintLayout = holder.getView(R.id.view_junior);
        RelativeLayout consview = holder.getView(R.id.consview);
        //账户类型 0-常规账户 1-亲子账户
        String accountType= (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ACCOUNTTYPE,"0");
        if ("1".equals(accountType))
        {
            lin.setVisibility(View.GONE);
            constraintLayout.setVisibility(View.VISIBLE);
            consview.setVisibility(View.GONE);
        }else{
            lin.setVisibility(View.VISIBLE);
            constraintLayout.setVisibility(View.GONE);
            consview.setVisibility(View.VISIBLE);
        }
            LinearLayout ll = holder.getView(R.id.activateNowLl);
        if (bean.isNeedActivate()) {
            ll.setVisibility(View.VISIBLE);
            holder.setTextViewStr(R.id.accountTv, CurrencyUtils.setCurrency(mContext, bean.getCurrType()) + " " + mContext.getString(R.string.home_label_account));
            TextView activateNowTv = holder.getView(R.id.activateNowTv);
            activateNowTv.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (listener != null) {
                        listener.toActivateNow(CurrencyUtils.setCurrency(mContext, bean.getCurrType()));
                    }
                }
            });
        } else {
            ll.setVisibility(View.GONE);
            LinearLayout topUpLl = holder.getView(R.id.topUpLl);
            LinearLayout withLl = holder.getView(R.id.withLl);
            LinearLayout recordLl = holder.getView(R.id.recordLl);
            ConstraintLayout rootView = holder.getView(R.id.topBg);
            View getBalance = holder.getView(R.id.bg_getBalance);
            View getBalance1 = holder.getView(R.id.bg_getBalance1);
            holder.setTextViewStr(R.id.curTitleTv, CurrencyUtils.setCurrency(mContext, bean.getCurrType()) + " " + mContext.getString(R.string.account));

            topUpLl.setOnClickListener(new CustomClickListener() {
                @Override
                public void onSingleClick(View view) {
                    if (listener != null) {
                        listener.toTopUP(CurrencyUtils.setCurrency(mContext, bean.getCurrType()));
                    }
                }
            });

            withLl.setOnClickListener(new CustomClickListener() {
                @Override
                public void onSingleClick(View view) {
                    if (listener != null) {
                        listener.toWithdraw(CurrencyUtils.setCurrency(mContext, bean.getCurrType()));
                    }
                }
            });
            recordLl.setOnClickListener(new CustomClickListener() {
                @Override
                public void onSingleClick(View view) {
                    if (listener != null) {
                        listener.toTransfer(CurrencyUtils.setCurrency(mContext, bean.getCurrType()));
                    }
                }
            });
            rootView.setOnClickListener(new CustomClickListener() {
                @Override
                public void onSingleClick(View view) {
                    if (listener != null) {
                        listener.toRecords(CurrencyUtils.setCurrency(mContext, bean.getCurrType()));
                    }
                }
            });

            getBalance.setOnClickListener(new CustomClickListener() {
                @Override
                public void onSingleClick(View view) {
                    listener.getBalance(CurrencyUtils.setCurrency(mContext, bean.getCurrType()), bean.getLastBalanceEnquiryTime());
                }
            });
            getBalance1.setOnClickListener(new CustomClickListener() {
                @Override
                public void onSingleClick(View view) {
                    listener.getBalance(CurrencyUtils.setCurrency(mContext, bean.getCurrType()), bean.getLastBalanceEnquiryTime());
                }
            });

            TextView tv_lastQueryTime = holder.getView(R.id.tv_lastQueryTime);
            TextView tv_lastQueryTime1 = holder.getView(R.id.tv_lastQueryTime1);
            String lastBalanceEnquiryTime = bean.getLastBalanceEnquiryTime();
            if (StringUtils.isValidString(lastBalanceEnquiryTime)) {
                tv_lastQueryTime.setText(ResourceHelper.getString(R.string.Last_m) + TimeUtils.getTime(lastBalanceEnquiryTime));
                tv_lastQueryTime.setVisibility(View.VISIBLE);
                tv_lastQueryTime1.setText(ResourceHelper.getString(R.string.Last_m) + TimeUtils.getTime(lastBalanceEnquiryTime));
                tv_lastQueryTime1.setVisibility(View.VISIBLE);
            } else {
                tv_lastQueryTime.setVisibility(View.GONE);
                tv_lastQueryTime1.setVisibility(View.GONE);
            }

            TextView tv_fees = holder.getView(R.id.tv_fees);
            TextView tv_fees1 = holder.getView(R.id.tv_fees1);
//            ZWL 20.00 / Per Charge
            tv_fees.setText(bean.getCurrType() + " " + bean.getTotalAmt() + " " + ResourceHelper.getString(R.string.per_Charge));
            tv_fees1.setText(bean.getCurrType() + " " + bean.getTotalAmt() + " " + ResourceHelper.getString(R.string.per_Charge));
        }
    }

    @Override
    public int getItemCount() {
        return mList.size();
    }


    public OnClickListener listener;

    public void setOnClickListener(OnClickListener listener) {
        this.listener = listener;
    }

    public interface OnClickListener {
        void toTopUP(String currency);

        void toWithdraw(String currency);

        void toRecords(String currency);

        void toTransfer(String currency);

        void toActivateNow(String currency);

        void getBalance(String currency, String lastBalanceEnquiryTime);
    }
}
