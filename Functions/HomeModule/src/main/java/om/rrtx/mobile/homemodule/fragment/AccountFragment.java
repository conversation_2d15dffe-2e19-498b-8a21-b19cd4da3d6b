package om.rrtx.mobile.homemodule.fragment;

import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.lifecycle.ViewModelProvider;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.ViewPager2;

import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;
import com.kapp.xmarketing.ShowMarketingManager;

import java.util.Iterator;
import java.util.List;

import butterknife.BindView;
import de.hdodenhof.circleimageview.CircleImageView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.PayMethodLiveData;
import om.rrtx.mobile.functioncommon.bean.BalanceBean;
import om.rrtx.mobile.functioncommon.bean.BalanceCertificateBean;
import om.rrtx.mobile.functioncommon.bean.CashierOrderInfoBean;
import om.rrtx.mobile.functioncommon.bean.CurrencyAccountListBean;
import om.rrtx.mobile.functioncommon.bean.PaymentBean;
import om.rrtx.mobile.functioncommon.callback.CashierCallBack;
import om.rrtx.mobile.functioncommon.dialog.PayPsdBottomFragment;
import om.rrtx.mobile.functioncommon.utils.CashierManager;
import om.rrtx.mobile.functioncommon.utils.PaymentTypeHelper;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.R2;
import om.rrtx.mobile.homemodule.activity.JuniorPersonalInfoActivity;
import om.rrtx.mobile.homemodule.activity.MyQrCodeActivity;
import om.rrtx.mobile.homemodule.activity.PersonalInfoActivity;
import om.rrtx.mobile.homemodule.activity.PortraitSettingActivity;
import om.rrtx.mobile.homemodule.activity.ShowInviteActivity;
import om.rrtx.mobile.homemodule.adapter.AccountPager2Adapter;
import om.rrtx.mobile.homemodule.bean.ActivityBean;
import om.rrtx.mobile.rrtxcommon1.bean.UserInfoBean;
import om.rrtx.mobile.homemodule.bean.UserQrCodeBean;
import om.rrtx.mobile.homemodule.presenter.AccountFragmentPresenter;
import om.rrtx.mobile.homemodule.view.AccountFragmentView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.MVPWithLazyBaseFragment;
import om.rrtx.mobile.rrtxcommon1.bean.AppDictListBean;
import om.rrtx.mobile.rrtxcommon1.image.ImageLoaderManager;
import om.rrtx.mobile.rrtxcommon1.kotlin.ExtensionsKt;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 我的页面Fragment
 */
public class AccountFragment extends MVPWithLazyBaseFragment<AccountFragmentView, AccountFragmentPresenter>
        implements AccountFragmentView {
    @BindView(R2.id.securityBg)
    View mSecurityBg;
    @BindView(R2.id.userNameTv)
    TextView mUserNameTv;
    @BindView(R2.id.cardIdTv)
    TextView mCardIdTv;
    @BindView(R2.id.tv_junior)
    TextView tv_junior;
    @BindView(R2.id.historyBg)
    View mHistoryBg;

    @BindView(R2.id.loyalty_card_Bg)
    View mloyaltyCardBg;
    @BindView(R2.id.loyalty_card_Iv)
    ImageView loyalty_card_Iv;
    @BindView(R2.id.loyalty_card_Tv)
    TextView loyalty_card_Tv;

    @BindView(R2.id.bankCardBg)
    View mBankCardBg;
    @BindView(R2.id.bankCardIv)
    ImageView bankCardIv;
    @BindView(R2.id.bankCardTv)
    TextView bankCardTv;

    @BindView(R2.id.inviteBg)
    View mInviteBg;
    @BindView(R2.id.inviteIv)
    ImageView inviteIv;
    @BindView(R2.id.inviteTv)
    TextView inviteTv;

    @BindView(R2.id.headIv)
    CircleImageView mHeadIv;
    @BindView(R2.id.codeIv)
    ImageView mCodeIv;
    @BindView(R2.id.topBg)
    View mTopBg;
    @BindView(R2.id.accountView)
    ConstraintLayout mAccountView;
    @BindView(R2.id.rewardTv)
    TextView mRewardTv;
    @BindView(R2.id.accountVp2)
    ViewPager2 mAccountVp2;

    //密码弹框
    private PayMethodLiveData mPayMethodLiveData;
    private PayPsdBottomFragment mPayPsdBottomFragment;
    private BalanceBean mBalanceBean;

    CurrencyAccountListBean mCurrencyAccountListBean;
    String mOrigBalanceListStr;
    AppDictListBean mAppDictListBean;
    int mPosition = 0;
    private String mLastBalanceEnquiryTime;

    /**
     * 当前选中币种
     */
    private String curCurency;
    private CashierManager mCashierManager;

    @Override
    protected int createViewLayoutId() {
        return R.layout.home_fragment_accrount;
    }

    @Override
    public AccountFragmentPresenter createPresenter() {
        return new AccountFragmentPresenter(mContext);
    }

    @Override
    protected void initView(View rootView) {
        ImmersionBar.with(this)
                .statusBarDarkFont(true, 0.2f)
                .statusBarView(R.id.statusView, mAccountView)
                .init();

        //账户类型 0-常规账户 1-亲子账户
        String accountType = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ACCOUNTTYPE, "0");
        if ("1".equals(accountType)) {
            mBankCardBg.setVisibility(View.GONE);
            bankCardTv.setVisibility(View.GONE);
            bankCardIv.setVisibility(View.GONE);

            mloyaltyCardBg.setVisibility(View.VISIBLE);
            loyalty_card_Iv.setVisibility(View.VISIBLE);
            loyalty_card_Tv.setVisibility(View.VISIBLE);

            mInviteBg.setVisibility(View.GONE);
            inviteIv.setVisibility(View.GONE);
            inviteTv.setVisibility(View.GONE);
            mRewardTv.setVisibility(View.GONE);

            tv_junior.setText(getString(R.string.account_type_junior));
            String userName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERNAME, "");
            if (!TextUtils.isEmpty(userName)) {
                mUserNameTv.setText(userName);
            }

            // 获取当前LayoutParams
            ViewGroup.LayoutParams layoutParams = mAccountVp2.getLayoutParams();
            // 设置宽度为200像素
            layoutParams.height = ExtensionsKt.pt2px(200);
            // 将修改后的LayoutParams应用到View
            mAccountVp2.setLayoutParams(layoutParams);
        } else {
            mBankCardBg.setVisibility(View.VISIBLE);
            bankCardTv.setVisibility(View.VISIBLE);
            bankCardIv.setVisibility(View.VISIBLE);

            mloyaltyCardBg.setVisibility(View.VISIBLE);
            loyalty_card_Iv.setVisibility(View.VISIBLE);
            loyalty_card_Tv.setVisibility(View.VISIBLE);

            mInviteBg.setVisibility(View.VISIBLE);
            inviteIv.setVisibility(View.VISIBLE);
            inviteTv.setVisibility(View.VISIBLE);
            mRewardTv.setVisibility(View.GONE);

            tv_junior.setText(getString(R.string.account_type));
            String userName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERNAME, "");
            if (!TextUtils.isEmpty(userName)) {
                mUserNameTv.setText(userName);
            }

            // 获取当前LayoutParams
            ViewGroup.LayoutParams layoutParams = mAccountVp2.getLayoutParams();
            // 设置宽度为200像素
            layoutParams.height = ExtensionsKt.pt2px(370);
            // 将修改后的LayoutParams应用到View
            mAccountVp2.setLayoutParams(layoutParams);
        }
    }

    @Override
    public void initDate() {
        mPayMethodLiveData = new ViewModelProvider(getActivity()).get(PayMethodLiveData.class);
        mPayMethodLiveData.getPsdFinish().removeObservers(getActivity());
        mPayMethodLiveData.mDismiss.observe(getActivity(), aBoolean -> mPayPsdBottomFragment.dismiss());
        mPayMethodLiveData.getPsdFinish().observe(getActivity(), psd -> {
            if (TextUtils.isEmpty(psd))
                return;
            String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
            mPresenter.requestBalancePay(CommonConstants.CashierPaymentType.Query_BALANCE,
                    psd,
                    curCurency,
                    CommonConstants.TransType.Query_BALANCE,
                    userId);

            if (mPayPsdBottomFragment != null) {
                mPayPsdBottomFragment.cancelPassword();
            }
        });
    }

    @Override
    public void initListener() {
        super.initListener();
        mSecurityBg.setOnClickListener(mAccountCustom);
        mHistoryBg.setOnClickListener(mAccountCustom);
        mBankCardBg.setOnClickListener(mAccountCustom);
        mCodeIv.setOnClickListener(mAccountCustom);
        mTopBg.setOnClickListener(mAccountCustom);
        mHeadIv.setOnClickListener(mAccountCustom);
        mInviteBg.setOnClickListener(mAccountCustom);
        mloyaltyCardBg.setOnClickListener(mAccountCustom);

    }

    private CustomClickListener mAccountCustom = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.securityBg) {
                ARouter.getInstance()
                        .build(ARouterPath.SecurityPath.SecurityCenterActivity)
                        .navigation();
            } else if (view.getId() == R.id.historyBg) {
                ARouter.getInstance()
                        .build(ARouterPath.TransferPath.QueryHistoryActivity)
                        .navigation();
            } else if (view.getId() == R.id.loyalty_card_Bg) {
                String userName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERNAME, "");
                String realName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.REALNAME, "");
                String userMobile = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERMOBILE, "");
                String userCardId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
                String currencyList = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ACTIVATE_CURRENCY, "");
                Log.e("sdk=", "userName=" + userName + "=realName=" + realName + "=userMobile=" + userMobile + "=userCardId=" + userCardId + "=currencyList=" + currencyList);
                ShowMarketingManager.getInstance().jumpCardWalletActivity(userCardId, realName, userName, userMobile, "", currencyList);

            } else if (view.getId() == R.id.bankCardBg) {
                ARouter.getInstance()
                        .build(ARouterPath.TransferPath.BankCardListActivity)
                        .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP)
                        .navigation();
            } else if (view.getId() == R.id.codeIv) {
                mPresenter.requestQrCode();
            } else if (view.getId() == R.id.topBg) {
                //账户类型 0-常规账户 1-亲子账户
                String accountType = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ACCOUNTTYPE, "0");
                if ("1".equals(accountType)) {
                    JuniorPersonalInfoActivity.jumpPersonalInfo(mContext);
                } else {
                    PersonalInfoActivity.jumpPersonalInfo(mContext);
                }
            } else if (view.getId() == R.id.headIv) {
                PortraitSettingActivity.jumpPortraitSetting(mContext);
            } else if (view.getId() == R.id.inviteBg) {
                ShowInviteActivity.jumpShowInvite(mContext);
            }
        }
    };


    @Override
    public void lazyDate() {
        super.lazyDate();
        mPresenter.requestAccountCurrency(CommonConstants.TransType.Query_BALANCE);
        mPresenter.requestCurrencyList();
        //请求是否有活动返现
        //mPresenter.requestIsHasActivity();

    }

    @Override
    public void onResume() {
        super.onResume();
        String mobile = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERMOBILE, "");
        String mobileAreaCode = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.MOBILEAREACODE, "");
        String cardMark = StringUtils.stringMask(mobileAreaCode, mobile);
        mCardIdTv.setText(cardMark);

        String userHeard = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERHEARD, "");
        if (!TextUtils.isEmpty(userHeard)) {
            ImageLoaderManager.getInstance().disPlayImage(mContext, userHeard, R.drawable.ic_contact_head, mHeadIv);
        }
    }

    @Override
    public void currencyAccountSuccess(CurrencyAccountListBean bean) {
        mCurrencyAccountListBean = bean;
        mOrigBalanceListStr = new Gson().toJson(bean);
        setViewPagerData();
    }

    @Override
    public void qrCodeSuccess(UserQrCodeBean sResData) {
        String mobile = SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERMOBILE, "").toString();
        String name = SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.REALNAME, "").toString();
        UserInfoBean bean = new UserInfoBean();
        bean.setQrCode(sResData.getQrCode());
        bean.setMobile(mobile);
        bean.setMobileAreaCode(getString(R.string.nommal_263));
        bean.setMaskName(name);
        MyQrCodeActivity.jump(mContext, bean);
    }

    @Override
    public void isHasActivitySuccess(ActivityBean sResData) {
        if (TextUtils.equals(sResData.getHasActivity(), BaseConstants.HasActivity.HasActivity)) {
            mRewardTv.setVisibility(View.GONE);
        } else {
            mRewardTv.setVisibility(View.GONE);
        }
    }

    /**
     * 获取币种列表
     *
     * @param dictListBean 余额实体类
     */
    @Override
    public void getCurListSuccess(AppDictListBean dictListBean) {
        mAppDictListBean = dictListBean;
        setViewPagerData();
    }

    @Override
    public void checkBalanceOrderSuccess(BalanceBean bean) {
        mBalanceBean = bean;
        //mPayPsdBottomFragment = PayPsdBottomFragment.newInstance("");
        //mPayPsdBottomFragment.show(getChildFragmentManager(), PayPsdBottomFragment.class.getSimpleName());

        CashierOrderInfoBean cashierOrderInfoBean = new CashierOrderInfoBean.Builder()
                .setCurrency(bean.getCurrType())
                .setOrderAmt(bean.getTotalAmt())
                .setTransType(CommonConstants.TransType.Query_BALANCE)
                .setOrderInfo(getString(R.string.balance_Enquiry))
                .setPayType(CommonConstants.CashierPaymentType.Query_BALANCE)
                .builder();
        String isBalanceEnough = bean.isEnoughPay().equals("true") ? "1" : "0";
        PaymentBean paymentBean = PaymentTypeHelper.packingPayMethod(bean.getCurrType(), isBalanceEnough, bean.getFeeAmt(), bean.getTaxAmt());
        new ViewModelProvider(getActivity()).get(PayMethodLiveData.class).getPaymentTypeLV().setValue(paymentBean);
        mCashierManager = new CashierManager((AppCompatActivity) getActivity(), new Gson().toJson(cashierOrderInfoBean), new CashierCallBack() {
            @Override
            public void paymentFailed(@NonNull String message) {

            }

            @Override
            public void cancelOrderPay() {
            }

            @Override
            public void forgotCallBack() {
            }

            @Override
            public void paymentSuccess(String dataJson) {
                mCashierManager.dismiss();
                BalanceCertificateBean balanceCertificateBean = new Gson().fromJson(dataJson, BalanceCertificateBean.class);
                balanceCertificateBean.setCurrency(curCurency);
                balanceCertificateBean.setLastBalanceEnquiryTime(mLastBalanceEnquiryTime);
                if (mCurrencyAccountListBean != null) {
                    ARouter.getInstance()
                            .build(ARouterPath.TransferPath.HistoryAccountActivity)
                            .withString(BaseConstants.Transmit.BALANCE_CERTIFICATE, new Gson().toJson(balanceCertificateBean))
                            .withInt(BaseConstants.Transmit.TABPOS, mPosition)
                            .navigation();
                }
            }
        });
        //mCashierManager.showCashierDialog();

    }

    @Override
    public void getBankNumSuccess(String num) {
        if (StringUtils.isValidString(num)) {
            Integer number = Integer.valueOf(num);
            if (number > 0) {
                ARouter.getInstance().build(ARouterPath.TopUp.TopUpActivity)
                        .withString(BaseConstants.Transmit.CURRENCY, curCurency)
                        .navigation();
            } else {
                ToastUtil.show(mContext, getString(R.string.no_Linked_Bank_Accounts));
            }
        }
    }

    @Override
    public void requestBalancePaySuccess(BalanceCertificateBean balanceCertificateBean) {
        if (mPayPsdBottomFragment != null) {
            mPayPsdBottomFragment.dismiss();
        }
        balanceCertificateBean.setCurrency(curCurency);
        balanceCertificateBean.setLastBalanceEnquiryTime(mLastBalanceEnquiryTime);
        if (mCurrencyAccountListBean != null) {
            ARouter.getInstance()
                    .build(ARouterPath.TransferPath.HistoryAccountActivity)
                    .withString(BaseConstants.Transmit.BALANCE_CERTIFICATE, new Gson().toJson(balanceCertificateBean))
                    .withInt(BaseConstants.Transmit.TABPOS, mPosition)
                    .navigation();
        }
    }

    private void setViewPagerData() {
        if (mCurrencyAccountListBean != null && mAppDictListBean != null) {
            List<BalanceBean> mList = mCurrencyAccountListBean.getBalanceList();
            Iterator<AppDictListBean.AppDictBean> iterator = mAppDictListBean.getAppDictCodeList().iterator();

            while (iterator.hasNext()) {
                AppDictListBean.AppDictBean item = iterator.next();
                for (BalanceBean bean : mCurrencyAccountListBean.getBalanceList()
                ) {
                    if (bean.getCurrType().equals(item.getDictValue())) {
                        iterator.remove();
                    }
                }
            }
            for (AppDictListBean.AppDictBean bean : mAppDictListBean.getAppDictCodeList()) {
                BalanceBean balanceBean = new BalanceBean();
                balanceBean.setNeedActivate(true);
                balanceBean.setCurrType(bean.getDictLabel());
                mList.add(balanceBean);
            }

            AccountPager2Adapter adapter = new AccountPager2Adapter(mContext, mList);
            RecyclerView recyclerView = (RecyclerView) mAccountVp2.getChildAt(0);
            if (mList != null && mList.size() == 1) {
                recyclerView.setPadding(30, 0, 30, 0);
            } else {
                recyclerView.setPadding(30, 0, 60, 0);
            }
            recyclerView.setClipToPadding(false);
            mAccountVp2.setAdapter(adapter);
            mAccountVp2.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {

                @Override
                public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                    super.onPageScrolled(position, positionOffset, positionOffsetPixels);
                }


                @Override
                public void onPageSelected(int position) {
                    mPosition = position;
                    RecyclerView recyclerView = (RecyclerView) mAccountVp2.getChildAt(0);
                    if (mList != null && mList.size() > 1) {
                        if (position == 0) {
                            recyclerView.setPadding(30, 0, 60, 0);
                        } else {
                            recyclerView.setPadding(60, 0, 60, 0);
                        }
                    }
                    recyclerView.setClipToPadding(false);
                    super.onPageSelected(position);

                }


                @Override
                public void onPageScrollStateChanged(int state) {
                    super.onPageScrollStateChanged(state);

                }
            });
            adapter.setOnClickListener(new AccountPager2Adapter.OnClickListener() {
                @Override
                public void toTopUP(String currency) {
                    mPresenter.getBankNum();
                    curCurency = currency;
                }

                @Override
                public void toWithdraw(String currency) {
                    ARouter.getInstance().build(ARouterPath.TransferPath.WithdrawalActivity)
                            .withString(BaseConstants.Transmit.CURRENCY, currency)
                            .navigation();
                }

                @Override
                public void toTransfer(String currency) {
                    //转账页面
                    ARouter.getInstance()
                            .build(ARouterPath.TransferPath.TransferActivity)
                            .withString(BaseConstants.Transmit.CURRENCY, currency)
                            .withString(BaseConstants.HomeJumpType.JUMP_HOME, BaseConstants.OrderSourceType.ACCOUNT_SOURCE)
                            .navigation();
                }

                @Override
                public void toRecords(String currency) {
                }

                @Override
                public void toActivateNow(String currency) {
                    ARouter.getInstance()
                            .build(ARouterPath.SecurityPath.PinInputActivity)
                            .withString(BaseConstants.Transmit.CURRENCY, currency)
                            .navigation();
                }

                @Override
                public void getBalance(String currency, String lastBalanceEnquiryTime) {
                    // 65 余额查询
                    mLastBalanceEnquiryTime = lastBalanceEnquiryTime;
                    curCurency = currency;
                    //个人及商户的查询业务跳过交易信息确认，发起后直接输入PIN码
                    //mPresenter.checkBalanceOrder(currency, CommonConstants.TransType.Query_BALANCE);
                    mPayPsdBottomFragment = PayPsdBottomFragment.newInstance("");
                    mPayPsdBottomFragment.show(getChildFragmentManager(), PayPsdBottomFragment.class.getSimpleName());

                }
            });
        }

    }
}
