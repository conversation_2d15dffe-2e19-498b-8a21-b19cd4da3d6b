package om.rrtx.mobile.homemodule.activity;

import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.appcompat.widget.AppCompatTextView;

import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import java.util.HashMap;
import java.util.Map;

import butterknife.BindView;
import om.rrtx.mobile.functioncommon.activity.CustomFlutterActivity;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.R2;
import om.rrtx.mobile.homemodule.bean.CommonSetBean;
import om.rrtx.mobile.homemodule.presenter.ContactUsPresenter;
import om.rrtx.mobile.homemodule.view.ContactUsView;
import om.rrtx.mobile.rrtxcommon1.bean.FeedbackRouteBean;
import om.rrtx.mobile.homemodule.dialog.PhoneDialog;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.LocaleManager;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

public class ContactUsActivity extends BaseSuperActivity<ContactUsView, ContactUsPresenter> implements ContactUsView {

    @BindView(R2.id.statusView)
    View mStatusView;
    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.twiterIv)
    ImageView twiterIv;
    @BindView(R2.id.faceIv)
    ImageView faceIv;
    @BindView(R2.id.callUsTv)
    AppCompatTextView mCallUsTv;
    @BindView(R2.id.feedBackTv)
    AppCompatTextView mFeedBackTv;
    @BindView(R2.id.showPhoneTv)
    AppCompatTextView mShowPhoneTv;
    private PhoneDialog mPhoneDialog;
    private String mobile, facebookUrl, twitterUrl;

    public static void jumpContactUs(Context context) {
        Intent intent = new Intent(context, ContactUsActivity.class);
        context.startActivity(intent);
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    protected int createContentView() {
        return R.layout.home_activity_contact_us;
    }

    @Override
    protected ContactUsPresenter createPresenter() {
        return new ContactUsPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.home_ic_back);

        mStatusView.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));
        mTitleTv.setText(R.string.contact_title_contact_us);

        mPresenter.requestCustomerService();
    }

    private CustomClickListener mCustomClickListener = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.callUsTv) {
                if (mPhoneDialog == null) {
                    mPhoneDialog = new PhoneDialog(mContext, mobile);
                }
                mPhoneDialog.show();
            } else if (view.getId() == R.id.leftBg) {
                finish();
            } else if (view.getId() == R.id.faceIv) {
                if (!TextUtils.isEmpty(facebookUrl)) {
                    openBrowser(mContext, facebookUrl);
                } else {
                    openBrowser(mContext, "https://m.facebook.com/onemoneyzw");
                }

                //ShowWebViewActivity.jumpShowWebView(mContext, "", "faceBook");
            } else if (view.getId() == R.id.twiterIv) {
                if (!TextUtils.isEmpty(twitterUrl)) {
                    openBrowser(mContext, twitterUrl);
                } else {
                    openBrowser(mContext, "https://twitter.com/OneMoneyZw");
                }

                //ShowWebViewActivity.jumpShowWebView(mContext, "https://twitter.com/OneMoneyZw", "twitter");
            } else if (view.getId() == R.id.feedBackTv) {
                String token = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.AUTHORIZATION, "");
                String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
                String userName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERNAME, "");
                Map uistyle = new HashMap();
                uistyle.put("submitBtnColor", "0xFFF3881E");
                Map params = new HashMap();
                params.put("token", token);
                params.put("userId", userId);
                params.put("userName", userName);
                FeedbackRouteBean routeBean = new FeedbackRouteBean();
                //获取相应的meta-data标签内容
                String url = BaseConstants.Urls.FLUTTER_FAT_URL;
                try {
                    ApplicationInfo applicationInfo = getPackageManager().getApplicationInfo(getPackageName(), PackageManager.GET_META_DATA);
                    String appType = applicationInfo.metaData.getString("appType");
                    if (!TextUtils.isEmpty(appType)) {
                        switch (appType) {
                            case "dev":
                            case "test":
                                url = BaseConstants.Urls.FLUTTER_TEST_URL;
                                break;
                            case "uat":
                                url = BaseConstants.Urls.FLUTTER_UAT_URL;
                                break;
                            case "fat":
                                url = BaseConstants.Urls.FLUTTER_FAT_URL;
                                break;
                            default:
                                url = BaseConstants.Urls.FLUTTER_FAT_URL;
                        }
                    }
                    routeBean.setBaseUrl(url);
                } catch (PackageManager.NameNotFoundException e) {
                    e.printStackTrace();
                }
                routeBean.setRouteName("feedback");
                routeBean.setPlatform("User");
                routeBean.setLanguage(LocaleManager.getInstance().getLanguage(mContext));
                routeBean.setUiStyle(uistyle);
                routeBean.setParams(params);
                //https://flutter.cn/docs/development/add-to-app/android/add-flutter-screen
                Intent build = CustomFlutterActivity
                        .withNewEngine(CustomFlutterActivity.class)
                        .initialRoute(new Gson().toJson(routeBean))
                        .build(mContext);
                mContext.startActivity(build);
            }
        }
    };

    @Override
    public void initListener() {
        super.initListener();
        mFeedBackTv.setOnClickListener(mCustomClickListener);
        mCallUsTv.setOnClickListener(mCustomClickListener);
        mLeftBg.setOnClickListener(mCustomClickListener);
        faceIv.setOnClickListener(mCustomClickListener);
        twiterIv.setOnClickListener(mCustomClickListener);
    }

    private void openBrowser(Context context, String url) {
        Uri uri = Uri.parse(url);
        Intent intent = new Intent();
        intent.setAction("android.intent.action.VIEW");
        intent.setData(uri);
        startActivity(Intent.createChooser(intent, "请选择浏览器"));
    }

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
        String showPhoneStr = getString(R.string.contact_label_service_time) + "10:00 - 18:00";
        mShowPhoneTv.setText(showPhoneStr);
    }

    @Override
    public void getInfo(CommonSetBean setBean) {
        String showPhoneStr = getString(R.string.contact_label_service_time) + setBean.getServiceTime();
        mShowPhoneTv.setText(showPhoneStr);
        mobile = setBean.getTelephone();
        facebookUrl = setBean.getFacebookUrl();
        twitterUrl = setBean.getTwitterUrl();
    }
}
