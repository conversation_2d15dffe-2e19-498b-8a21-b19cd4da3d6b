package om.rrtx.mobile.homemodule.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.text.TextUtils;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.github.chrisbanes.photoview.PhotoView;
import com.gyf.immersionbar.ImmersionBar;
import com.tbruyelle.rxpermissions2.RxPermissions;

import java.io.File;

import butterknife.BindView;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.R2;
import om.rrtx.mobile.homemodule.bean.PortraitSettingBean;
import om.rrtx.mobile.homemodule.dialog.SelectPictureDialog;
import om.rrtx.mobile.homemodule.presenter.PortraitSettingPresenter;
import om.rrtx.mobile.homemodule.view.PortraitSettingView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.PictureHelper;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 */
public class PortraitSettingActivity extends BaseSuperActivity<PortraitSettingView, PortraitSettingPresenter>
        implements PortraitSettingView, SelectPictureDialog.SelectPictureCallBack, PictureHelper.SnapshotCallBack {

    private final String photoName = "photo.png";

    @BindView(R2.id.statusView)
    View mStatusView;
    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.rightBg)
    View mRightBg;
    @BindView(R2.id.rightIv)
    ImageView mRightIv;
    @BindView(R2.id.photoView)
    PhotoView mPhotoView;
    private SelectPictureDialog mSelectPictureDialog;
    private PictureHelper mPictureHelper;
    private Bitmap mShowBitmap;

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    public static void jumpPortraitSetting(Context context) {
        Intent intent = new Intent(context, PortraitSettingActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int createContentView() {
        return R.layout.home_activity_portrait_setting;
    }

    @Override
    protected PortraitSettingPresenter createPresenter() {
        return new PortraitSettingPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.home_ic_back);

        mStatusView.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));
        mTitleTv.setText(R.string.edit_Portrait);
    }

    @Override
    public void initDate() {
        super.initDate();
        mPictureHelper = new PictureHelper(this);
    }

    @Override
    protected void onResume() {
        super.onResume();
        String userHeard = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERHEARD, "");
//        ImageLoaderManager.getInstance().disPlayImage(mContext, userHeard, R.drawable.ic_head_square_ye_side, mPhotoView);
        Glide.with(mContext)
                .load(userHeard)
                //加载成功前显示的图片
                .placeholder(R.drawable.ic_head_square_ye_side)
                //加载失败显示的图片
                .fallback(R.drawable.ic_head_square_ye_side)
                .skipMemoryCache(true)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(mPhotoView);

    }

    @Override
    public void initListener() {
        super.initListener();

        mRightBg.setOnClickListener(mCustomClickListener);
        mLeftBg.setOnClickListener(mCustomClickListener);
    }

    private CustomClickListener mCustomClickListener = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.rightBg) {
                mSelectPictureDialog = new SelectPictureDialog(mContext);
                mSelectPictureDialog.setSelectPictureCallBack(PortraitSettingActivity.this);
                mSelectPictureDialog.show();
            } else if (view.getId() == R.id.leftBg) {
                finish();
            }
        }
    };

    @Override
    public void openAlbum() {
        if (mPictureHelper != null) {
            mPictureHelper.openAlbum(photoName, photoName, mPhotoView.getWidth(), mPhotoView.getWidth(), this);
        }
        if (mSelectPictureDialog != null) {
            mSelectPictureDialog.dismiss();
        }
    }

    @Override
    public void openSnapshot() {
        if (mPictureHelper != null) {
            mPictureHelper.openSnapshot(photoName, photoName, mPhotoView.getWidth(), mPhotoView.getWidth(), this);
        }
        if (mSelectPictureDialog != null) {
            mSelectPictureDialog.dismiss();
        }
    }

    @Override
    public void snapshotCallBack(String mRequestCode, Bitmap bitmap, String mPath) {
        //回显头像
        mShowBitmap = bitmap;
        if (mShowBitmap != null) {
            Glide.with(this)
                    .asBitmap()
                    .load(bitmap)
                    .into(mPhotoView);
        }
        if (!TextUtils.isEmpty(mPath) && mShowBitmap != null) {
            mPresenter.uploadUserAvatar(new File(mPath));
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (mPictureHelper != null) {
            mPictureHelper.onActivityResult(requestCode, resultCode, data);
        }
    }

    @Override
    public void upLoadSuccess(PortraitSettingBean portraitSettingBean) {
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERHEARD, portraitSettingBean.getUserAvatar());
        Glide.with(mContext)
                .load(portraitSettingBean.getUserAvatar())
                //加载成功前显示的图片
                //.placeholder(R.drawable.ic_head_square_ye_side)
                //加载失败显示的图片
                //.fallback(R.drawable.ic_head_square_ye_side)
                .skipMemoryCache(true)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .into(mPhotoView);
    }

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (mShowBitmap != null) {
            mShowBitmap.recycle();
            mShowBitmap = null;
        }
    }
}
