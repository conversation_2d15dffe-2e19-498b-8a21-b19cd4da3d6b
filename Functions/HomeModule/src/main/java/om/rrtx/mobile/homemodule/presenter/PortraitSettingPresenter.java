package om.rrtx.mobile.homemodule.presenter;

import android.content.Context;
import android.text.TextUtils;

import java.io.File;

import om.rrtx.mobile.homemodule.bean.PortraitSettingBean;
import om.rrtx.mobile.homemodule.bean.PubBean;
import om.rrtx.mobile.homemodule.model.HomeModel;
import om.rrtx.mobile.homemodule.view.PortraitSettingView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * 首页Tab的p层
 */
public class PortraitSettingPresenter extends BasePresenter<PortraitSettingView> {
    private String TAG = PortraitSettingPresenter.class.getSimpleName();
    private Context mContext;
    private HomeModel mHomeModel;

    public PortraitSettingPresenter(Context context) {
        mHomeModel = new HomeModel();
        mContext = context;
    }

    /**
     * 请求账户余额接口
     */
    public void uploadUserAvatar(File userPhoto) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mHomeModel.uploadUserAvatar(userPhoto, new BaseObserver<PortraitSettingBean>(mContext) {
                @Override
                public void requestSuccess(PortraitSettingBean portraitSettingBean) {
                    if (getView() != null) {
                        getView().upLoadSuccess(portraitSettingBean);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        }else {
            mHomeModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mHomeModel.uploadUserAvatar(userPhoto, new BaseObserver<PortraitSettingBean>(mContext) {
                        @Override
                        public void requestSuccess(PortraitSettingBean portraitSettingBean) {
                            if (getView() != null) {
                                getView().upLoadSuccess(portraitSettingBean);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }
}
