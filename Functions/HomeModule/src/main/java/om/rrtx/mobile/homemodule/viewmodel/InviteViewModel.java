package om.rrtx.mobile.homemodule.viewmodel;

import android.content.Context;
import android.text.TextUtils;

import androidx.lifecycle.MutableLiveData;
import androidx.lifecycle.ViewModel;

import om.rrtx.mobile.homemodule.bean.InviteBean;
import om.rrtx.mobile.homemodule.bean.PubBean;
import om.rrtx.mobile.homemodule.model.HomeModel;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 */
public class InviteViewModel extends ViewModel {

    private HomeModel mHomeModel;

    private MutableLiveData<InviteBean> mInviteLd = new MutableLiveData<>();
    private MutableLiveData<String> mErrorStr = new MutableLiveData<>();

    public InviteViewModel() {
        mHomeModel = new HomeModel();
    }

    public MutableLiveData<InviteBean> getInviteLd() {
        if (mInviteLd == null) {
            mInviteLd = new MutableLiveData<>();
        }
        return mInviteLd;
    }

    public MutableLiveData<String> getErrorStr() {
        if (mErrorStr == null) {
            mErrorStr = new MutableLiveData<>();
        }
        return mErrorStr;
    }

    public void requestInviteData(Context context) {
        String pubLick = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mHomeModel.requestInviteData(userId,new BaseObserver<InviteBean>(context) {
                @Override
                public void requestSuccess(InviteBean sResData) {
                    if (mInviteLd != null) {
                        mInviteLd.setValue(sResData);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (mErrorStr != null) {
                        mErrorStr.setValue(sResMsg);
                    }
                }
            });
        }else {
         mHomeModel.commonPub(new BaseObserver<PubBean>(context) {
             @Override
             public void requestSuccess(PubBean sResData) {
                 SharedPreferencesUtils.setParam(context, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                 mHomeModel.requestInviteData(userId,new BaseObserver<InviteBean>(context) {
                     @Override
                     public void requestSuccess(InviteBean sResData) {
                         if (mInviteLd != null) {
                             mInviteLd.setValue(sResData);
                         }
                     }

                     @Override
                     public void requestFail(String sResMsg) {
                         if (mErrorStr != null) {
                             mErrorStr.setValue(sResMsg);
                         }
                     }
                 });
             }

             @Override
             public void requestFail(String sResMsg) {

             }
         });
        }
    }
}
