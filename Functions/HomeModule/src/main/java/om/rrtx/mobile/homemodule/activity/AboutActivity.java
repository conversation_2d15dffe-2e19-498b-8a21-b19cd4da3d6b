package om.rrtx.mobile.homemodule.activity;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.cardview.widget.CardView;

import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import butterknife.ButterKnife;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.R2;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.AppInfoUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;

/**
 * <AUTHOR>
 * 关于
 */
public class AboutActivity extends BaseSuperActivity {


    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.versionTv)
    TextView mVersionTv;
    @BindView(R2.id.statusView)
    View mStatusView;
    @BindView(R2.id.termsCv)
    CardView termsCv;
    @BindView(R2.id.policyCv)
    CardView policyCv;

    public static void jumpAbout(Context context) {
        Intent intent = new Intent(context, AboutActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int createContentView() {
        return R.layout.home_activity_about;
    }

    @Override
    protected BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.home_ic_back);
        mStatusView.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));
        mTitleTv.setText(R.string.about_title_about);
    }

    @Override
    public void initDate() {
        super.initDate();
        String versionName = "V" + AppInfoUtils.getVersionName(mContext);
        mVersionTv.setText(versionName);
    }

    @Override
    public void initListener() {
        super.initListener();
        mLeftBg.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                finish();
            }
        });

        termsCv.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                openLinkInBrowser("https://onemoney.co.zw/privacypolicy.html");
                //ShowWebViewActivity.jumpShowWebView(mContext, "https://onemoney.co.zw/", "terms");
            }
        });
        policyCv.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                openLinkInBrowser("https://onemoney.co.zw/privacypolicy.html");
                //ShowWebViewActivity.jumpShowWebView(mContext, "https://onemoney.co.zw/", "policy");
            }
        });
    }

    private void openLinkInBrowser(String url) {
        Intent intent = new Intent(Intent.ACTION_VIEW);
        intent.setData(Uri.parse(url));
        startActivity(intent);
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }
}
