package om.rrtx.mobile.homemodule.activity;

import android.content.Intent;
import android.text.Editable;
import android.text.InputFilter;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;

import com.google.android.material.textfield.TextInputEditText;
import com.google.android.material.textfield.TextInputLayout;
import com.gyf.immersionbar.ImmersionBar;

import java.util.List;

import butterknife.BindView;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.R2;
import om.rrtx.mobile.homemodule.bean.RegionBean;
import om.rrtx.mobile.homemodule.presenter.UserInfoEditPresenter;
import om.rrtx.mobile.homemodule.view.UserInfoEditView;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 编辑用户信息
 */
public class UserInfoEditActivity extends BaseSuperActivity<UserInfoEditView, UserInfoEditPresenter>
        implements TextWatcher, UserInfoEditView {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.editTie)
    EditText mEditTie;
    @BindView(R2.id.tv_hint)
    TextView tvHint;
    @BindView(R2.id.loginTv)
    TextView mLoginTv;
    @BindView(R2.id.statusView)
    View mStatusView;
    private String mEditType;
    /**
     * 页面回填值
     */
    private String mPagerInfo;

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        mEditType = getIntent().getStringExtra(UserConstants.Transmit.EDITTYPE);
        mPagerInfo = getIntent().getStringExtra(UserConstants.Transmit.PAGERINFO);
    }

    @Override
    protected int createContentView() {
        return R.layout.home_activity_user_info_edit;
    }

    @Override
    protected UserInfoEditPresenter createPresenter() {
        return new UserInfoEditPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.home_ic_back);

        mStatusView.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));

        if (TextUtils.equals(mEditType, UserConstants.EditType.NICKNAMETYPE)) {
            mTitleTv.setText(R.string.personal_title_nickname);
            tvHint.setText(getString(R.string.personal_title_nickname));
            mEditTie.setFilters(new InputFilter[]{new InputFilter.LengthFilter(30)});
        } else if (TextUtils.equals(mEditType, UserConstants.EditType.EMAILTYPE)) {
            mTitleTv.setText(R.string.edit_Email);
            tvHint.setText(getString(R.string.personal_title_email));
            mEditTie.setFilters(new InputFilter[]{new InputFilter.LengthFilter(50)});
        }

        if (!TextUtils.isEmpty(mPagerInfo)) {
            mEditTie.setText(mPagerInfo);

        }

        //设置底部按钮状态
        String editText = mEditTie.getText().toString();
        if (!TextUtils.isEmpty(editText) && editText.length() > 0) {
            mLoginTv.setEnabled(true);
        } else {
            mLoginTv.setEnabled(false);
        }

        mEditTie.requestFocus();
    }

    @Override
    public void initDate() {
        super.initDate();

    }

    @Override
    public void initListener() {
        super.initListener();
        mEditTie.addTextChangedListener(this);
        mLeftBg.setOnClickListener(mClickListener);
        mLoginTv.setOnClickListener(mClickListener);
        findViewById(R.id.textClear_iv).setOnClickListener(mClickListener);
    }

    private CustomClickListener mClickListener = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.leftBg) {
                finish();
            } else if (view.getId() == R.id.loginTv) {
                String editStr = mEditTie.getText().toString();
                if (TextUtils.equals(mEditType, UserConstants.EditType.NICKNAMETYPE)) {
                    if (TextUtils.equals(mPagerInfo, editStr)) {
                        ToastUtil.show(mContext, getString(R.string.personal_tip_no_modify));
                        return;
                    }

                    mPresenter.requestEditInfo("", "", "", "", editStr);
                } else if (TextUtils.equals(mEditType, UserConstants.EditType.EMAILTYPE)) {
                    if (TextUtils.equals(mPagerInfo, editStr)) {
                        ToastUtil.show(mContext, getString(R.string.personal_tip_no_modify));
                        return;
                    }
                    mPresenter.requestEditInfo("", "", "", editStr, "");
                }
            } else if (view.getId() == R.id.textClear_iv) {
                mEditTie.setText("");
            }
        }
    };

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        String editText = mEditTie.getText().toString();
        if (!TextUtils.isEmpty(editText) && editText.length() > 0) {
            mLoginTv.setEnabled(true);
        } else {
            mLoginTv.setEnabled(false);
        }
    }

    @Override
    public void editSuccess(String email, String nickName) {
        Intent intent;
        if (TextUtils.equals(mEditType, UserConstants.EditType.NICKNAMETYPE)) {
            intent = new Intent();
            intent.putExtra(UserConstants.Transmit.NICKNAME, nickName);
            setResult(RESULT_OK, intent);
        } else if (TextUtils.equals(mEditType, UserConstants.EditType.EMAILTYPE)) {
            intent = new Intent();
            intent.putExtra(UserConstants.Transmit.EMAIL, email);
            setResult(RESULT_OK, intent);
        }
        finish();
    }

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    public void regionSuccess(List<RegionBean> regionBeans) {

    }

    @Override
    public void checkRepeat() {

    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }
}
