package om.rrtx.mobile.homemodule.bean;

/**
 * <AUTHOR>
 */
public class SendMessageBean {

    private String jumpFlag;
    private String mobile;
    private String mobileAreaCode;
    private String idCard;
    private String userName;
    private String messageTemplateType;

    public String getJumpFlag() {
        return jumpFlag;
    }

    public void setJumpFlag(String jumpFlag) {
        this.jumpFlag = jumpFlag;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getMobileAreaCode() {
        return mobileAreaCode;
    }

    public void setMobileAreaCode(String mobileAreaCode) {
        this.mobileAreaCode = mobileAreaCode;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getMessageTemplateType() {
        return messageTemplateType;
    }

    public void setMessageTemplateType(String messageTemplateType) {
        this.messageTemplateType = messageTemplateType;
    }

    public SendMessageBean(Builder builder) {
        jumpFlag = builder.jumpFlag;
        mobile = builder.mobile;
        mobileAreaCode = builder.mobileAreaCode;
        idCard = builder.idCard;
        userName = builder.userName;
        messageTemplateType = builder.messageTemplateType;
    }

    public static class Builder {
        private String jumpFlag;
        private String mobile;
        private String mobileAreaCode;
        private String idCard;
        private String userName;
        private String messageTemplateType;

        public Builder setJumpFlag(String jumpFlag) {
            this.jumpFlag = jumpFlag;
            return this;
        }

        public Builder setMobile(String mobile) {
            this.mobile = mobile;
            return this;
        }

        public Builder setMobileAreaCode(String mobileAreaCode) {
            this.mobileAreaCode = mobileAreaCode;
            return this;
        }

        public Builder setIdCard(String idCard) {
            this.idCard = idCard;
            return this;
        }

        public Builder setUserName(String userName) {
            this.userName = userName;
            return this;
        }

        public Builder setMessageTemplateType(String messageTemplateType) {
            this.messageTemplateType = messageTemplateType;
            return this;
        }

        public SendMessageBean build() {
            return new SendMessageBean(this);
        }
    }
}
