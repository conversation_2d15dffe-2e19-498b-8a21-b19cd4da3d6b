package om.rrtx.mobile.homemodule.bean

data class JuniorAccountListBean(
    val countId: String,
    val current: Int,
    val maxLimit: String,
    val optimizeCountSql: Boolean,
    val orders: List<Any>,
    val pages: Int,
    val records: ArrayList<JuniorAccountBean>,
    val searchCount: Boolean,
    val size: Int,
    val total: Int
)

data class JuniorAccountBean(
    val juniorUserId: String,
    val avatarUrl: String,
    val userName: String,
    val mobile: String, // 掩码手机号
    val mobileAreaCode: String,
    val mobileNo: String,
    val status: String,
    val verifyStatus: String,//2-升级待认证
    var statusStr: String="",
)