package om.rrtx.mobile.homemodule.activity;

import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;

import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import de.hdodenhof.circleimageview.CircleImageView;
import io.reactivex.Observable;
import io.reactivex.ObservableOnSubscribe;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.disposables.Disposable;
import io.reactivex.schedulers.Schedulers;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.R2;
import om.rrtx.mobile.rrtxcommon1.bean.UserInfoBean;
import om.rrtx.mobile.homemodule.bean.UserQrCodeBean;
import om.rrtx.mobile.homemodule.presenter.PersenterInfoPresenter;
import om.rrtx.mobile.homemodule.view.PersonalInfoView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.image.ImageLoaderManager;
import om.rrtx.mobile.rrtxcommon1.kotlin.ExtensionsKt;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.QRCodeEncoder;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.TimeUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 用户中心页面
 */
public class PersonalInfoActivity extends BaseSuperActivity<PersonalInfoView, PersenterInfoPresenter>
        implements PersonalInfoView {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.headIv)
    CircleImageView mHeadIv;
    @BindView(R2.id.userNameBg)
    View mUserNameBg;
    @BindView(R2.id.userNameTv)
    TextView nameTv;
    @BindView(R2.id.middleNameTv)
    TextView middleNameTv;
    @BindView(R2.id.nricBg)
    View mNricBg;
    @BindView(R2.id.nricTv)
    TextView mNricTv;
    @BindView(R2.id.mobileBg)
    View mMobileBg;
    @BindView(R2.id.mobileTv)
    TextView mMobileTv;
    @BindView(R2.id.emailBg)
    View mEmailBg;
    @BindView(R2.id.emailTv)
    TextView mEmailTv;
    private static int EMAILREQUEST = 0x2;
    @BindView(R2.id.statusView)
    View mStatusView;
    @BindView(R2.id.heardBg)
    View mHeardBg;
    private String mEmail;
    private ImageView qrCodeIv;
    private TextView expiringTv;
    private UserInfoBean mUserInfoBean;
    private ImageView qrCodeMore;
    private Disposable qrCodeTask;

    public static void jumpPersonalInfo(Context context) {
        Intent intent = new Intent(context, PersonalInfoActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int createContentView() {
        return R.layout.home_activity_personal_info;
    }

    @Override
    protected PersenterInfoPresenter createPresenter() {
        return new PersenterInfoPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.home_ic_back);

        mStatusView.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));
        mTitleTv.setText(R.string.personal_title_personal_info);

        qrCodeIv = findViewById(R.id.qrCode_Iv);
        expiringTv = findViewById(R.id.expiringTv);
        qrCodeMore = findViewById(R.id.qrCodeMore);
    }

    @Override
    public void initDate() {
        super.initDate();
        String idCard = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.IDCARD, "");
        String userName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.REALNAME, "");
        mPresenter.requestUserInfo(idCard, userName);
        mPresenter.getRrCode();
    }

    @Override
    public void initListener() {
        super.initListener();
        mLeftBg.setOnClickListener(mClickListener);
        mEmailBg.setOnClickListener(mClickListener);
        mMobileBg.setOnClickListener(mClickListener);
        mHeardBg.setOnClickListener(mClickListener);
        qrCodeMore.setOnClickListener(mClickListener);
        qrCodeIv.setOnClickListener(mClickListener);
    }

    private CustomClickListener mClickListener = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            int id = view.getId();
            if (view.getId() == R.id.leftBg) {
                finish();
            } else if (view.getId() == R.id.emailBg) {
                Intent intent = new Intent(mContext, UserInfoEditActivity.class);
                intent.putExtra(UserConstants.Transmit.EDITTYPE, UserConstants.EditType.EMAILTYPE);
                intent.putExtra(UserConstants.Transmit.PAGERINFO, mEmail);
                startActivityForResult(intent, EMAILREQUEST);
            } else if (view.getId() == R.id.mobileBg) {
                //UserInfoEditMobileActivity.jumpUserInfoEditMobile(mContext);
            } else if (view.getId() == R.id.heardBg) {
                PortraitSettingActivity.jumpPortraitSetting(mContext);
            } else if (id == R.id.qrCodeMore || id == R.id.qrCode_Iv) {
                MyQrCodeActivity.jump(mContext, mUserInfoBean);
            }
        }
    };

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == EMAILREQUEST && resultCode == RESULT_OK) {
            if (data != null) {
                mEmail = data.getStringExtra(UserConstants.Transmit.EMAIL);
                mEmailTv.setText(mEmail);
            }
        }
    }

    @Override
    public void userInfoSuccess(UserInfoBean userInfoBean) {
        if (userInfoBean != null) {
            this.mUserInfoBean = userInfoBean;
            //头像
            SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.USERHEARD, userInfoBean.getUserAvatar());
            ImageLoaderManager.getInstance().disPlayImage(mContext, userInfoBean.getUserAvatar(), R.drawable.ic_contact_head, mHeadIv);

            //用户名
            nameTv.setText(userInfoBean.getMaskName());

            //nric
            mNricTv.setText(userInfoBean.getNationalityName());

            //电话
            mMobileTv.setText(StringUtils.stringMask(userInfoBean.getMobileAreaCode(), userInfoBean.getMobile()));

            expiringTv.setText(TimeUtils.getTime(userInfoBean.getIdExpiredDate()));
            //email
            mEmail = userInfoBean.getEmail();
            if (StringUtils.isValidString(mEmail)) {
                mEmailTv.setText(mEmail);
            } else {
                mEmailTv.setText(R.string.to_be_inputted);
            }
        }
    }

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    public void qrCodeSuccess(UserQrCodeBean bean) {

        qrCodeTask = Observable.create((ObservableOnSubscribe<Bitmap>) emitter -> {
                    Bitmap qrCodeBitmap = QRCodeEncoder.syncEncodeQRCode(bean.getQrCode(), ExtensionsKt.pt2px(48));
                    emitter.onNext(qrCodeBitmap);

                }).subscribeOn(Schedulers.newThread())
                .observeOn(AndroidSchedulers.mainThread())
                .subscribe(bitmap -> {
                    qrCodeIv.setImageBitmap(bitmap);
                    if (mUserInfoBean != null) {
                        mUserInfoBean.setQrCode(bean.getQrCode());
                    }
                    qrCodeTask.dispose();
                });
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        if (intent != null) {
            String mobile = intent.getStringExtra(UserConstants.Transmit.MOBILE);
            String mobileAreaCode = intent.getStringExtra(UserConstants.Transmit.MOBILEAREACODE);
            mMobileTv.setText(StringUtils.stringMask(mobileAreaCode, mobile));
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        String userHeard = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERHEARD, "");
        ImageLoaderManager.getInstance().disPlayImage(mContext, userHeard, R.drawable.ic_contact_head, mHeadIv);

    }
}
