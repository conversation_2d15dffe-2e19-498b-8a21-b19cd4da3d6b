package om.rrtx.mobile.homemodule.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.cncoderx.wheelview.WheelView;

import om.rrtx.mobile.homemodule.R;


/**
 * <AUTHOR>
 * 地区选择框
 */
public class RegionPicker extends Dialog {

    private WheelView mRegionWheel;
    private TextView mCancelTv;
    private String[] mRegions;
    private TextView mOkTv;
    private RagionCallBack mCallBack;
    private String mSelectStr;

    public RegionPicker(@NonNull Context context, String[] regions, String selectStr) {
        super(context, R.style.transparentDialog);
        mRegions = regions;
        mSelectStr = selectStr;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.home_bottom_region_picker);

        setLocation();
        initView();
        initDate();
        initListener();
    }

    private void setLocation() {
        Window window = getWindow();
        if (window != null) {
            window.setGravity(Gravity.BOTTOM);
            WindowManager.LayoutParams WL = window.getAttributes();
            WL.height = ConstraintLayout.LayoutParams.WRAP_CONTENT;
            WL.width = ConstraintLayout.LayoutParams.MATCH_PARENT;
            window.setAttributes(WL);
        }
    }

    private void initView() {
        mRegionWheel = findViewById(R.id.regionWv);
        mCancelTv = findViewById(R.id.cancelTv);
        mOkTv = findViewById(R.id.okTv);
    }

    private void initDate() {
        mRegionWheel.setEntries(mRegions);

        int selectIndex = -1;
        for (int i = 0; i < mRegions.length; i++) {
            if (TextUtils.equals(mRegions[i], mSelectStr)) {
                selectIndex = i;
                break;
            }
        }

        if (selectIndex != -1) {
            mRegionWheel.setCurrentIndex(selectIndex);
        }
    }


    private void initListener() {
        mCancelTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (mCallBack != null) {
                    mCallBack.cancel();
                }
            }
        });
        mOkTv.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                dismiss();
                if (mCallBack != null) {
                    int currentIndex = mRegionWheel.getCurrentIndex();
                    Log.e("done", "onClick: " + currentIndex);
                    mCallBack.getRegion(currentIndex);
                }
            }
        });
    }

    public void setCallBack(RagionCallBack callBack) {
        mCallBack = callBack;
    }

    public interface RagionCallBack {
        /**
         * 获取时间
         *
         * @param currentPosition 当前的角标
         */
        void getRegion(int currentPosition);

        /**
         * 取消按钮
         */
        void cancel();
    }
}
