package om.rrtx.mobile.homemodule.presenter;

import android.content.Context;

import om.rrtx.mobile.homemodule.bean.CommonSetBean;
import om.rrtx.mobile.homemodule.model.HomeModel;
import om.rrtx.mobile.homemodule.view.ContactUsView;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;

public class ContactUsPresenter extends BasePresenter<ContactUsView> {


    private HomeModel mHomeModel;
    private Context mContext;

    public ContactUsPresenter(Context context) {
        mHomeModel = new HomeModel();
        mContext = context;
    }

    public void requestCustomerService() {
        mHomeModel.requestCustomerService(new BaseObserver<CommonSetBean>(mContext) {
            @Override
            public void requestSuccess(CommonSetBean setBean) {
                if (getView() != null) {
                    getView().getInfo(setBean);
                }
            }

            @Override
            public void requestFail(String sResMsg) {
                if (getView() != null) {
                    getView().requestFail(sResMsg);
                }
            }
        });
    }

}
