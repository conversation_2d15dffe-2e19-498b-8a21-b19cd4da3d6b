package om.rrtx.mobile.homemodule.presenter;

import android.content.Context;
import android.text.TextUtils;

import com.google.gson.Gson;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;

import om.rrtx.mobile.homemodule.bean.LocalRegionBean;
import om.rrtx.mobile.homemodule.bean.PubBean;
import om.rrtx.mobile.homemodule.model.HomeModel;
import om.rrtx.mobile.homemodule.view.UserInfoEditView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * 首页的p层
 */
public class UserInfoEditPresenter extends BasePresenter<UserInfoEditView> {
    private String TAG = UserInfoEditPresenter.class.getSimpleName();
    private Context mContext;
    private HomeModel mHomeModel;

    public UserInfoEditPresenter(Context context) {
        mHomeModel = new HomeModel();
        mContext = context;
    }

    /**
     * 编辑信息接口
     */
    public void requestEditInfo(String mobile, String mobileArea, String gender, String email, String nickName) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mHomeModel.requestEditInfo(userId,mobile, mobileArea, gender, email, nickName, new BaseObserver<Object>(mContext) {
                @Override
                public void requestSuccess(Object object) {
                    if (getView() != null) {
                        getView().editSuccess(email, nickName);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        }else {
            mHomeModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mHomeModel.requestEditInfo(userId,mobile, mobileArea, gender, email, nickName, new BaseObserver<Object>(mContext) {
                        @Override
                        public void requestSuccess(Object object) {
                            if (getView() != null) {
                                getView().editSuccess(email, nickName);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    /**
     * 请求时区接口
     */
    public void requestRegion() {
        try {
            InputStream isMac = mContext.getAssets().open("json/nation_english.json");
            //一波读写操作
            String tempStr;
            BufferedReader bis = new BufferedReader(new InputStreamReader(isMac));
            StringBuilder sbData = new StringBuilder();
            while ((tempStr = bis.readLine()) != null) {
                sbData.append(tempStr);
            }
            bis.close();
            isMac.close();

            LocalRegionBean localRegionBean = new Gson().fromJson(sbData.toString(), LocalRegionBean.class);
            if (getView() != null) {
                getView().regionSuccess(localRegionBean.getData());
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public void requestCheckRepeat(String mobile) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mHomeModel.requestCheckRepeat(userId,mobile, new BaseObserver<Object>(mContext) {
                @Override
                public void requestSuccess(Object sResData) {
                    if (getView() != null) {
                        getView().checkRepeat();
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        }else {
            mHomeModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mHomeModel.requestCheckRepeat(userId,mobile, new BaseObserver<Object>(mContext) {
                        @Override
                        public void requestSuccess(Object sResData) {
                            if (getView() != null) {
                                getView().checkRepeat();
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }
}
