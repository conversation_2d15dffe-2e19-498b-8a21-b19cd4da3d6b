package om.rrtx.mobile.homemodule.presenter;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.homemodule.bean.PubBean;
import om.rrtx.mobile.homemodule.bean.UserQrCodeBean;
import om.rrtx.mobile.homemodule.model.HomeModel;
import om.rrtx.mobile.homemodule.view.JuniorPersonalInfoView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.bean.UserInfoBean;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 首页的p层
 */
public class JuniorPersenterInfoPresenter extends BasePresenter<JuniorPersonalInfoView> {
    private String TAG = JuniorPersenterInfoPresenter.class.getSimpleName();
    private Context mContext;
    private HomeModel mHomeModel;

    public JuniorPersenterInfoPresenter(Context context) {
        mHomeModel = new HomeModel();
        mContext = context;
    }


    public void requestUserInfoById() {
        new CommonModel().requestUserInfoById(new BaseObserver<UserInfoBean>(mContext) {
            @Override
            public void requestSuccess(UserInfoBean userInfoBean) {
                if (getView() != null) {
                    getView().userInfoSuccess(userInfoBean);
                }
            }

            @Override
            public void requestFail(String sResMsg) {
                if (getView() != null) {
                    getView().requestFail(sResMsg);
                }
            }
        });
    }

    /**
     * 请求账户余额接口
     */
    public void requestEditInfo(String mobile, String mobileArea, String gender, String email, String nickName) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mHomeModel.requestEditInfo(userId, mobile, mobileArea, gender, email, nickName, new BaseObserver<Object>(mContext) {
                @Override
                public void requestSuccess(Object object) {
                    if (getView() != null) {
                        getView().editSuccess(gender);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        } else {
            mHomeModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mHomeModel.requestEditInfo(userId, mobile, mobileArea, gender, email, nickName, new BaseObserver<Object>(mContext) {
                        @Override
                        public void requestSuccess(Object object) {
                            if (getView() != null) {
                                getView().editSuccess(gender);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    public void getRrCode() {
        Activity activity = ActivityController.getInstance().currentActivity();
        mHomeModel.requestQrCode("", new BaseObserver<UserQrCodeBean>(activity) {
            @Override
            public void requestSuccess(UserQrCodeBean sResData) {
                if (getView() != null) {
                    getView().qrCodeSuccess(sResData);
                }
            }

            @Override
            public void requestFail(String sResMsg) {
                ToastUtil.show(activity, sResMsg);
            }
        });
    }
}
