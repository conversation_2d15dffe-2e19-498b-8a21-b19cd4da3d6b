package om.rrtx.mobile.homemodule.utils;

import android.content.ContentResolver;
import android.content.Context;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.os.Build;
import android.os.Environment;
import android.provider.MediaStore;
import android.util.Log;

import androidx.core.os.EnvironmentCompat;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

public class FileUtils {
    /**
     * 保存bitmap到文件
     *
     * @param context 上下问
     * @param bitmap  bitmap对象
     * @return
     */
    public static boolean saveBitmap2File(Context context, Bitmap bitmap) {
        String fileName;
        File file;
        String bitName = System.currentTimeMillis() + ".JPEG";
        if (Build.BRAND.equals("Xiaomi")) {

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                fileName = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES).getPath() + "/" + bitName;
            } else {
                // 小米手机
                fileName = Environment.getExternalStorageDirectory().getPath() + "/DCIM/Camera/" + bitName;
            }
        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                fileName = context.getExternalFilesDir(Environment.DIRECTORY_PICTURES).getPath() + "/" + bitName;
            } else {
                // 小米手机
                fileName = Environment.getExternalStorageDirectory().getPath() + "/DCIM/" + bitName;
            }
        }
        LogUtil.e("TAG", "saveBitmap2File: " + fileName);

        file = new File(fileName);

        if (file.exists()) {
            file.delete();
        }

        FileOutputStream out;
        try {
            out = new FileOutputStream(file);
            // 格式为 JPEG，照相机拍出的图片为JPEG格式的，PNG格式的不能显示在相册中
            if (bitmap.compress(Bitmap.CompressFormat.JPEG, 90, out)) {
                out.flush();
                out.close();
                // 插入图库
                MediaStore.Images.Media.insertImage(context.getContentResolver(), file.getAbsolutePath(), bitName, null);
            }
            // 发送广播，通知刷新图库的显示
            context.sendBroadcast(new Intent(Intent.ACTION_MEDIA_SCANNER_SCAN_FILE, Uri.parse("file://" + fileName)));
            return true;
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            return false;
        } catch (IOException e) {
            e.printStackTrace();
            return false;
        }
    }
}
