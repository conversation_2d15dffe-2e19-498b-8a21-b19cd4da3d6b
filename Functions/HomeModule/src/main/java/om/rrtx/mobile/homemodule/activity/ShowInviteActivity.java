package om.rrtx.mobile.homemodule.activity;

import android.Manifest;
import android.content.Context;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.Uri;
import android.net.http.SslError;
import android.os.Build;
import android.provider.MediaStore;
import android.text.TextUtils;
import android.util.Base64;
import android.util.Log;
import android.webkit.JavascriptInterface;
import android.webkit.SslErrorHandler;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.annotation.RequiresApi;
import androidx.lifecycle.ViewModelProvider;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.gyf.immersionbar.ImmersionBar;
import com.tbruyelle.rxpermissions2.RxPermissions;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Calendar;

import io.reactivex.disposables.Disposable;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.databinding.HomeActivityShowInviteBinding;
import om.rrtx.mobile.homemodule.utils.BaseClick;
import om.rrtx.mobile.homemodule.utils.FileUtils;
import om.rrtx.mobile.homemodule.utils.LogUtil;
import om.rrtx.mobile.homemodule.viewmodel.InviteViewModel;
import om.rrtx.mobile.rrtxcommon.utils.ResourcesUtils;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity;
import om.rrtx.mobile.rrtxcommon1.utils.LocaleManager;
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;


/**
 * <AUTHOR>
 * 展示邀请好友页面
 */
@Route(path = ARouterPath.HomePath.ShowInviteActivity)
public class ShowInviteActivity extends BaseActivity<HomeActivityShowInviteBinding> {

    private InviteViewModel mInviteViewModel;
    private RxPermissions mRxPermissions;

    public static void jumpShowInvite(Context context) {
        Intent intent = new Intent(context, ShowInviteActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected int createContentView() {
        return R.layout.home_activity_show_invite;
    }

    @Override
    protected void initWorkspaceAction() {
        dataBinding.setInvitationOpt(new InvitationOpt());

        mInviteViewModel = new ViewModelProvider(this).get(InviteViewModel.class);

        if (!mInviteViewModel.getInviteLd().hasObservers()) {
            mInviteViewModel.getInviteLd().observe(this, inviteBean -> {

                if (inviteBean != null) {
                    dataBinding.showWeb.loadUrl(inviteBean.getInviteRegitsterUrl());
                }
            });
        }
    }

    @Override
    public void initView() {
        super.initView();
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        dataBinding.includeTitle.backIv.setImageResource(R.drawable.home_ic_back);

        dataBinding.includeTitle.statusView.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        dataBinding.includeTitle.titleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        dataBinding.includeTitle.titleTv.setTextColor(getResources().getColor(R.color.color_131313));
        dataBinding.includeTitle.titleTv.setText(R.string.invitation_title_invitation);


        WebSettings settings = dataBinding.showWeb.getSettings();
        settings.setJavaScriptEnabled(true);
        dataBinding.showWeb.addJavascriptInterface(new CheckoutPlug(), "inviteFriends");
        dataBinding.showWeb.setWebViewClient(new WebViewClient() {
            @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                view.loadUrl(String.valueOf(request.getUrl()));
                return super.shouldOverrideUrlLoading(view, request);
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return super.shouldOverrideUrlLoading(view, url);
            }

            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                handler.cancel();
                ToastUtil.showCenter(mContext, getString(R.string.common_label_sll_error));
                super.onReceivedSslError(view, handler, error);
            }
        });
    }


    @Override
    public void initData() {
        super.initData();

        mRxPermissions = new RxPermissions(this);
        mInviteViewModel.requestInviteData(mContext);
    }

    @Override
    public void onBackPressed() {
        if (dataBinding.showWeb.canGoBack()) {
            dataBinding.showWeb.goBack();
        } else {
            super.onBackPressed();
        }
    }

    public class InvitationOpt extends BaseClick {

        @Override
        public void leftClick() {
            if (dataBinding.showWeb.canGoBack()) {
                dataBinding.showWeb.goBack();
            } else {
                finish();
            }
        }
    }

    /**
     * 继承自Object类
     */
    public class CheckoutPlug extends Object {

        // 定义JS需要调用的方法
        // window.currentCookies.postMessage(
        // 被JS调用的方法必须加入@JavascriptInterface注解
        // 这里的showCheckout 返回的应该是OrderInfo的结果
        @JavascriptInterface
        public void postMessage(String base64Str) {
            runOnUiThread(() -> {
                try {
                    LogUtil.e("TAG", "AppCall: " + base64Str);
                    if (TextUtils.isEmpty(base64Str)) {
                        ToastUtil.show(mContext, ResourceHelper.getString(mContext, R.string.qrcode_tip_store_fail));
                        return;
                    }
                    JSONObject jsonObject = new JSONObject(base64Str);
                    String method = jsonObject.optString("method");
                    if (TextUtils.equals(method, "savePoster")) {
                        String imageData = jsonObject.optString("imageData");
                        if (TextUtils.isEmpty(imageData)) {
                            ToastUtil.showCenter(mContext, ResourcesUtils.resToStr(mContext, R.string.qrcode_tip_store_fail));
                            return;
                        }
                        Disposable subscribe = mRxPermissions.request(Manifest.permission.WRITE_EXTERNAL_STORAGE)
                                .subscribe(aBoolean -> {
                                    if (aBoolean) {
                                        byte[] decode = Base64.decode(imageData, Base64.DEFAULT);
                                        Bitmap showBitmap = BitmapFactory.decodeByteArray(decode, 0, decode.length);
//                                        if (FileUtils.saveBitmap2File(mContext, showBitmap)) {
//                                            ToastUtil.show(mContext, ResourcesUtils.resToStr(mContext, R.string.qrcode_tip_store_success));
//                                        } else {
//                                            ToastUtil.show(mContext, ResourcesUtils.resToStr(mContext, R.string.qrcode_tip_store_fail));
//                                        }
//                                        showBitmap.recycle();
                                        shareSingleImage(showBitmap);
                                    }
                                }, throwable -> {
                                    Log.e("TAG", "accept: " + throwable.getMessage());
                                    ToastUtil.showCenter(mContext, ResourcesUtils.resToStr(mContext, R.string.qrcode_tip_store_fail));
                                });
                    }else if (TextUtils.equals(method, "shareText")) {
                        String imageData = jsonObject.optString("textData");
                        if (TextUtils.isEmpty(imageData)) {
                            return;
                        }
                        shareSingleText(imageData);
                    }
                } catch (JSONException e) {
                    LogUtil.e("TAG", "解析异常: " + e.getMessage());
                    ToastUtil.show(mContext, ResourceHelper.getString(mContext, R.string.qrcode_tip_store_fail));
                }
            });
        }
        //分享单张图片
        public void shareSingleImage(Bitmap bitmap) {
            //由文件得到uri
            Uri imageUri = Uri.parse(MediaStore.Images.Media.insertImage(getContentResolver(), bitmap, "IMG" + Calendar.getInstance().getTime(), null));
            Intent shareIntent = new Intent();
            shareIntent.setAction(Intent.ACTION_SEND);
            shareIntent.putExtra(Intent.EXTRA_STREAM, imageUri);
            shareIntent.setType("image/*");
            startActivity(Intent.createChooser(shareIntent, "分享到"));
        }

        //分享文本信息
        public void shareSingleText(String text) {
            Intent shareIntent = new Intent();
            shareIntent.setAction(Intent.ACTION_SEND);
            shareIntent.putExtra(Intent.EXTRA_TEXT, text);
            shareIntent.setType("text/plain");
            startActivity(Intent.createChooser(shareIntent, "分享到"));
        }

    }
}