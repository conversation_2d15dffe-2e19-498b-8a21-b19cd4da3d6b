package om.rrtx.mobile.homemodule.activity

import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.home_activity_more_function.rv
import kotlinx.android.synthetic.main.home_base_title.backIv
import kotlinx.android.synthetic.main.home_base_title.leftBg
import kotlinx.android.synthetic.main.home_base_title.titleTv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.homemodule.R
import om.rrtx.mobile.homemodule.adapter.MoreFunctionAdapter
import om.rrtx.mobile.homemodule.databinding.HomeActivityMoreFunctionBinding
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.widget.SpacesItemDecoration

@Route(path = ARouterPath.HomePath.MoreFunctionActivity)

class MoreFunctionActivity : BaseActivity<HomeActivityMoreFunctionBinding>() {
    override fun createContentView() = R.layout.home_activity_more_function

    override fun initWorkspaceAction() {
        leftBg.setOnClickListener {
            finish()
        }
    }

    override fun initView() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true, 0.2f)
            .init()
        titleTv.setText(R.string.more)
        titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
        backIv.setBackgroundResource(R.drawable.common_ic_back_black)
        titleTv.setBackgroundColor(resources.getColor(R.color.color_FFFFFF))

        rv.apply {
            layoutManager = LinearLayoutManager(mContext)
            adapter = MoreFunctionAdapter()
            addItemDecoration(SpacesItemDecoration(24.pt2px()))
        }

        leftBg.setOnClickListener { finish() }
    }
}