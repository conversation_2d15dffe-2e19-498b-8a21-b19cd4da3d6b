package om.rrtx.mobile.homemodule.adapter

import android.view.View
import android.view.ViewGroup
import com.google.gson.Gson
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.homemodule.R
import om.rrtx.mobile.homemodule.activity.JuniorAccountActivity
import om.rrtx.mobile.homemodule.bean.JuniorAccountBean
import om.rrtx.mobile.homemodule.databinding.ItemJuniorAccountBinding
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseRecyclerViewAdapter
import om.rrtx.mobile.rrtxcommon1.image.ImageLoaderManager
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil

class JuniorAccountAdapter :
    BaseRecyclerViewAdapter<JuniorAccountBean, ItemJuniorAccountBinding>() {
    override fun getItemId() = R.layout.item_junior_account

    override fun handleItemData(binding: ItemJuniorAccountBinding, position: Int) {
        binding.apply {
            val bean = data[position]
            titleTv.text = bean.userName
            when (bean.status) {
                CommonConstants.UserStatus.NORMAL -> {
                    if (bean.verifyStatus == "2")//升级待认证
                    {
                        // 获取当前LayoutParams
                        val layoutParams: ViewGroup.LayoutParams = textTv.getLayoutParams()
                        layoutParams.width = 291.pt2px()
                        // 将修改后的LayoutParams应用到View
                        textTv.layoutParams = layoutParams
                        textTv.setBackgroundResource(R.drawable.grey_bg)
                        textTv.text = getString(R.string.upgrade_Pending_Verification)
                        textTv.setTextColor(getColor(R.color.common_text_1d2129))
                        titleTv.setTextColor(getColor(R.color.common_text_1d2129))
                        iconIv.setBackgroundResource(R.drawable.ic_junior_round_ye_bg)
                    }else {
                        // 获取当前LayoutParams
                        val layoutParams: ViewGroup.LayoutParams = textTv.getLayoutParams()
                        layoutParams.width = 122.pt2px()
                        // 将修改后的LayoutParams应用到View
                        textTv.layoutParams = layoutParams

                        textTv.text = getString(R.string.activated)
                        textTv.setBackgroundResource(R.drawable.green_bg)
                        textTv.setTextColor(getColor(R.color.common_green_00A53D))
                        titleTv.setTextColor(getColor(R.color.common_text_1d2129))
                        iconIv.setBackgroundResource(R.drawable.ic_junior_round_ye_bg)
                    }
                }

                CommonConstants.UserStatus.PENDING_AUTH -> {
                    textTv.setBackgroundResource(R.drawable.grey_bg)
                    if (bean.verifyStatus == "2")//升级待认证
                    {
                        // 获取当前LayoutParams
                        val layoutParams: ViewGroup.LayoutParams = textTv.getLayoutParams()
                        layoutParams.width = 291.pt2px()
                        // 将修改后的LayoutParams应用到View
                        textTv.layoutParams = layoutParams

                        textTv.text = getString(R.string.upgrade_Pending_Verification)
                        textTv.setTextColor(getColor(R.color.common_text_1d2129))
                        titleTv.setTextColor(getColor(R.color.common_text_1d2129))
                        iconIv.setBackgroundResource(R.drawable.ic_junior_round_ye_bg)
                    } else {
                        // 获取当前LayoutParams
                        val layoutParams: ViewGroup.LayoutParams = textTv.getLayoutParams()
                        layoutParams.width = 122.pt2px()
                        // 将修改后的LayoutParams应用到View
                        textTv.layoutParams = layoutParams

                        textTv.text = getString(R.string.unverified)
                        textTv.setTextColor(getColor(R.color.color_C9CDD4))
                        titleTv.setTextColor(getColor(R.color.color_C9CDD4))
                        iconIv.setBackgroundResource(R.drawable.ic_junior_round_gray_bg)
                    }
                }

                CommonConstants.UserStatus.FROST_LIN -> {
                    // 获取当前LayoutParams
                    val layoutParams: ViewGroup.LayoutParams = textTv.getLayoutParams()
                    layoutParams.width = 200.pt2px()
                    // 将修改后的LayoutParams应用到View
                    textTv.layoutParams = layoutParams

                    textTv.text = getString(R.string.temp_unverified)
                    textTv.setBackgroundResource(R.drawable.grey_bg)
                    textTv.setTextColor(getColor(R.color.color_C9CDD4))
                    titleTv.setTextColor(getColor(R.color.color_C9CDD4))
                    iconIv.setBackgroundResource(R.drawable.ic_junior_round_gray_bg)
                }

                CommonConstants.UserStatus.FROST -> {
                    // 获取当前LayoutParams
                    val layoutParams: ViewGroup.LayoutParams = textTv.getLayoutParams()
                    layoutParams.width = 122.pt2px()
                    // 将修改后的LayoutParams应用到View
                    textTv.layoutParams = layoutParams

                    textTv.text = getString(R.string.freeze)
                    textTv.setBackgroundResource(R.drawable.grey_bg)
                    textTv.setTextColor(getColor(R.color.color_C9CDD4))
                    titleTv.setTextColor(getColor(R.color.common_text_1d2129))
                    iconIv.setBackgroundResource(R.drawable.ic_junior_round_ye_bg)
                    //ImageLoaderManager.getInstance().disPlayImage(context, bean.avatarUrl, R.drawable.ic_junior_round_ye_bg, iconIv)
                }

                else -> {
                    // 获取当前LayoutParams
                    val layoutParams: ViewGroup.LayoutParams = textTv.getLayoutParams()
                    layoutParams.width = 122.pt2px()
                    // 将修改后的LayoutParams应用到View
                    textTv.layoutParams = layoutParams

                    textTv.text = getString(R.string.unverified)
                    textTv.setBackgroundResource(R.drawable.grey_bg)
                    textTv.setTextColor(getColor(R.color.color_C9CDD4))
                    titleTv.setTextColor(getColor(R.color.color_C9CDD4))
                    iconIv.setBackgroundResource(R.drawable.ic_junior_round_gray_bg)
                }
            }
            bean.statusStr = textTv.text.toString()

            root.setOnClickListener(object : CustomClickListener() {
                override fun onSingleClick(view: View) {
                    if (bean.status == CommonConstants.UserStatus.PENDING_AUTH && bean.verifyStatus != "2") {
                        //ToastUtil.show(context,getString(R.string.please_go_to_OneMoney_branch_for_identity_authentication))
                    } else {
                        JuniorAccountActivity.jump(context, bean)
                        SharedPreferencesUtils.setParam(
                            context,
                            "juniorJson",
                            Gson().toJson(bean)
                        )
                    }
                }
            })
        }
    }
}