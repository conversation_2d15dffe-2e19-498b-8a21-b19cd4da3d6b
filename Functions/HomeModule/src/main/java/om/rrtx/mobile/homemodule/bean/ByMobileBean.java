package om.rrtx.mobile.homemodule.bean;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * <AUTHOR>
 * 根据电话号查找用户
 */
public class ByMobileBean implements Parcelable, Comparable<ByMobileBean> {

    /**
     * realName : fengxz
     * mobile : +6017731140266
     * nikeName : +6017731140266
     * userAvatar :
     * isContact : 0
     */

    private String realName;
    private String mobile;
    private String nikeName;
    private String userAvatar;
    private String isContact;
    private String remark;
    /**
     * 这里是单独加的金额
     * 主要是为了下一个页面的传值
     */
    private String amount;
    private String mobileAreaCode;
    private String currency;

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getNikeName() {
        return nikeName;
    }

    public void setNikeName(String nikeName) {
        this.nikeName = nikeName;
    }

    public String getUserAvatar() {
        return userAvatar;
    }

    public void setUserAvatar(String userAvatar) {
        this.userAvatar = userAvatar;
    }

    public String getIsContact() {
        return isContact;
    }

    public void setIsContact(String isContact) {
        this.isContact = isContact;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getMobileAreaCode() {
        return mobileAreaCode;
    }

    public void setMobileAreaCode(String mobileAreaCode) {
        this.mobileAreaCode = mobileAreaCode;
    }

    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public ByMobileBean() {
    }

    @Override
    public int compareTo(ByMobileBean byMobileBean) {
        // 获取ascii值
        int lhs_ascii = getRealName().toUpperCase().charAt(0);
        int rhs_ascii = byMobileBean.getRealName().toUpperCase().charAt(0);
        // 判断若不是字母，则排在字母之后
        if ((lhs_ascii < 65 || lhs_ascii > 90) && !(rhs_ascii < 65 || rhs_ascii > 90)) {
            //lhs_ascii包含字母但是没有判断rhs_ascii这个包含字母的情况
            return 1;
        } else if (rhs_ascii < 65 || rhs_ascii > 90) {
            return -1;
        } else {
            return getRealName().toUpperCase().compareTo(byMobileBean.getRealName().toUpperCase());
        }
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.realName);
        dest.writeString(this.mobile);
        dest.writeString(this.nikeName);
        dest.writeString(this.userAvatar);
        dest.writeString(this.isContact);
        dest.writeString(this.remark);
        dest.writeString(this.amount);
        dest.writeString(this.mobileAreaCode);
        dest.writeString(this.currency);
    }

    protected ByMobileBean(Parcel in) {
        this.realName = in.readString();
        this.mobile = in.readString();
        this.nikeName = in.readString();
        this.userAvatar = in.readString();
        this.isContact = in.readString();
        this.remark = in.readString();
        this.amount = in.readString();
        this.mobileAreaCode = in.readString();
        this.currency = in.readString();
    }

    public static final Creator<ByMobileBean> CREATOR = new Creator<ByMobileBean>() {
        @Override
        public ByMobileBean createFromParcel(Parcel source) {
            return new ByMobileBean(source);
        }

        @Override
        public ByMobileBean[] newArray(int size) {
            return new ByMobileBean[size];
        }
    };
}
