package om.rrtx.mobile.homemodule;

import java.util.List;
import java.util.Map;

import io.reactivex.Observable;
import okhttp3.MultipartBody;
import om.rrtx.mobile.functioncommon.bean.BalanceBean;
import om.rrtx.mobile.homemodule.bean.ActivityBean;
import om.rrtx.mobile.homemodule.bean.BannerBean;
import om.rrtx.mobile.homemodule.bean.CommonSetBean;
import om.rrtx.mobile.homemodule.bean.HasNews;
import om.rrtx.mobile.homemodule.bean.InviteBean;
import om.rrtx.mobile.homemodule.bean.JuniorAccountListBean;
import om.rrtx.mobile.homemodule.bean.PortraitSettingBean;
import om.rrtx.mobile.homemodule.bean.PubBean;
import om.rrtx.mobile.homemodule.bean.QrcodeBean;
import om.rrtx.mobile.homemodule.bean.RegionBean;
import om.rrtx.mobile.homemodule.bean.SdkInfoBean;
import om.rrtx.mobile.homemodule.bean.UpDataBean;
import om.rrtx.mobile.homemodule.bean.UserQrCodeBean;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.rrtxcommon1.bean.AppDictListBean;
import om.rrtx.mobile.rrtxcommon1.bean.BaseBean;
import om.rrtx.mobile.rrtxcommon1.bean.CheckAccountResultBean;
import retrofit2.Response;
import retrofit2.http.FieldMap;
import retrofit2.http.FormUrlEncoded;
import retrofit2.http.Header;
import retrofit2.http.Multipart;
import retrofit2.http.POST;
import retrofit2.http.Part;

/**
 * 首页的Api接口层
 */
public interface HomeService {


    /**
     * 获取公钥的接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.GETPUB)
    Observable<Response<BaseBean<PubBean>>> requestPub(@FieldMap Map<String, String> formData);


    /**
     * 退出登录
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.LOGOUT)
    Observable<Response<BaseBean<Object>>> requestLogout(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(UserConstants.URL.CUSTOMERSERVICE)
    Observable<Response<BaseBean<CommonSetBean>>> requestCustomerService(@FieldMap Map<String, String> formData);

    /**
     * 解析二维码接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.ANALYZE)
    Observable<Response<BaseBean<QrcodeBean>>> requestAnalysisCode(@FieldMap Map<String, String> formData);

    /**
     * 获取二维码接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.GETCODE)
    Observable<Response<BaseBean<UserQrCodeBean>>> requestQrCode(@FieldMap Map<String, String> formData);

    /**
     * 获取二维码接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.CHECKACTIVITY)
    Observable<Response<BaseBean<ActivityBean>>> requestIsHasActivity(@FieldMap Map<String, String> formData);


    /**
     * 编辑用户信息
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.MODIFYUSERINFO)
    Observable<Response<BaseBean<Object>>> requestEditInfo(@FieldMap Map<String, String> formData);

    /**
     * 校验手机号是否重复
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.CHECKREPEAT)
    Observable<Response<BaseBean<Object>>> requestCheckRepeat(@FieldMap Map<String, String> formData);

    /**
     * 请求时区接口
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.RAGION)
    Observable<Response<BaseBean<List<RegionBean>>>> requestRegion(@FieldMap Map<String, String> formData);

    /**
     * 上传头衔
     *
     * @param body 实体信息
     * @return 相应的Observable
     */
    @Multipart
    @POST(UserConstants.URL.UPLOADUSERAVATAR)
    Observable<Response<BaseBean<PortraitSettingBean>>> uploadUserAvatar(@Header("Authorization") String contentRange, @Part MultipartBody.Part body);

    /**
     * 检查版本更新
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.APPVERSION)
    Observable<Response<BaseBean<UpDataBean>>> requestUpData(@FieldMap Map<String, String> formData);

    /**
     * 是否有新消息
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.CHECKNEW)
    Observable<Response<BaseBean<HasNews>>> requestNews(@FieldMap Map<String, String> formData);

    /**
     * 轮播图
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.ROTATEPICTURE)
    Observable<Response<BaseBean<BannerBean>>> requestBanners(@FieldMap Map<String, String> formData);

    /**
     * 悬浮图
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.FLOATINGPICTURE)
    Observable<Response<BaseBean<BannerBean.DetailsBean>>> requestFloating(@FieldMap Map<String, String> formData);

    /**
     * 悬浮图
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.INVITEFRIENDSGET)
    Observable<Response<BaseBean<InviteBean>>> requestInviteData(@FieldMap Map<String, String> formData);


    /**
     * 切换语言
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.LANGUAGESETUP)
    Observable<Response<BaseBean<Object>>> requestLanguageSetUp(@FieldMap Map<String, String> formData);

    /**
     * 获取SDK信息
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.SDKINFO)
    Observable<Response<BaseBean<SdkInfoBean>>> requestXStoreInfo(@FieldMap Map<String, String> formData);

    /**
     * 查询用户币种
     *
     * @param formData 需要参数
     * @return 相应的Observable
     */
    @FormUrlEncoded
    @POST(UserConstants.URL.APPDICTCODE)
    Observable<Response<BaseBean<AppDictListBean>>> requestCurrencyList(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(UserConstants.URL.JUNIOR_LIST)
    Observable<Response<BaseBean<JuniorAccountListBean>>> getJuniorList(@FieldMap Map<String, String> formData);

    @FormUrlEncoded
    @POST(UserConstants.URL.JUNIOR_DELETE_CHECK)
    Observable<Response<BaseBean<CheckAccountResultBean>>> requestJuniorDeleteCheck(@FieldMap Map<String, String> formData);

}
