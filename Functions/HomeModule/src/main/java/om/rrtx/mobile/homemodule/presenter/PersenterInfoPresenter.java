package om.rrtx.mobile.homemodule.presenter;

import android.app.Activity;
import android.content.Context;
import android.text.TextUtils;

import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.rrtxcommon1.bean.UserInfoBean;
import om.rrtx.mobile.homemodule.bean.PubBean;
import om.rrtx.mobile.homemodule.bean.UserQrCodeBean;
import om.rrtx.mobile.homemodule.model.HomeModel;
import om.rrtx.mobile.homemodule.view.PersonalInfoView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 首页的p层
 */
public class PersenterInfoPresenter extends BasePresenter<PersonalInfoView> {
    private String TAG = PersenterInfoPresenter.class.getSimpleName();
    private Context mContext;
    private HomeModel mHomeModel;

    public PersenterInfoPresenter(Context context) {
        mHomeModel = new HomeModel();
        mContext = context;
    }


    public void requestUserInfo(String idCard, String userName) {
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        String mobile = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERMOBILE, "");

        new CommonModel().requestUserInfo(mobile, idCard, userName, new BaseObserver<UserInfoBean>(mContext) {
            @Override
            public void requestSuccess(UserInfoBean userInfoBean) {
                if (getView() != null) {
                    getView().userInfoSuccess(userInfoBean);
                }
            }

            @Override
            public void requestFail(String sResMsg) {
                if (getView() != null) {
                    getView().requestFail(sResMsg);
                }
            }
        });
    }

    public void getRrCode() {
        Activity activity = ActivityController.getInstance().currentActivity();
        mHomeModel.requestQrCode("", new BaseObserver<UserQrCodeBean>(activity) {
            @Override
            public void requestSuccess(UserQrCodeBean sResData) {
                if (getView() != null) {
                    getView().qrCodeSuccess(sResData);
                }
            }

            @Override
            public void requestFail(String sResMsg) {
                ToastUtil.show(activity, sResMsg);
            }
        });
    }
}
