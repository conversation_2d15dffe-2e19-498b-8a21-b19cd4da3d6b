package om.rrtx.mobile.homemodule.presenter;

import android.content.Context;
import android.text.TextUtils;

import om.rrtx.mobile.functioncommon.bean.BalanceBean;
import om.rrtx.mobile.functioncommon.bean.BalanceCertificateBean;
import om.rrtx.mobile.functioncommon.bean.BankNumBean;
import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.homemodule.bean.ActivityBean;
import om.rrtx.mobile.rrtxcommon1.bean.AppDictListBean;
import om.rrtx.mobile.functioncommon.bean.CurrencyAccountListBean;
import om.rrtx.mobile.homemodule.bean.PubBean;
import om.rrtx.mobile.homemodule.bean.UserQrCodeBean;
import om.rrtx.mobile.homemodule.model.HomeModel;
import om.rrtx.mobile.homemodule.utils.LogUtil;
import om.rrtx.mobile.homemodule.view.AccountFragmentView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserverNoError;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 首页Tab的p层
 */
public class AccountFragmentPresenter extends BasePresenter<AccountFragmentView> {
    private String TAG = AccountFragmentPresenter.class.getSimpleName();
    private Context mContext;
    private HomeModel mHomeModel;

    public AccountFragmentPresenter(Context context) {
        mHomeModel = new HomeModel();
        mContext = context;
    }

    /**
     * 获取所有币种账户
     */
    public void requestAccountCurrency(String type) {
        CommonModel commonModel = new CommonModel();
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        if (!TextUtils.isEmpty(pubLick)) {
            commonModel.requestAccountCurrency(type, new BaseObserverNoError<CurrencyAccountListBean>(mContext) {
                @Override
                public void requestSuccess(CurrencyAccountListBean balanceBean) {
                    if (getView() != null) {
                        getView().currencyAccountSuccess(balanceBean);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    LogUtil.e(TAG, "获取余额失败" + sResMsg);
                }
            });
        } else {
            mHomeModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    commonModel.requestAccountCurrency(type, new BaseObserverNoError<CurrencyAccountListBean>(mContext) {
                        @Override
                        public void requestSuccess(CurrencyAccountListBean balanceBean) {
                            if (getView() != null) {
                                getView().currencyAccountSuccess(balanceBean);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            LogUtil.e(TAG, "获取余额失败" + sResMsg);
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    public void requestBalancePay(String payType, String pin, String currency, String transType,
                                  String userId) {
        new CommonModel().requestBalancePay(payType, pin, currency, transType, userId, new BaseObserver<BalanceCertificateBean>(mContext) {
            @Override
            public void requestSuccess(BalanceCertificateBean sResData) {
                if (getView() != null) {
                    getView().requestBalancePaySuccess(sResData);
                }

            }

            @Override
            public void requestFail(String sResMsg) {
                ToastUtil.show(mContext, sResMsg);
            }
        });
    }

    /**
     * 获取币种账户余额校验
     */
    public void checkBalanceOrder(String currency, String queryType) {
        new CommonModel().checkBalanceOrder(currency, queryType, new BaseObserver<BalanceBean>(mContext) {
            @Override
            public void requestSuccess(BalanceBean sResData) {
                if (getView() != null) {
                    getView().checkBalanceOrderSuccess(sResData);
                }

            }

            @Override
            public void requestFail(String sResMsg) {
                ToastUtil.show(mContext, sResMsg);
            }
        });
    }

    /**
     * 查询用户币种
     */
    public void requestCurrencyList() {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mHomeModel.requestCurrencyList(userId, new BaseObserver<AppDictListBean>(mContext) {
                @Override
                public void requestSuccess(AppDictListBean balanceBean) {
                    if (getView() != null) {
                        getView().getCurListSuccess(balanceBean);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    LogUtil.e(TAG, "获取币种列表失败" + sResMsg);
                }
            });
        } else {
            mHomeModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mHomeModel.requestCurrencyList(userId, new BaseObserver<AppDictListBean>(mContext) {
                        @Override
                        public void requestSuccess(AppDictListBean balanceBean) {
                            if (getView() != null) {
                                getView().getCurListSuccess(balanceBean);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            LogUtil.e(TAG, "获取币种列表失败" + sResMsg);
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    public void requestQrCode() {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mHomeModel.requestQrCode(userId, new BaseObserver<UserQrCodeBean>(mContext) {
                @Override
                public void requestSuccess(UserQrCodeBean sResData) {
                    if (getView() != null) {
                        getView().qrCodeSuccess(sResData);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                }
            });
        } else {
            mHomeModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mHomeModel.requestQrCode(userId, new BaseObserver<UserQrCodeBean>(mContext) {
                        @Override
                        public void requestSuccess(UserQrCodeBean sResData) {
                            if (getView() != null) {
                                getView().qrCodeSuccess(sResData);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    public void requestIsHasActivity() {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mHomeModel.requestIsHasActivity(userId, new BaseObserver<ActivityBean>(mContext) {
                @Override
                public void requestSuccess(ActivityBean sResData) {
                    if (getView() != null) {
                        getView().isHasActivitySuccess(sResData);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                }
            });
        } else {
            mHomeModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mHomeModel.requestIsHasActivity(userId, new BaseObserver<ActivityBean>(mContext) {
                        @Override
                        public void requestSuccess(ActivityBean sResData) {
                            if (getView() != null) {
                                getView().isHasActivitySuccess(sResData);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    public void getBankNum() {
        new CommonModel().getBankNum(new BaseObserver<BankNumBean>(mContext) {
            @Override
            public void requestSuccess(BankNumBean sResData) {
                if (getView() != null) {
                    getView().getBankNumSuccess(sResData.getBankCardNum());
                }
            }

            @Override
            public void requestFail(String sResMsg) {
                ToastUtil.show(mContext, sResMsg);
            }
        });
    }
}
