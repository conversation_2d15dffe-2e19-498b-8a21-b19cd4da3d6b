package om.rrtx.mobile.homemodule.bean;

/**
 * <AUTHOR>
 */
public class InviteBean {

    /**
     * nickName : 0452
     * userName : hejinlong
     * realName : hejinling
     * inviteCode : 30317c54676c6f4244744b746c6e4365757a574a4c394767773d3d
     */

    private String nickName;
    private String userName;
    private String realName;
    private String inviteCode;
    private String inviteRegitsterUrl;

    public String getInviteRegitsterUrl() {
        return inviteRegitsterUrl;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getInviteCode() {
        return inviteCode;
    }

    public void setInviteCode(String inviteCode) {
        this.inviteCode = inviteCode;
    }
}
