package om.rrtx.mobile.homemodule.activity

import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.graphics.Paint
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.google.gson.Gson
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.home_activity_junior_account.cancel_tv
import kotlinx.android.synthetic.main.home_activity_junior_account.function_rv
import kotlinx.android.synthetic.main.home_activity_junior_account.icon_iv
import kotlinx.android.synthetic.main.home_activity_junior_account.mobile_tv
import kotlinx.android.synthetic.main.home_activity_junior_account.name_tv
import kotlinx.android.synthetic.main.home_activity_junior_account.status_tv
import kotlinx.android.synthetic.main.home_base_title.backIv
import kotlinx.android.synthetic.main.home_base_title.leftBg
import kotlinx.android.synthetic.main.home_base_title.titleTv
import om.rrtx.mobile.functionapi.ARouterPath
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.functioncommon.activity.VerCodeActivity
import om.rrtx.mobile.functioncommon.adapter.SettingsAdapter
import om.rrtx.mobile.homemodule.R
import om.rrtx.mobile.homemodule.bean.ByMobileBean
import om.rrtx.mobile.homemodule.bean.JuniorAccountBean
import om.rrtx.mobile.homemodule.databinding.HomeActivityJuniorAccountBinding
import om.rrtx.mobile.homemodule.viewmodel.JuniorAccountVM
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseVVMActivity
import om.rrtx.mobile.rrtxcommon1.bean.InfoItemBean
import om.rrtx.mobile.rrtxcommon1.dialog.AloneDialog
import om.rrtx.mobile.rrtxcommon1.image.ImageLoaderManager
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.widget.SpacesItemDecoration

@Route(path = ARouterPath.HomePath.JuniorAccountActivity)
class JuniorAccountActivity : BaseVVMActivity<JuniorAccountVM, HomeActivityJuniorAccountBinding>() {

    private var verifyStatus = "0"
    private lateinit var mBean: JuniorAccountBean
    override fun createContentView() = R.layout.home_activity_junior_account
    override fun doGetExtra() {
        super.doGetExtra()

        var stringExtra = intent.getStringExtra(CommonConstants.Transmit.JSON)
        if (!StringUtils.isValidString(stringExtra)) {
            stringExtra = SharedPreferencesUtils.getParam(mContext, "juniorJson", "").toString()
        }
        mBean = Gson().fromJson(stringExtra, JuniorAccountBean::class.java)
        verifyStatus = intent.getStringExtra("verifyStatus").toString()
    }

    override fun initView() {
        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true, 0.2f)
            .init()
        titleTv.setText(R.string.junior_Account)
        titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
        titleTv.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))
        backIv.setBackgroundResource(R.drawable.common_ic_back_black)
        icon_iv.setBackgroundResource(R.drawable.ic_junior_round_white_bg)
        //ImageLoaderManager.getInstance().disPlayImage(this, mBean.avatarUrl, R.drawable.ic_junior_round_white_bg, icon_iv)
        name_tv.text = mBean.userName
        mobile_tv.text = mBean.mobile
        if (verifyStatus == "2")
        {
            status_tv.text = getString(R.string.upgrade_Pending_Verification)
        }else{
            status_tv.text = mBean.statusStr
        }
        SharedPreferencesUtils.setParam(
            mContext,
            BaseConstants.Transmit.JUNIOR_NAME,
            mBean.userName
        )

        function_rv.apply {
            layoutManager = LinearLayoutManager(context)
            val settingsAdapter = SettingsAdapter()
            var mList = ArrayList<InfoItemBean>()
            when (mBean.status) {
                CommonConstants.UserStatus.NORMAL -> {
                    if (mBean.verifyStatus == "2" || verifyStatus == "2")//升级待认证
                    {
                        mList = arrayListOf(
                            InfoItemBean(
                                getString(R.string.send_money),
                                callBack = ::jumpSendMoney
                            ),
                            InfoItemBean(
                                getString(R.string.check_Junior_Account),
                                callBack = ::jumpCheckAccount
                            ),
                            InfoItemBean(getString(R.string.change_PIN), callBack = ::jumpChangPin),
                        )
                    } else {
                        mList = arrayListOf(
                            InfoItemBean(
                                getString(R.string.send_money),
                                callBack = ::jumpSendMoney
                            ),
                            InfoItemBean(
                                getString(R.string.check_Junior_Account),
                                callBack = ::jumpCheckAccount
                            ),
                            InfoItemBean(getString(R.string.change_PIN), callBack = ::jumpChangPin),
                            InfoItemBean(
                                getString(R.string.upgrade_Regular_Account),
                                callBack = ::jumpUpgradeAccount
                            ),
                        )
                    }
                }

                CommonConstants.UserStatus.PENDING_AUTH -> {
                    if (mBean.verifyStatus == "2")//升级待认证
                    {
                        mList = arrayListOf(
                            InfoItemBean(
                                getString(R.string.send_money),
                                callBack = ::jumpSendMoney
                            ),
                            InfoItemBean(
                                getString(R.string.check_Junior_Account),
                                callBack = ::jumpCheckAccount
                            ),
                            InfoItemBean(getString(R.string.change_PIN), callBack = ::jumpChangPin),
                        )
                    }
                }

                CommonConstants.UserStatus.FROST_LIN -> {
                    mList = arrayListOf(
                        InfoItemBean(
                            getString(R.string.send_money),
                            callBack = ::jumpSendMoney
                        ),
                        InfoItemBean(
                            getString(R.string.check_Junior_Account),
                            callBack = ::jumpCheckAccount
                        ),
                        InfoItemBean(getString(R.string.change_PIN), callBack = ::jumpChangPin),
                    )
                }

                CommonConstants.UserStatus.FROST -> {
                    mList = arrayListOf(
                        InfoItemBean(
                            getString(R.string.check_Junior_Account),
                            callBack = ::jumpCheckAccount
                        ),
                    )
                }

                else -> {
                    mList = arrayListOf(
                        InfoItemBean(
                            getString(R.string.send_money),
                            callBack = ::jumpSendMoney
                        ),
                        InfoItemBean(
                            getString(R.string.check_Junior_Account),
                            callBack = ::jumpCheckAccount
                        ),
                        InfoItemBean(getString(R.string.change_PIN), callBack = ::jumpChangPin),
                        InfoItemBean(
                            getString(R.string.upgrade_Regular_Account),
                            callBack = ::jumpUpgradeAccount
                        ),
                    )
                }
            }

            settingsAdapter.setNewData(mList)
            adapter = settingsAdapter
            addItemDecoration(SpacesItemDecoration(24.pt2px()))
        }

        // 下划线
        //cancel_tv.paint.flags = Paint.UNDERLINE_TEXT_FLAG
    }

    override fun initClickListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View) {
                when (view) {
                    leftBg -> onBackPressed()
                    cancel_tv -> viewModel.requestJuniorDeleteCheck(mBean.juniorUserId)

                }
            }

        }.apply {
            leftBg.setOnClickListener(this)
            cancel_tv.setOnClickListener(this)
        }
    }


    override fun initVMListener() {
        viewModel.checkJuniorResultLD.observe(this) {
            if (it.hasUnfinishedTrade == "1") {
                showAloneDialog(R.string.there_are_unfinished_transactions)
            } else if (it.hasBalance == "1") {
                showAloneDialog(R.string.the_account_has_a_balance_and)
            } else {
                showDeleteJuniorDialog(it.mobile)
            }
        }
    }

    private fun jumpSendMoney() {
        val byMobileBean = ByMobileBean()
        byMobileBean.mobile = mBean.mobileNo
        byMobileBean.mobileAreaCode = mBean.mobileAreaCode
        byMobileBean.userAvatar = mBean.avatarUrl
        byMobileBean.realName = mBean.userName
        //转账页面
        ARouter.getInstance()
            .build(ARouterPath.TransferPath.TransferDetailsActivity)
            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.JUNIOR_SEND_MONEY)
            .withString(BaseConstants.Transmit.FROMTYPE, BaseConstants.JumpFlag.SEND_MONEY)
            .withString(CommonConstants.Transmit.JSON, Gson().toJson(byMobileBean))
            .navigation()
    }

    private fun showAloneDialog(content: Int) {
        val aloneDialog = AloneDialog(mContext)
        aloneDialog.setAloneCallBack { aloneDialog.dismiss() }
        aloneDialog.show()
        aloneDialog.setTitleStr(getString(R.string.notice))
            .setBottomStr(getString(R.string.confirm))
            .setContentStr(getString(content))
    }

    private fun showDeleteJuniorDialog(mobile: String) {
        val mClosedDialog = om.rrtx.mobile.rrtxcommon1.dialog.DoubleDialog(mContext)
        mClosedDialog.setDoubleCallback(object :
            om.rrtx.mobile.rrtxcommon1.dialog.DoubleDialog.DoubleCallback {
            override fun leftCallback() {
                mClosedDialog.dismiss()
            }

            override fun rightCallback() {
                SharedPreferencesUtils.setParam(
                    mContext,
                    BaseConstants.SaveParameter.JUNIOR_ID,
                    mBean.juniorUserId
                )
                SharedPreferencesUtils.setParam(
                    mContext,
                    BaseConstants.SaveParameter.JUNIOR_MOBILE,
                    mobile
                )
                VerCodeActivity.jump(mContext, mobile, BaseConstants.JumpFlag.Delete_junior_Account)
                mClosedDialog.dismiss()
            }
        })
        mClosedDialog.show()
        mClosedDialog.setMyTitle(getString(om.rrtx.mobile.functioncommon.R.string.common_alert_cancel))
            .setContentStr(getString(R.string.are_you_sure_you_want_to_delete_junior_account))
            .setLeftStr(getString(R.string.close)).setRightStr(getString(R.string.confirm))
    }

    private fun jumpCheckAccount() {
//        CheckJuniorDealActivity.jump(this.mContext)
//        SharedPreferencesUtils.setParam(mContext,
//            BaseConstants.Transmit.JUNIOR_ID,
//            mBean.juniorUserId)
        ARouter.getInstance().build(ARouterPath.TransferPath.CheckJuniorDealActivity)
            .withString(BaseConstants.Transmit.JUNIOR_ID, mBean.juniorUserId)
            .navigation()
    }

    private fun jumpUpgradeAccount() {
        ARouter.getInstance().build(ARouterPath.LoginPath.UpgradeAccountActivity)
            .withString(BaseConstants.Transmit.JUNIOR_ID, mBean.juniorUserId).navigation()
    }

    private fun jumpChangPin() {
        ARouter.getInstance().build(ARouterPath.LoginPath.ForgetPinActivity)
            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.SET_JUNIOR_PIN)
            .withString(BaseConstants.Transmit.JUNIOR_ID, mBean.juniorUserId).navigation()
//        ARouter.getInstance()
//            .build(ARouterPath.SecurityPath.SecurityPinPaymentActivity)
//            .withString(BaseConstants.Transmit.PINPAYMENT, BaseConstants.Check.AUTHENTICATION)
//            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.SET_JUNIOR_PIN)
//            .navigation()
    }

    companion object {
        fun jump(context: Context, bean: JuniorAccountBean) {
            val intent = Intent(context, JuniorAccountActivity::class.java)
            intent.putExtra(CommonConstants.Transmit.JSON, Gson().toJson(bean))
            context.startActivity(intent)
        }
    }
}