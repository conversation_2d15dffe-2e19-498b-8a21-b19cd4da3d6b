package om.rrtx.mobile.homemodule.dialog;

import android.Manifest;
import android.app.Activity;
import android.app.Dialog;
import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.os.Bundle;
import android.util.Base64;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.fragment.app.FragmentActivity;

import com.tbruyelle.rxpermissions2.RxPermissions;

import io.reactivex.disposables.Disposable;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.rrtxcommon.utils.ResourcesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

public class SelectPictureDialog extends Dialog {

    private TextView albumTv;
    private TextView snapshotTv;
    private TextView cancelTv;
    private RxPermissions mRxPermissions;
    private Activity mContext;
    private SelectPictureCallBack selectPictureCallBack;

    public SelectPictureDialog(@NonNull Activity context) {
        super(context, R.style.transparentDialog);
        this.mContext = context;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.home_dialog_select_picture);
        setCancelable(true);
        setCanceledOnTouchOutside(true);
        setLocation();
        initView();
        initListener();
    }

    private void setLocation() {
        Window window = getWindow();
        if (window != null) {
            window.setGravity(Gravity.BOTTOM);
            WindowManager.LayoutParams WL = window.getAttributes();
            WL.height = ConstraintLayout.LayoutParams.WRAP_CONTENT;
            WL.width = ConstraintLayout.LayoutParams.MATCH_PARENT;
            window.setAttributes(WL);
        }
    }

    private void initView() {
        mRxPermissions = new RxPermissions((FragmentActivity) mContext);
        albumTv = findViewById(R.id.albumTv);
        snapshotTv = findViewById(R.id.takePhotoTv);
        cancelTv = findViewById(R.id.cancelTv);
    }

    private void initListener() {
        albumTv.setOnClickListener(mClickListener);
        snapshotTv.setOnClickListener(mClickListener);
        cancelTv.setOnClickListener(mClickListener);
    }

    private CustomClickListener mClickListener = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.takePhotoTv) {
                Disposable subscribe = mRxPermissions.request(Manifest.permission.CAMERA)
                        .subscribe(aBoolean -> {
                            if (aBoolean) {
                                if (selectPictureCallBack != null) {
                                    selectPictureCallBack.openSnapshot();
                                }
                            }
                        }, throwable -> {
                            Log.e("TAG", "accept: " + throwable.getMessage());
                        });


            } else if (view.getId() == R.id.albumTv) {
                if (selectPictureCallBack != null) {
                    selectPictureCallBack.openAlbum();
                }
            } else if (view.getId() == R.id.cancelTv) {
                dismiss();
            }
        }
    };

    /**
     * 设置相应的监听
     *
     * @param selectPictureCallBack 回调
     */
    public void setSelectPictureCallBack(SelectPictureCallBack selectPictureCallBack) {
        this.selectPictureCallBack = selectPictureCallBack;
    }

    public interface SelectPictureCallBack {
        /**
         * 打开相册
         */
        void openAlbum();

        /**
         * 打开摄像机
         */
        void openSnapshot();
    }
}