package om.rrtx.mobile.homemodule.activity

import android.Manifest
import android.content.Context
import android.content.Intent
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Canvas
import android.graphics.drawable.BitmapDrawable
import android.graphics.drawable.VectorDrawable
import android.util.Log
import android.view.View
import androidx.annotation.DrawableRes
import androidx.core.content.ContextCompat
import androidx.vectordrawable.graphics.drawable.VectorDrawableCompat
import com.google.gson.Gson
import com.gyf.immersionbar.ImmersionBar
import com.tbruyelle.rxpermissions2.RxPermissions
import io.reactivex.Observable
import io.reactivex.ObservableEmitter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import kotlinx.android.synthetic.main.home_activity_qrcode.close_tv
import kotlinx.android.synthetic.main.home_activity_qrcode.head_iv
import kotlinx.android.synthetic.main.home_activity_qrcode.mobile_tv
import kotlinx.android.synthetic.main.home_activity_qrcode.name_tv
import kotlinx.android.synthetic.main.home_activity_qrcode.qrCode_iv
import kotlinx.android.synthetic.main.home_activity_qrcode.save_QrCode_Iv
import kotlinx.android.synthetic.main.home_activity_qrcode.save_mobile_tv
import kotlinx.android.synthetic.main.home_activity_qrcode.save_name_tv
import kotlinx.android.synthetic.main.home_activity_qrcode.save_qrCode_bg
import kotlinx.android.synthetic.main.home_activity_qrcode.save_tv
import kotlinx.android.synthetic.main.home_base_title.backIv
import kotlinx.android.synthetic.main.home_base_title.leftBg
import kotlinx.android.synthetic.main.home_base_title.titleTv
import om.rrtx.mobile.functioncommon.CommonConstants
import om.rrtx.mobile.homemodule.R
import om.rrtx.mobile.homemodule.databinding.HomeActivityQrcodeBinding
import om.rrtx.mobile.homemodule.utils.FileUtils
import om.rrtx.mobile.rrtxcommon.utils.ResourcesUtils
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity
import om.rrtx.mobile.rrtxcommon1.bean.UserInfoBean
import om.rrtx.mobile.rrtxcommon1.image.ImageLoaderManager
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.BitmapUtils
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.QRCodeEncoder
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil


class MyQrCodeActivity : BaseActivity<HomeActivityQrcodeBinding>() {

    lateinit var bean: UserInfoBean
    lateinit var mRxPermissions: RxPermissions
    lateinit var saveQrcodeTask: Disposable
    lateinit var qrcodeTask: Disposable

    companion object {
        @JvmStatic
        fun jump(context: Context, bean: UserInfoBean) {
            val intent = Intent(context, MyQrCodeActivity::class.java)
            intent.putExtra(CommonConstants.Parameter.JSON_BEAN, Gson().toJson(bean))
            context.startActivity(intent)
        }
    }

    override fun createContentView() = R.layout.home_activity_qrcode

    override fun initWorkspaceAction() {
        mRxPermissions = RxPermissions(this@MyQrCodeActivity)
    }

    override fun doGetExtra() {
        val json = intent.getStringExtra(CommonConstants.Parameter.JSON_BEAN)
        if (StringUtils.isValidString(json)) {
            bean = Gson().fromJson(json, UserInfoBean::class.java)
        }
    }

    override fun initView() {

        ImmersionBar.with(this)
            .statusBarView(R.id.statusView)
            .statusBarDarkFont(true, 0.2f)
            .init()

        titleTv.setText(R.string.my_QR_Code)
        titleTv.setTextColor(ResourceHelper.getColor(R.color.common_text_1d2129))
        backIv.setBackgroundResource(R.drawable.common_ic_back_black)
        titleTv.setBackgroundColor(ResourceHelper.getColor(R.color.color_FFFFFF))

        val userHeard = SharedPreferencesUtils.getParam(mContext,
            BaseConstants.SaveParameter.USERHEARD,
            "") as String

        ImageLoaderManager.getInstance().disPlayImage(mContext,
            userHeard,
            R.drawable.ic_contact_head,
            head_iv)

        val maskName = bean.maskName
        name_tv.setText(maskName)
        save_name_tv.setText(maskName)

        //电话
        val maskMobile = StringUtils.stringMask(bean.getMobileAreaCode(),
            bean.getMobile())
        mobile_tv.setText(maskMobile)
        save_mobile_tv.setText(maskMobile)

        drawQrcode()
//        drawSaveQrcode()
    }

    private fun drawQrcode() {
        qrcodeTask = Observable.create<Bitmap> { emitter: ObservableEmitter<Bitmap?> ->

            val head =
                BitmapFactory.decodeResource(mContext.resources, R.drawable.ic_logo_black_side)
            val bitmap = BitmapUtils.getRoundedCornerBitmap(
                head,
                mContext.resources.getDimension(R.dimen.home_scan_qr_code_price_round_size))
            val qrCodeBitmap = QRCodeEncoder.syncEncodeQRCode(bean.qrCode,
                402.pt2px(),
                mContext.resources.getColor(R.color.color_131313),
                mContext.resources.getColor(R.color.color_FFFFFF),
                bitmap)
            emitter.onNext(qrCodeBitmap)
        }.subscribeOn(Schedulers.newThread())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { bitmap: Bitmap? ->
                qrCode_iv.setImageBitmap(bitmap)
                qrcodeTask.dispose()
            }
    }

    private fun drawSaveQrcode() {
        saveQrcodeTask = Observable.create<Bitmap> { emitter: ObservableEmitter<Bitmap?> ->
            val head = BitmapUtils.svgDrawableToBitmap(head_iv.drawable)
            val bitmap = BitmapUtils.getRoundedCornerBitmap(head,
                mContext.resources.getDimension(R.dimen.home_scan_qr_code_price_round_size))

            val qrCodeBitmap = QRCodeEncoder.syncEncodeQRCode(bean.qrCode,
                402.pt2px(),
                mContext.resources.getColor(R.color.color_131313),
                mContext.resources.getColor(R.color.color_FFFFFF),
                Bitmap.createScaledBitmap(bitmap,100,100,false))
            emitter.onNext(qrCodeBitmap)
        }.subscribeOn(Schedulers.newThread())
            .observeOn(AndroidSchedulers.mainThread())
            .subscribe { bitmap: Bitmap? ->
                save_QrCode_Iv.setImageBitmap(bitmap)
                downloadPicture()
                saveQrcodeTask.dispose()
            }
    }

    override fun initListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    save_tv -> {
                        drawSaveQrcode()
                    }

                    leftBg, close_tv -> {
                        onBackPressed()
                    }
                }
            }

        }.apply {
            save_tv.setOnClickListener(this)
            leftBg.setOnClickListener(this)
            close_tv.setOnClickListener(this)
        }
    }


    private fun downloadPicture() {
//        if (!isHasQrCode) {
//            ToastUtil.show(mContext,
//                ResourcesUtils.resToStr(mContext, R.string.qrcode_tip_store_fail))
//            return
//        }
        val subscribe: Disposable =
            mRxPermissions.request(Manifest.permission.WRITE_EXTERNAL_STORAGE)
                .subscribe({
//                    mShowMerName.setText(merName)
                    val bitmap: Bitmap = getViewBitmap(save_qrCode_bg)
                    if (FileUtils.saveBitmap2File(mContext, bitmap)) {
                        ToastUtil.show(mContext,
                            ResourcesUtils.resToStr(mContext, R.string.qrcode_tip_store_success))
                    } else {
                        ToastUtil.show(mContext,
                            ResourcesUtils.resToStr(mContext, R.string.qrcode_tip_store_fail))
                    }
                    bitmap.recycle()
                }, { throwable ->
                    Log.e("TAG", "accept: " + throwable.message)
                    ToastUtil.show(mContext,
                        ResourcesUtils.resToStr(mContext, R.string.qrcode_tip_store_fail))
                })
    }

    fun getViewBitmap(v: View): Bitmap {

        v.isDrawingCacheEnabled = true
        v.buildDrawingCache()
        v.measure(View.MeasureSpec.makeMeasureSpec(v.width, View.MeasureSpec.EXACTLY),
            View.MeasureSpec.makeMeasureSpec(v.height, View.MeasureSpec.EXACTLY))
        v.layout(v.x.toInt(),
            v.y.toInt(),
            v.x.toInt() + v.measuredWidth,
            v.y.toInt() + v.measuredHeight)
        val bitmap = Bitmap.createBitmap(v.drawingCache, 0, 0, v.measuredWidth, v.measuredHeight)
        v.isDrawingCacheEnabled = false
        v.destroyDrawingCache()
        return bitmap
    }

    fun getBitmapFromDrawable(context: Context?, @DrawableRes drawableId: Int): Bitmap {
        val drawable = ContextCompat.getDrawable(
            context!!, drawableId)
        return when (drawable) {
            is BitmapDrawable -> {
                drawable.bitmap
            }

            is VectorDrawable, is VectorDrawableCompat -> {
                val bitmap = Bitmap.createBitmap(drawable.intrinsicWidth,
                    drawable.intrinsicHeight,
                    Bitmap.Config.ARGB_8888)
                val canvas = Canvas(bitmap)
                drawable.setBounds(0, 0, canvas.width, canvas.height)
                drawable.draw(canvas)
                bitmap
            }

            else -> {
                throw IllegalArgumentException("unsupported drawable type")
            }
        }
    }

//    1 获取当前可见的View的图片，类似于手机截屏
//
//    View dView = getWindow().getDecorView();//获取DecorView
//    dView.setDrawingCacheEnabled(true);//生成View的副本，是一个Bitmap
//    Bitmap bmp = dView.getDrawingCache();//获取View的副本，就是这个View上面所显示的任何内容
//    ————————————————
//    版权声明：本文为CSDN博主「喷破天」的原创文章，遵循CC 4.0 BY-SA版权协议，转载请附上原文出处链接及本声明。
//    原文链接：https://blog.csdn.net/qq_15771061/article/details/69944156
}