package om.rrtx.mobile.homemodule.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.TextView;

import androidx.annotation.NonNull;

import om.rrtx.mobile.homemodule.R;

/**
 * 单独的对话框
 */
public class AloneDialog extends Dialog {

    private TextView mBottomTv;
    private TextView mContentTv;
    private AloneCallBack mAloneCallBack;
    private TextView mTitleTv;

    public AloneDialog(@NonNull Context context) {
        super(context, R.style.home_whiteRoundDialog);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.home_dialog_alone);

        setCancelable(false);
        setCanceledOnTouchOutside(false);

        setLocation();

        initView();

        initListener();
    }

    /**
     * 初始化控件
     */
    private void initView() {
        mBottomTv = findViewById(R.id.bottomTv);
        mContentTv = findViewById(R.id.contentTv);
        mTitleTv = findViewById(R.id.titleTv);
    }

    public void setLocation() {
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams WL = window.getAttributes();
            WL.height = ViewGroup.LayoutParams.WRAP_CONTENT;
            WL.width = ViewGroup.LayoutParams.WRAP_CONTENT;
            window.setGravity(Gravity.CENTER);
            window.setAttributes(WL);
        }
    }

    private void initListener() {
        mBottomTv.setOnClickListener((view -> {
            if (mAloneCallBack != null) {
                mAloneCallBack.clickEnter();
            }
        }));
    }

    /**
     * 设置标题显示的文字
     *
     * @param titleTv 标题文字
     */
    public AloneDialog setTitle(String titleTv) {
        if (!TextUtils.isEmpty(titleTv)) {
            mTitleTv.setText(titleTv);
        }
        return this;
    }

    /**
     * 设置标题显示的文字隐藏
     */
    public AloneDialog setTitleHint() {
        if (mTitleTv != null) {
            mTitleTv.setVisibility(View.GONE);
        }
        return this;
    }

    /**
     * 设置底部文字
     *
     * @param bottomStr 底部显示的文字
     */
    public AloneDialog setBottomStr(String bottomStr) {
        if (!TextUtils.isEmpty(bottomStr)) {
            mBottomTv.setText(bottomStr);
        }
        return this;
    }

    /**
     * 设置底部文字颜色
     *
     * @param rightColor 右边文字颜色
     */
    public AloneDialog setBottomColor(int rightColor) {
        mBottomTv.setTextColor(rightColor);
        return this;
    }

    /**
     * 设置中间显示的文字
     *
     * @param contentStr 底部显示的文字
     */
    public AloneDialog setContentStr(String contentStr) {
        if (!TextUtils.isEmpty(contentStr)) {
            mContentTv.setText(contentStr);
        }
        return this;
    }

    public AloneDialog setAloneCallBack(AloneCallBack aloneCallBack) {
        mAloneCallBack = aloneCallBack;
        return this;
    }

    /**
     * 按钮监听
     */
    public interface AloneCallBack {
        /**
         * 点击事件
         */
        void clickEnter();
    }
}
