package om.rrtx.mobile.homemodule.adapter;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentManager;
import androidx.fragment.app.FragmentPagerAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> Angle
 * 创建时间 : 2019/1/26 11:00
 * 描述 : 所有ViewPager的适配器
 */
public class BaseVPAdapter extends FragmentPagerAdapter {
    private List<Fragment> mFragments;

    public BaseVPAdapter(FragmentManager fm, List<Fragment> fragments) {
        super(fm);
        if (null != fragments) {
            mFragments = fragments;
        } else {
            mFragments = new ArrayList<>();
        }
    }

    @Override
    public Fragment getItem(int position) {
        return mFragments.get(position);
    }

    @Override
    public int getCount() {
        return mFragments == null ? 0 : mFragments.size();
    }
}
