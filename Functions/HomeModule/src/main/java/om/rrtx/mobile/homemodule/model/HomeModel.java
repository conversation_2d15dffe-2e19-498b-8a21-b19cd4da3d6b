package om.rrtx.mobile.homemodule.model;

import android.content.Context;
import android.text.TextUtils;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.RequestBody;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.functioncommon.bean.BalanceBean;
import om.rrtx.mobile.homemodule.bean.CommonSetBean;
import om.rrtx.mobile.homemodule.bean.JuniorAccountListBean;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.homemodule.HomeService;
import om.rrtx.mobile.homemodule.bean.ActivityBean;
import om.rrtx.mobile.rrtxcommon1.bean.AppDictListBean;
import om.rrtx.mobile.homemodule.bean.BannerBean;
import om.rrtx.mobile.homemodule.bean.HasNews;
import om.rrtx.mobile.homemodule.bean.InviteBean;
import om.rrtx.mobile.homemodule.bean.PortraitSettingBean;
import om.rrtx.mobile.homemodule.bean.PubBean;
import om.rrtx.mobile.homemodule.bean.QrcodeBean;
import om.rrtx.mobile.homemodule.bean.RegionBean;
import om.rrtx.mobile.homemodule.bean.SdkInfoBean;
import om.rrtx.mobile.homemodule.bean.UpDataBean;
import om.rrtx.mobile.homemodule.bean.UserQrCodeBean;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.bean.CheckAccountResultBean;
import om.rrtx.mobile.rrtxcommon1.net.BaseLoader;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.net.RetrofitServiceManager;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * 登录模块的整天请求
 */
public class HomeModel extends BaseLoader {

    private HomeService mHomeService;

    public HomeModel() {
        mHomeService = RetrofitServiceManager.getInstance().create(HomeService.class);
    }


    public void commonPub(BaseObserver<PubBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        observe(mHomeService.requestPub(map)).subscribe(baseObserver);
    }



    /**
     * 请求退出接口
     *
     * @param baseObserver 回调
     */
    public void requestLogout(String userId, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.USERID, userId);
        observe(mHomeService.requestLogout(map)).subscribe(baseObserver);
    }

    public void requestCustomerService(BaseObserver<CommonSetBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        observe(mHomeService.requestCustomerService(map)).subscribe(baseObserver);
    }

    /**
     * 解析二维码
     *
     * @param baseObserver 回调
     */
    public void requestAnalysisCode(String userId, String qrCode, BaseObserver<QrcodeBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.QRCODE, qrCode);
        map.put(UserConstants.Parameter.USERID, userId);
        observe(mHomeService.requestAnalysisCode(map)).subscribe(baseObserver);
    }

    /**
     * 获取二维码
     */
    public void requestQrCode(String userId, BaseObserver<UserQrCodeBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(UserConstants.Parameter.QRTYPE, UserConstants.QrType.PMP);
//        map.put(UserConstants.Parameter.USERID, userId);
        observe(mHomeService.requestQrCode(map)).subscribe(baseObserver);
    }

    /**
     * 是否有活动返现
     */
    public void requestIsHasActivity(String userId, BaseObserver<ActivityBean> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(UserConstants.Parameter.USERID, userId);
        observe(mHomeService.requestIsHasActivity(map)).subscribe(baseObserver);
    }

    /**
     * 编辑用户信息
     */
    public void requestEditInfo(String userId, String mobile, String mobileArea, String gender, String email, String nickName, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        if (!TextUtils.isEmpty(mobile) && !TextUtils.isEmpty(mobileArea)) {
            map.put(UserConstants.Parameter.MOBILE, mobile);
            map.put(UserConstants.Parameter.MOBILEAREACODE, mobileArea);
        }
        if (!TextUtils.isEmpty(gender)) {
            map.put(UserConstants.Parameter.GENDER, gender);
        }
        if (!TextUtils.isEmpty(email)) {
            map.put(UserConstants.Parameter.EMAIL, email);
        }
        if (!TextUtils.isEmpty(nickName)) {
            map.put(UserConstants.Parameter.NICKNAME, nickName);
        }
        map.put(UserConstants.Parameter.USERID, userId);
        observe(mHomeService.requestEditInfo(map)).subscribe(baseObserver);
    }

    /**
     * 获取时区接口
     */
    public void requestRegion(BaseObserver<List<RegionBean>> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        observe(mHomeService.requestRegion(map)).subscribe(baseObserver);
    }

    public void requestCheckRepeat(String userId, String mobile, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>(2);
        map.put(UserConstants.Parameter.MOBILE, mobile);
        map.put(UserConstants.Parameter.USERID, userId);
        observe(mHomeService.requestCheckRepeat(map)).subscribe(baseObserver);
    }

    /**
     * 上传头像
     *
     * @param userPhoto    头像文件
     * @param baseObserver 回调
     */
    public void uploadUserAvatar(File userPhoto, BaseObserver<PortraitSettingBean> baseObserver) {
        RequestBody requestFile =
                RequestBody.create(MediaType.parse("multipart/form-data"), userPhoto);

        MultipartBody.Part body =
                MultipartBody.Part.createFormData("file", userPhoto.getName(), requestFile);

        String authorization = (String) SharedPreferencesUtils.getParam(BaseApp.getAPPContext(), BaseConstants.SaveParameter.AUTHORIZATION, "");

        observe(mHomeService.uploadUserAvatar(authorization, body)).subscribe(baseObserver);
    }

    /**
     * 检查更新
     *
     * @param versionName
     * @param baseObserver 回调
     */
    public void requestUpData(String userId, String versionName, BaseObserver<UpDataBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.OSTYPE, "1");
        map.put(UserConstants.Parameter.USERID, userId);
        map.put(UserConstants.Parameter.VERSIONNO, String.valueOf(versionName));
        observe(mHomeService.requestUpData(map)).subscribe(baseObserver);
    }

    /**
     * 是否有新消息
     *
     * @param baseObserver 回调
     */
    public void requestNews(String userId, BaseObserver<HasNews> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.APPNAME, "0");
        map.put(UserConstants.Parameter.USERID, userId);
        observe(mHomeService.requestNews(map)).subscribe(baseObserver);
    }

    /**
     * 轮播图
     *
     * @param baseObserver 回调
     */
    public void requestBanners(String userId, String position, BaseObserver<BannerBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.POSITION, position);
        map.put(UserConstants.Parameter.USERID, userId);
        observe(mHomeService.requestBanners(map)).subscribe(baseObserver);
    }

    /**
     * 悬浮图
     *
     * @param baseObserver 回调
     */
    public void requestFloating(String userId, String position, BaseObserver<BannerBean.DetailsBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.POSITION, position);
        map.put(UserConstants.Parameter.USERID, userId);
        observe(mHomeService.requestFloating(map)).subscribe(baseObserver);
    }

    /**
     * 悬浮图
     *
     * @param baseObserver 回调
     */
    public void requestInviteData(String userId, BaseObserver<InviteBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.USERID, userId);
        observe(mHomeService.requestInviteData(map)).subscribe(baseObserver);

    }

    /**
     * 检查更新
     *
     * @param language     当前语言
     * @param baseObserver 回调
     */
    public void requestLanguageSetUp(String userId, String language, BaseObserver<Object> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.USERID, userId);
        map.put(UserConstants.Parameter.LANGUAGE, language);
        observe(mHomeService.requestLanguageSetUp(map)).subscribe(baseObserver);
    }


    /**
     * 查询SDK信息
     *
     * @param baseObserver 回调
     */
    public void requestXStoreInfo(String userId, BaseObserver<SdkInfoBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.USERID, userId);
        map.put(UserConstants.Parameter.SDKNAME, "xstore");
        observe(mHomeService.requestXStoreInfo(map)).subscribe(baseObserver);
    }

    /**
     * 查询用户币种
     *
     * @param baseObserver 回调
     */
    public void requestCurrencyList(String userId, BaseObserver<AppDictListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.USERID, userId);
        map.put("dictCode", "currency_type");
        observe(mHomeService.requestCurrencyList(map)).subscribe(baseObserver);
    }

    public void getJuniorList(int pageNum, int pageSize, BaseObserver<JuniorAccountListBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.PAGENUM, pageNum + "");
        map.put(UserConstants.Parameter.PAGESIZE, pageSize + "");
        observe(mHomeService.getJuniorList(map)).subscribe(baseObserver);
    }

    public void requestJuniorDeleteCheck(String juniorId, BaseObserver<CheckAccountResultBean> baseObserver) {
        Map<String, String> map = new HashMap<>();
        map.put(UserConstants.Parameter.JUNIOR_USER_ID, juniorId);
        observe(mHomeService.requestJuniorDeleteCheck(map)).subscribe(baseObserver);
    }


}
