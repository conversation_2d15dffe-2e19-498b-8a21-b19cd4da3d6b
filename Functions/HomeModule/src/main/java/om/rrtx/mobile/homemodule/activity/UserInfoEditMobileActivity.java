package om.rrtx.mobile.homemodule.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextUtils;
import android.text.TextWatcher;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.constraintlayout.widget.ConstraintLayout;

import com.alibaba.android.arouter.launcher.ARouter;
import com.google.android.material.textfield.TextInputEditText;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;

import java.util.List;

import butterknife.BindView;
import butterknife.ButterKnife;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functionapi.ITransferService;
import om.rrtx.mobile.functionapi.ServiceFactory;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.R2;
import om.rrtx.mobile.homemodule.bean.RegionBean;
import om.rrtx.mobile.homemodule.bean.SendMessageBean;
import om.rrtx.mobile.homemodule.presenter.UserInfoEditPresenter;
import om.rrtx.mobile.homemodule.view.UserInfoEditView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 编辑电话号码页面
 */
public class UserInfoEditMobileActivity extends BaseSuperActivity<UserInfoEditView, UserInfoEditPresenter>
        implements View.OnFocusChangeListener, TextWatcher, ITransferService.RegionCallBack, UserInfoEditView {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.hint)
    TextView mHint;
    @BindView(R2.id.mobileTv)
    TextView mMobileTv;
    @BindView(R2.id.mobileTitle)
    TextView mMobileTitle;
    @BindView(R2.id.codeTv)
    TextView mCodeTv;
    @BindView(R2.id.icon)
    ImageView mIcon;
    @BindView(R2.id.leftCl)
    ConstraintLayout mLeftCl;
    @BindView(R2.id.mobileEt)
    TextInputEditText mMobileEt;
    @BindView(R2.id.phoneIv)
    ImageView mPhoneIv;
    @BindView(R2.id.line3)
    View mLine3;
    @BindView(R2.id.confirmTv)
    TextView mConfirmTv;

    private List<RegionBean> mRegionBeans;
    private String[] mRegionArr;
    private String mCode;

    public static void jumpUserInfoEditMobile(Context context) {
        Intent intent = new Intent(context, UserInfoEditMobileActivity.class);
        context.startActivity(intent);
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    protected int createContentView() {
        return R.layout.home_activity_user_info_edit_mobile;
    }

    @Override
    protected UserInfoEditPresenter createPresenter() {
        return new UserInfoEditPresenter(mContext);
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.home_ic_back);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));
        mTitleTv.setText(R.string.personal_title_mobile);
    }

    @Override
    public void initDate() {
        super.initDate();

        String mobile = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERMOBILE, "");
        String mobileAreaCode = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.MOBILEAREACODE, "");
        mobile = StringUtils.stringMask(mobileAreaCode, mobile);
        mMobileTv.setText(mobile);
    }

    @Override
    public void initListener() {
        super.initListener();

        mMobileEt.setOnFocusChangeListener(this);
        mConfirmTv.setOnClickListener(mClickListener);
        mMobileEt.addTextChangedListener(this);
        mLeftCl.setOnClickListener(mClickListener);
        mLeftBg.setOnClickListener(mClickListener);
    }

    private CustomClickListener mClickListener = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.confirmTv) {
                String code = mCodeTv.getText().toString();
                String mobile = mMobileEt.getText().toString();
                mPresenter.requestCheckRepeat(code + mobile);
            } else if (view.getId() == R.id.leftCl) {
                if (mRegionArr != null) {
                    ServiceFactory.getInstance().getTransferService().showRegion(mContext, mRegionArr, UserInfoEditMobileActivity.this);
                    mIcon.setImageResource(R.drawable.login_ic_sanjiao_up);
                } else {
                    mPresenter.requestRegion();
                }
            } else if (view.getId() == R.id.leftBg) {
                finish();
            }
        }
    };

    @Override
    public void onFocusChange(View v, boolean hasFocus) {
        Editable text = mMobileEt.getText();
        mMobileTitle.setVisibility(View.VISIBLE);
        mLeftCl.setVisibility(View.VISIBLE);
        mMobileEt.setHint("");
        if (hasFocus && text != null && text.length() > 0) {
            mPhoneIv.setVisibility(View.VISIBLE);
        } else {
            mPhoneIv.setVisibility(View.GONE);
        }
    }

    @Override
    public void beforeTextChanged(CharSequence s, int start, int count, int after) {

    }

    @Override
    public void onTextChanged(CharSequence s, int start, int before, int count) {

    }

    @Override
    public void afterTextChanged(Editable s) {
        String editText = mMobileEt.getText().toString();
        if (!TextUtils.isEmpty(editText) && editText.length() > 5) {
            mConfirmTv.setBackgroundResource(R.drawable.common_usable_btn);
            mConfirmTv.setEnabled(true);
        } else {
            mConfirmTv.setBackgroundResource(R.drawable.common_unusable_btn);
            mConfirmTv.setEnabled(false);
        }
    }

    @Override
    public void confirmCallBack(int currentPosition) {
        if (mRegionBeans != null && mRegionBeans.size() > 0) {
            RegionBean regionBean = mRegionBeans.get(currentPosition);
            mCode = "+" + regionBean.getAreaCode();
            mCodeTv.setText(mCode);
            mIcon.setImageResource(R.drawable.home_ic_sanjiao_down);
        }
    }

    @Override
    public void cancelCallBack() {
        mIcon.setImageResource(R.drawable.home_ic_sanjiao_down);
    }

    @Override
    public void editSuccess(String email, String nickName) {
    }

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    public void regionSuccess(List<RegionBean> regionBeans) {
        if (regionBeans != null && regionBeans.size() > 0) {
            mRegionBeans = regionBeans;

            mRegionArr = new String[regionBeans.size()];

            for (int i = 0; i < regionBeans.size(); i++) {
                RegionBean regionBean = regionBeans.get(i);
                mRegionArr[i] = "+" + regionBean.getAreaCode() + " " + regionBean.getNationName();
            }

            ServiceFactory.getInstance().getTransferService().showRegion(mContext, mRegionArr, this);
            mIcon.setImageResource(R.drawable.login_ic_sanjiao_up);
        }
    }

    @Override
    public void checkRepeat() {
        String code = mCodeTv.getText().toString();
        String mobile = mMobileEt.getText().toString();

        SendMessageBean sendMessageBean = new SendMessageBean.Builder()
                .setMessageTemplateType(BaseConstants.MessageTemplateType.ModifyMobile)
                .setJumpFlag(BaseConstants.SendMessageJump.USERINFOEDITMOBILE)
                .setMobile(code + mobile)
                .setMobileAreaCode(code.replace("+", ""))
                .build();

        ARouter.getInstance().build(ARouterPath.SecurityPath.PayPinSendActivity)
                .withString(BaseConstants.Transmit.SENDMESSAGEJSON,new Gson().toJson(sendMessageBean))
                .navigation();
    }
}
