package om.rrtx.mobile.homemodule.activity;

import android.content.Context;
import android.content.Intent;
import android.widget.CompoundButton;

import com.gyf.immersionbar.ImmersionBar;

import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.databinding.HomeActivityBroadcastSettingBinding;
import om.rrtx.mobile.homemodule.utils.BaseClick;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BaseActivity;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * 播报设置
 */
public class BroadcastSettingActivity extends BaseActivity<HomeActivityBroadcastSettingBinding> implements CompoundButton.OnCheckedChangeListener {

    public static void jumpBroadcastSetting(Context context) {
        Intent intent = new Intent(context, BroadcastSettingActivity.class);
        context.startActivity(intent);
    }

    @Override
    public boolean isSupportSwipeBack() {
        return true;
    }

    @Override
    protected int createContentView() {
        return R.layout.home_activity_broadcast_setting;
    }

    @Override
    protected void initWorkspaceAction() {
        dataBinding.setBroadcastOpt(new BroadcastOpt());
    }

    @Override
    public void initView() {
        super.initView();
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        dataBinding.includeTitle.backIv.setImageResource(R.drawable.home_ic_back);

        dataBinding.includeTitle.statusView.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        dataBinding.includeTitle.titleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        dataBinding.includeTitle.titleTv.setTextColor(getResources().getColor(R.color.color_131313));
        dataBinding.includeTitle.titleTv.setText(R.string.broacast_title_broadcast_setting);

        //设置选择的内容
        boolean isFinger = (boolean) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ISBROADCAST, false);
        dataBinding.touchIdSwitch.setChecked(isFinger);
    }

    @Override
    public void initListener() {
        super.initListener();
        dataBinding.touchIdSwitch.setOnCheckedChangeListener(this);
    }

    @Override
    public void onCheckedChanged(CompoundButton buttonView, boolean isChecked) {
        dataBinding.touchIdSwitch.setChecked(isChecked);
        SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.ISBROADCAST, isChecked);
    }

    public class BroadcastOpt extends BaseClick {

        @Override
        public void leftClick() {
            finish();
        }
    }
}