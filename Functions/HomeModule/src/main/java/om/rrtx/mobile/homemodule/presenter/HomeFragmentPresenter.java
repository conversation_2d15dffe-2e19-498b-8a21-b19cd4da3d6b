package om.rrtx.mobile.homemodule.presenter;

import android.content.Context;
import android.text.TextUtils;

import om.rrtx.mobile.functioncommon.bean.BankNumBean;
import om.rrtx.mobile.functioncommon.model.CommonModel;
import om.rrtx.mobile.homemodule.bean.BannerBean;
import om.rrtx.mobile.homemodule.bean.HasNews;
import om.rrtx.mobile.homemodule.bean.PubBean;
import om.rrtx.mobile.homemodule.bean.QrcodeBean;
import om.rrtx.mobile.homemodule.model.HomeModel;
import om.rrtx.mobile.functioncommon.plugin.PluginModel;
import om.rrtx.mobile.homemodule.view.HomeFragmentView;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.net.BaseObserver;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 首页Tab的p层
 */
public class HomeFragmentPresenter extends BasePresenter<HomeFragmentView> {
    private String TAG = HomeFragmentPresenter.class.getSimpleName();
    private Context mContext;
    private HomeModel mHomeModel;
    private final PluginModel mPluginModel;

    public HomeFragmentPresenter(Context context) {
        mPluginModel = new PluginModel();
        mHomeModel = new HomeModel();
        mContext = context;
    }

    /**
     * <AUTHOR>
     * 解析二维码
     */
    public void requestAnalysisCode(String qrCode) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mHomeModel.requestAnalysisCode(userId,qrCode, new BaseObserver<QrcodeBean>(mContext) {
                @Override
                public void requestSuccess(QrcodeBean balanceBean) {
                    if (getView() != null) {
                        getView().analysisCodeSuccess(balanceBean);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if (getView() != null) {
                        getView().requestFail(sResMsg);
                    }
                }
            });
        }else {
            mHomeModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mHomeModel.requestAnalysisCode(userId,qrCode, new BaseObserver<QrcodeBean>(mContext) {
                        @Override
                        public void requestSuccess(QrcodeBean balanceBean) {
                            if (getView() != null) {
                                getView().analysisCodeSuccess(balanceBean);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if (getView() != null) {
                                getView().requestFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    /**
     * 是否有新消息
     */
    public void requestHasNews() {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mHomeModel.requestNews(userId,new BaseObserver<HasNews>(mContext) {
                @Override
                public void requestSuccess(HasNews sResData) {
                    if (getView() != null) {
                        getView().hasNewsSuccess(sResData);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if(getView()!=null){
                        getView().hasNewsFail(sResMsg);
                    }
                }
            });
        }else {
            mHomeModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mHomeModel.requestNews(userId,new BaseObserver<HasNews>(mContext) {
                        @Override
                        public void requestSuccess(HasNews sResData) {
                            if (getView() != null) {
                                getView().hasNewsSuccess(sResData);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if(getView()!=null){
                                getView().hasNewsFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    public void requestBanners(String position) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mHomeModel.requestBanners(userId,position, new BaseObserver<BannerBean>(mContext) {
                @Override
                public void requestSuccess(BannerBean sResData) {
                    if (getView() != null) {
                        getView().bannerSuccess(sResData);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                    if(getView()!=null){
                        getView().bannerFail(sResMsg);
                    }
                }
            });
        }else {
            mHomeModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mHomeModel.requestBanners(userId,position, new BaseObserver<BannerBean>(mContext) {
                        @Override
                        public void requestSuccess(BannerBean sResData) {
                            if (getView() != null) {
                                getView().bannerSuccess(sResData);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                            if(getView()!=null){
                                getView().bannerFail(sResMsg);
                            }
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    public void requestFloating(String position) {
        String pubLick = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, "");
        String userId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
        if (!TextUtils.isEmpty(pubLick)) {
            mHomeModel.requestFloating(userId,position, new BaseObserver<BannerBean.DetailsBean>(mContext) {
                @Override
                public void requestSuccess(BannerBean.DetailsBean sResData) {
                    if (getView() != null) {
                        getView().floatingSuccess(sResData);
                    }
                }

                @Override
                public void requestFail(String sResMsg) {
                }
            });
        }else {
            mHomeModel.commonPub(new BaseObserver<PubBean>(mContext) {
                @Override
                public void requestSuccess(PubBean sResData) {
                    SharedPreferencesUtils.setParam(mContext, BaseConstants.SaveParameter.PUBLICKEY, sResData.getPubKeyBase64());
                    mHomeModel.requestFloating(userId,position, new BaseObserver<BannerBean.DetailsBean>(mContext) {
                        @Override
                        public void requestSuccess(BannerBean.DetailsBean sResData) {
                            if (getView() != null) {
                                getView().floatingSuccess(sResData);
                            }
                        }

                        @Override
                        public void requestFail(String sResMsg) {
                        }
                    });
                }

                @Override
                public void requestFail(String sResMsg) {

                }
            });
        }
    }

    public void getBankNum() {
        new CommonModel().getBankNum(new BaseObserver<BankNumBean>(mContext) {
            @Override
            public void requestSuccess(BankNumBean sResData) {
                if (getView() != null) {
                    getView().getBankNumSuccess(sResData.getBankCardNum());
                }
            }

            @Override
            public void requestFail(String sResMsg) {
                ToastUtil.show(mContext, sResMsg);
            }
        });
    }
}
