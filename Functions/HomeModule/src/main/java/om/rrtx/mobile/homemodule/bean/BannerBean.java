package om.rrtx.mobile.homemodule.bean;

import java.util.List;

/**
 * <AUTHOR>
 */
public class BannerBean {

    private List<DetailsBean> details;

    public List<DetailsBean> getDetails() {
        return details;
    }

    public void setDetails(List<DetailsBean> details) {
        this.details = details;
    }

    public static class DetailsBean {
        /**
         * sequenceNo : 1
         * imageUrl : https://xwallet-pro.oss-cn-beijing.aliyuncs.com/xWalletPro/upload/advertisingPromotion/6057a6dc-4f7d-4987-aace-e2e48572de41.png
         * imageLinkType : 0
         * imageLink : TopUp
         */

        private String sequenceNo;
        private String imageUrl;
        private String imageLinkType;
        private String imageLink;

        public String getSequenceNo() {
            return sequenceNo;
        }

        public void setSequenceNo(String sequenceNo) {
            this.sequenceNo = sequenceNo;
        }

        public String getImageUrl() {
            return imageUrl;
        }

        public void setImageUrl(String imageUrl) {
            this.imageUrl = imageUrl;
        }

        public String getImageLinkType() {
            return imageLinkType;
        }

        public void setImageLinkType(String imageLinkType) {
            this.imageLinkType = imageLinkType;
        }

        public String getImageLink() {
            return imageLink;
        }

        public void setImageLink(String imageLink) {
            this.imageLink = imageLink;
        }
    }
}
