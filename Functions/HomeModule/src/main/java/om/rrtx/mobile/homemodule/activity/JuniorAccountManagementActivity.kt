package om.rrtx.mobile.homemodule.activity

import android.content.Context
import android.content.Intent
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.alibaba.android.arouter.facade.annotation.Route
import com.alibaba.android.arouter.launcher.ARouter
import com.gyf.immersionbar.ImmersionBar
import kotlinx.android.synthetic.main.home_activity_junior_account_management.hint_tv
import kotlinx.android.synthetic.main.home_activity_junior_account_management.next_tv
import kotlinx.android.synthetic.main.home_activity_junior_account_management.rv_account
import kotlinx.android.synthetic.main.home_activity_junior_account_management.s1_tv
import kotlinx.android.synthetic.main.home_base_title.leftBg
import kotlinx.android.synthetic.main.home_base_title.view.backIv
import kotlinx.android.synthetic.main.home_base_title.view.statusView
import kotlinx.android.synthetic.main.home_base_title.view.titleTv
import om.rrtx.mobile.functionapi.ARouterPath

import om.rrtx.mobile.homemodule.R
import om.rrtx.mobile.homemodule.adapter.JuniorAccountAdapter
import om.rrtx.mobile.homemodule.databinding.HomeActivityJuniorAccountManagementBinding
import om.rrtx.mobile.homemodule.viewmodel.JuniorAccountVM
import om.rrtx.mobile.rrtxcommon1.BaseConstants
import om.rrtx.mobile.rrtxcommon1.base.BaseVVMActivity
import om.rrtx.mobile.rrtxcommon1.kotlin.pt2px
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener
import om.rrtx.mobile.rrtxcommon1.utils.ResourceHelper
import om.rrtx.mobile.rrtxcommon1.widget.SpacesItemDecoration

@Route(path = ARouterPath.HomePath.JuniorAccountManagementActivity)
class JuniorAccountManagementActivity :
    BaseVVMActivity<JuniorAccountVM, HomeActivityJuniorAccountManagementBinding>() {

    private lateinit var mAdapter: JuniorAccountAdapter
    private var pageNum = 1
    private var pageSize = 10
    override fun createContentView() = R.layout.home_activity_junior_account_management

    override fun initView() {
        ImmersionBar.with(this).statusBarView(R.id.statusView).init()
        dataBinding.includeTitle.apply {
            titleTv.setText(R.string.junior_Account_Management)
            backIv.setBackgroundResource(R.drawable.ic_back_w)
            statusView.setBackgroundColor(ResourceHelper.getColor(R.color.common_ye_F3881E))
            titleTv.setBackgroundColor(resources.getColor(R.color.common_ye_F3881E))
        }

        rv_account.apply {
            layoutManager = LinearLayoutManager(mContext)
            mAdapter = JuniorAccountAdapter()
            adapter = mAdapter
            setEmptyView(hint_tv)
            addItemDecoration(SpacesItemDecoration(24.pt2px()))
        }
    }

    override fun initData() {
    }

    override fun onResume() {
        super.onResume()
        viewModel.getJuniorList(pageNum, pageSize)
    }

    override fun initListener() {
        object : CustomClickListener() {
            override fun onSingleClick(view: View?) {
                when (view) {
                    next_tv -> {
                        ARouter.getInstance().build(ARouterPath.LoginPath.NewUserInfoActivity)
                            .withString(BaseConstants.Transmit.JUMPFLAG,
                                BaseConstants.JumpFlag.Register_junior_Account).navigation()
                    }

                    leftBg -> {
                        onBackPressed()
                    }
                }
            }

        }.apply {
            leftBg.setOnClickListener(this)
            next_tv.setOnClickListener(this)
        }
    }

    override fun initVMListener() {
        viewModel.juniorListLD.observe(this) {
            mAdapter.setNewData(it.records)
            // 同 setEmptyView 逻辑
            val emptyViewVisible = mAdapter.itemCount == 0
            s1_tv.visibility = if (emptyViewVisible) View.GONE else View.VISIBLE
        }
    }

    companion object {
        fun jump(context: Context) {
            val intent = Intent(context, JuniorAccountManagementActivity::class.java)
            context.startActivity(intent)
        }
    }
}
