package om.rrtx.mobile.homemodule.activity;

import android.content.Context;
import android.content.Intent;
import android.net.http.SslError;
import android.os.Build;
import android.view.View;
import android.webkit.SslErrorHandler;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.R2;
import om.rrtx.mobile.rrtxcommon.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon.utils.CustomClickListener;


/**
 * <AUTHOR>
 * 展示邀请好友页面
 */
public class ShowWebViewActivity extends BaseSuperActivity {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.statusView)
    View mStatusView;
    @BindView(R2.id.showWeb)
    WebView showWeb;

    private String webUrl;
    private String mType;

    public static void jumpShowWebView(Context context, String url, String urlType) {
        Intent intent = new Intent(context, ShowWebViewActivity.class);
        intent.putExtra("URL", url);
        intent.putExtra("TYPE", urlType);
        context.startActivity(intent);
    }

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        webUrl = getIntent().getStringExtra("URL");
        mType = getIntent().getStringExtra("TYPE");
    }

    @Override
    protected int createContentView() {
        return R.layout.home_show_webview;
    }

    @Override
    protected BasePresenter createPresenter() {
        return null;
    }

    @Override
    public void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setImageResource(R.drawable.home_ic_back);
        mStatusView.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));
        if ("terms".equals(mType)) {
            mTitleTv.setText(R.string.about_btn_term_conditions);
        } else if ("policy".equals(mType)) {
            mTitleTv.setText(R.string.about_btn_privacy_policy);
        } else {
            mTitleTv.setText(R.string.common_app_name);
        }

        showWeb.loadUrl(webUrl);
        WebSettings settings = showWeb.getSettings();
        settings.setJavaScriptEnabled(true);
        showWeb.setWebViewClient(new WebViewClient() {
            @RequiresApi(api = Build.VERSION_CODES.LOLLIPOP)
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                view.loadUrl(String.valueOf(request.getUrl()));
                return super.shouldOverrideUrlLoading(view, request);
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                view.loadUrl(url);
                return super.shouldOverrideUrlLoading(view, url);
            }

            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                handler.cancel();
                super.onReceivedSslError(view, handler, error);
            }
        });

        mLeftBg.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                onBackPressed();
            }
        });
    }

    @Override
    public void onBackPressed() {
        if (showWeb.canGoBack()) {
            showWeb.goBack();
        } else {
            super.onBackPressed();
        }
    }

}