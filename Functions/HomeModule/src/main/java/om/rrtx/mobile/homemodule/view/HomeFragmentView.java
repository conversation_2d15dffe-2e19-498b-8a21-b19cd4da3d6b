package om.rrtx.mobile.homemodule.view;

import om.rrtx.mobile.functioncommon.bean.CurrencyAccountListBean;
import om.rrtx.mobile.homemodule.bean.BannerBean;
import om.rrtx.mobile.homemodule.bean.HasNews;
import om.rrtx.mobile.homemodule.bean.QrcodeBean;

/**
 * <AUTHOR>
 * 首页Tab的接口
 */
public interface HomeFragmentView {
    /**
     * 获取账户余额成功
     *
     * @param balanceBean 余额实体类
     */
    void balanceSuccess(CurrencyAccountListBean balanceBean);


    /**
     * <AUTHOR>
     * 解析二维码成功
     */
    void analysisCodeSuccess(QrcodeBean balanceBean);


    /**
     * <AUTHOR>
     * 请求失败
     */
    void requestFail(String sResMsg);

    /**
     * 是否有新消息
     *
     * @param sResData 新消息实体
     */
    void hasNewsSuccess(HasNews sResData);

    /**
     * 请求是否有新消息失败
     * @param sResMsg
     */
    void hasNewsFail(String sResMsg);

    void bannerSuccess(BannerBean sResData);

    void bannerFail(String sResMsg);

    void floatingSuccess(BannerBean.DetailsBean sResData);
    void getBankNumSuccess(String num);

}
