package om.rrtx.mobile.homemodule.bean;

import om.rrtx.mobile.rrtxcommon1.bean.UserInfoBean;

/**
 * <AUTHOR>
 */
public class QrcodeBean {

    /**
     * qrType : PMP
     * payeeInfo : {"payeeMobile":"","mobileAreaCode":"","amt":"","remark":"","realName":"","userAvatar":""}
     * merRecInfo : {"merNo":"","merName":"","checkstandNo":"","amt":""}
     * userInfo : {"realName":"heduoduo","mobile":"+60123123123123","nikeName":"","userAvatar":"","userName":"","isContact":"0","mobileAreaCode":"+60","email":"","gender":""}
     */

    private String qrType;
    private PayeeInfoBean payeeInfo;
    private MerRecInfoBean merRecInfo;
    private UserInfoBean userInfo;

    public String getQrType() {
        return qrType;
    }

    public void setQrType(String qrType) {
        this.qrType = qrType;
    }

    public PayeeInfoBean getPayeeInfo() {
        return payeeInfo;
    }

    public void setPayeeInfo(PayeeInfoBean payeeInfo) {
        this.payeeInfo = payeeInfo;
    }

    public MerRecInfoBean getMerRecInfo() {
        return merRecInfo;
    }

    public void setMerRecInfo(MerRecInfoBean merRecInfo) {
        this.merRecInfo = merRecInfo;
    }

    public UserInfoBean getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(UserInfoBean userInfo) {
        this.userInfo = userInfo;
    }

    public static class PayeeInfoBean {
        /**
         * payeeMobile :
         * mobileAreaCode :
         * amt :
         * remark :
         * realName :
         * userAvatar :
         */

        private String payeeMobile;
        private String mobileAreaCode;
        private String amt;
        private String remark;
        private String realName;
        private String userAvatar;
        private String currency;

        public String getPayeeMobile() {
            return payeeMobile;
        }

        public void setPayeeMobile(String payeeMobile) {
            this.payeeMobile = payeeMobile;
        }

        public String getMobileAreaCode() {
            return mobileAreaCode;
        }

        public void setMobileAreaCode(String mobileAreaCode) {
            this.mobileAreaCode = mobileAreaCode;
        }

        public String getAmt() {
            return amt;
        }

        public void setAmt(String amt) {
            this.amt = amt;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            this.remark = remark;
        }

        public String getRealName() {
            return realName;
        }

        public void setRealName(String realName) {
            this.realName = realName;
        }

        public String getUserAvatar() {
            return userAvatar;
        }

        public void setUserAvatar(String userAvatar) {
            this.userAvatar = userAvatar;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }
    }

    public static class MerRecInfoBean {
        /**
         * merNo :
         * merName :
         * checkstandNo :
         * amt :
         */

        private String merNo;
        private String merName;
        private String checkstandNo;
        private String amt;
        private String currency;

        public String getMerNo() {
            return merNo;
        }

        public void setMerNo(String merNo) {
            this.merNo = merNo;
        }

        public String getMerName() {
            return merName;
        }

        public void setMerName(String merName) {
            this.merName = merName;
        }

        public String getCheckstandNo() {
            return checkstandNo;
        }

        public void setCheckstandNo(String checkstandNo) {
            this.checkstandNo = checkstandNo;
        }

        public String getAmt() {
            return amt;
        }

        public void setAmt(String amt) {
            this.amt = amt;
        }

        public String getCurrency() {
            return currency;
        }

        public void setCurrency(String currency) {
            this.currency = currency;
        }
    }
}
