package om.rrtx.mobile.homemodule.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.MutableLiveData
import om.rrtx.mobile.homemodule.bean.JuniorAccountListBean
import om.rrtx.mobile.homemodule.model.HomeModel
import om.rrtx.mobile.rrtxcommon1.bean.CheckAccountResultBean
import om.rrtx.mobile.rrtxcommon1.net.BaseObserverNoError
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController

class JuniorAccountVM(application: Application) : AndroidViewModel(application) {

    private val mModel = HomeModel()

    val juniorListLD = MutableLiveData<JuniorAccountListBean>()
    val checkJuniorResultLD = MutableLiveData<CheckAccountResultBean>()
    fun getJuniorList(pageNum: Int, pageSize: Int) {
        val activity = ActivityController.getInstance().currentActivity()
        mModel.getJuniorList(pageNum,
            pageSize,
            object : BaseObserverNoError<JuniorAccountListBean>(activity) {
                override fun requestSuccess(bean: JuniorAccountListBean) {
                    juniorListLD.value = bean
                }
            })
    }

    fun requestJuniorDeleteCheck(juniorId: String) {
        val activity = ActivityController.getInstance().currentActivity()
        mModel.requestJuniorDeleteCheck(juniorId,
            object : BaseObserverNoError<CheckAccountResultBean>(activity) {
                override fun requestSuccess(bean: CheckAccountResultBean) {
                    checkJuniorResultLD.value = bean
                }
            })
    }
}