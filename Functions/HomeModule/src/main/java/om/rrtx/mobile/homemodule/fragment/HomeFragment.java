package om.rrtx.mobile.homemodule.fragment;

import android.Manifest;
import android.app.Activity;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.alibaba.android.arouter.core.LogisticsCenter;
import com.alibaba.android.arouter.facade.Postcard;
import com.alibaba.android.arouter.launcher.ARouter;
import com.google.gson.Gson;
import com.gyf.immersionbar.ImmersionBar;
import com.itingchunyu.badgeview.BaseBadgeView;
import com.kapp.xmarketing.ShowMarketingManager;
import com.tbruyelle.rxpermissions2.RxPermissions;
import com.youth.banner.Banner;
import com.youth.banner.listener.OnBannerListener;

import butterknife.BindView;
import io.reactivex.disposables.Disposable;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.functioncommon.CommonConstants;
import om.rrtx.mobile.homemodule.HomeConstants;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.R2;
import om.rrtx.mobile.functioncommon.activity.CustomFlutterActivity;
import om.rrtx.mobile.homemodule.adapter.HomeRvAdapter;
import om.rrtx.mobile.functioncommon.bean.CurrencyAccountListBean;
import om.rrtx.mobile.homemodule.bean.BannerBean;
import om.rrtx.mobile.homemodule.bean.ByMobileBean;
import om.rrtx.mobile.homemodule.bean.HasNews;
import om.rrtx.mobile.homemodule.bean.HomeTabBean;
import om.rrtx.mobile.homemodule.bean.NewRouteBean;
import om.rrtx.mobile.homemodule.bean.QrcodeBean;
import om.rrtx.mobile.homemodule.presenter.HomeFragmentPresenter;
import om.rrtx.mobile.homemodule.utils.GlideImageLoader;
import om.rrtx.mobile.homemodule.utils.JumpJudgementUtils;
import om.rrtx.mobile.homemodule.view.HomeFragmentView;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.base.MVPWithLazyBaseFragment;
import om.rrtx.mobile.rrtxcommon1.bean.QueryOrderBean;
import om.rrtx.mobile.rrtxcommon1.bean.UserInfoBean;
import om.rrtx.mobile.rrtxcommon1.dialog.PromotionPictureDialog;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.RVAdapterItemClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;
import om.rrtx.mobile.rrtxcommon1.utils.toast.ToastUtil;

/**
 * <AUTHOR>
 * 首页的Fragment
 */
public class HomeFragment extends MVPWithLazyBaseFragment<HomeFragmentView, HomeFragmentPresenter> implements HomeFragmentView, RVAdapterItemClickListener<HomeTabBean>, OnBannerListener {

    private final int REQUEST = 0x1;
    @BindView(R2.id.homeIv)
    ImageView mHomeIv;
    @BindView(R2.id.functionRv)
    RecyclerView mFunctionRv;
    @BindView(R2.id.nameTv)
    TextView mNameTv;
    @BindView(R2.id.dateTv)
    TextView mDateTv;
    @BindView(R2.id.receiveIv)
    ImageView mReceiveIv;
    @BindView(R2.id.receiveTv)
    TextView mReceiveTv;
    @BindView(R2.id.scanIv)
    ImageView mScanIv;
    @BindView(R2.id.paymentIv)
    ImageView mPaymentIv;
    @BindView(R2.id.paymentTv)
    TextView mPaymentTv;
    @BindView(R2.id.homeView)
    ConstraintLayout mHomeView;
    @BindView(R2.id.bbv)
    BaseBadgeView mBbv;
    @BindView(R2.id.newsIv)
    ImageView mNewsIv;
    @BindView(R2.id.bannerIv)
    Banner mBannerIv;
    private HomeCallBack mHomeCallBack;
    /**
     * 账户类型
     */
    private RxPermissions mRxPermissions;
    private String mQrCode = "https://xwallet-static.rrtx.vimbug.com/h5/test/pages/Download.html?source=orderQR&payToken=69625030402f2fc209bbf218a58dde905871d806456ed4009b30d939b177f0ed";

    private BannerBean mBannerBean;
    private PromotionPictureDialog mDialog;

    @Override
    protected int createViewLayoutId() {
        return R.layout.home_fragment_home;
    }

    @Override
    public HomeFragmentPresenter createPresenter() {
        return new HomeFragmentPresenter(mContext);
    }

    @Override
    protected void initView(View rootView) {
        ImmersionBar.with(this).statusBarDarkFont(true, 0.2f)
                .statusBarView(R.id.statusView, mHomeView).init();

        String fullName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERNAME, "");
        if (!TextUtils.isEmpty(fullName)) {
            String helloStr = getString(R.string.home_label_hi) + "," + fullName;
            mNameTv.setText(helloStr);
        }

        String lastLoginTime = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.LASTLOGINTIME, "");
        if (!TextUtils.isEmpty(lastLoginTime)) {
            String timeStr = getString(R.string.home_label_last_logintime) + " " + lastLoginTime;
            mDateTv.setVisibility(View.VISIBLE);
            mDateTv.setText(timeStr);
        }

        //初始化轮播图
        mBannerIv.setImageLoader(new GlideImageLoader());
        mBannerIv.setOnBannerListener(this);
    }

    @Override
    public void initDate() {
        super.initDate();

        if (getActivity() != null) {
            mRxPermissions = new RxPermissions(getActivity());
        }

        initRecycler();

        //请求轮播图接口
        mPresenter.requestBanners(BaseConstants.BannerPosition.HOME);

        if (BaseApp.isIsPromotion()) {
            mPresenter.requestFloating(BaseConstants.BannerPosition.HOME);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        boolean appState = BaseApp.isIsLock();
        boolean isFinger = (boolean) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ISFINGER, false);
        boolean isGesture = (boolean) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ISGESTURE, false);

        if (!appState && !isFinger && !isGesture) {
            mPresenter.requestHasNews();
        }
    }

    private void initRecycler() {
        mFunctionRv.setLayoutManager(new GridLayoutManager(mContext, 3) {
            @Override
            public boolean canScrollVertically() {
                return false;
            }
        });
        HomeRvAdapter adapter = new HomeRvAdapter(mContext);
        adapter.setItemClickListener(this);
        mFunctionRv.setAdapter(adapter);
    }

    @Override
    public void initListener() {
        super.initListener();
        mHomeIv.setOnClickListener(mHomeCustom);
        mReceiveIv.setOnClickListener(mHomeCustom);
        mReceiveTv.setOnClickListener(mHomeCustom);
        mScanIv.setOnClickListener(mHomeCustom);
        mPaymentIv.setOnClickListener(mHomeCustom);
        mPaymentTv.setOnClickListener(mHomeCustom);
        mNewsIv.setOnClickListener(mHomeCustom);
    }

    private CustomClickListener mHomeCustom = new CustomClickListener() {
        @Override
        public void onSingleClick(View view) {
            if (view.getId() == R.id.homeIv) {
                if (mHomeCallBack != null) {
                    mHomeCallBack.homeCallBack();
                }
            } else if (view.getId() == R.id.receiveIv || view.getId() == R.id.receiveTv) {
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.ReceivePaymentJump, new JumpJudgementUtils.JumpCallBack() {
                    @Override
                    public void normalClick() {
                        ARouter.getInstance().build(ARouterPath.Payment.ReceivePaymentActiity).navigation();
                    }
                });
            } else if (view.getId() == R.id.scanIv || view.getId() == R.id.scanTv) {
                jumpScanCode();
            } else if (view.getId() == R.id.paymentTv || view.getId() == R.id.paymentIv) {
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.MakePaymentJump, () -> {
                    ARouter.getInstance().build(ARouterPath.Payment.MakePaymentAllActivity).navigation();
                });
            } else if (view.getId() == R.id.newsIv) {
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.JumpFlag.HOMEJUMP, () -> {
                    NewRouteBean newRouteBean = new NewRouteBean();
                    //获取相应的meta-data标签内容
                    String url = BaseConstants.Urls.FLUTTER_FAT_URL;
                    try {
                        ApplicationInfo applicationInfo = getActivity().getPackageManager().getApplicationInfo(getActivity().getPackageName(), PackageManager.GET_META_DATA);
                        String appType = applicationInfo.metaData.getString("appType");
                        if (!TextUtils.isEmpty(appType)) {
                            switch (appType) {
                                case "dev":
                                case "test":
                                    url = BaseConstants.Urls.FLUTTER_TEST_URL;
                                    break;
                                case "uat":
                                    url = BaseConstants.Urls.FLUTTER_UAT_URL;
                                    break;
                                case "fat":
                                    url = BaseConstants.Urls.FLUTTER_FAT_URL;
                                    break;
                                default:
                                    url = BaseConstants.Urls.FLUTTER_FAT_URL;
                            }
                        }
                        newRouteBean.setBaseUrl(url);
                    } catch (PackageManager.NameNotFoundException e) {
                        e.printStackTrace();
                    }
                    newRouteBean.setRouteName("newsList");
                    newRouteBean.setParams("nihao");
                    //https://flutter.cn/docs/development/add-to-app/android/add-flutter-screen
                    Intent build = CustomFlutterActivity.withNewEngine(CustomFlutterActivity.class).initialRoute(new Gson().toJson(newRouteBean)).build(mContext);
                    mContext.startActivity(build);
                });
                mBbv.setBadgeShown(false);
            }
        }
    };

    private void jumpScanCode() {
        Disposable subscribe = mRxPermissions.request(Manifest.permission.CAMERA).subscribe((isSuccess) -> {
            if (isSuccess) {
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.ScanCodeJump, () -> {
                    Postcard postcard = ARouter.getInstance().build(ARouterPath.Payment.ScanCoceActiity);
                    LogisticsCenter.completion(postcard);
                    Intent intent = new Intent(getActivity(), postcard.getDestination());
                    intent.putExtras(postcard.getExtras());
                    startActivityForResult(intent, REQUEST);
                });
            }
        });
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == REQUEST && resultCode == Activity.RESULT_OK) {
            if (data != null) {
                mQrCode = data.getStringExtra(BaseConstants.Transmit.QRCODE);
                disposeCode();
            }
        }

        if (requestCode == CommonConstants.ResultCode.Request &&
                resultCode == CommonConstants.ResultCode.Success) {
            jumpScanCode();
        }
    }

    private void disposeCode() {
        Log.e("扫码归来", mQrCode);
        if (mQrCode.contains("http") || mQrCode.contains("https")) {
            //扫码前缀
            //返回的格式
            //这里解析的第一个参数是网址,第二个参数是参数集合
            String[] paramsSp = mQrCode.split("\\?");

            if (paramsSp.length > 1) {
                //这里说明后面有参数
                String[] split = paramsSp[1].split("&");
                if (split.length >= 1) {
                    //这里设置相应的参数
                    // 这个只有短信的时候有
                    String[] source = split[0].split("=");
                    // 这个只有支付的时候有
                    String[] payToken = split[1].split("=");

                    if (source.length == 1 || payToken.length == 1) {
                        Log.e("TAG", "onActivityResult: 解析参数有误");
                        return;
                    }

                    Log.e("TAG", "onActivityResult: " + source[1] + "---" + payToken[1]);

                    QueryOrderBean outJumpBean = new QueryOrderBean(payToken[1], "", source[1], "");

                    jumpCashierPay(outJumpBean);

                }
            } else {
                Log.e("TAG", "onActivityResult: 扫码参数异常");
            }
        } else {
            mPresenter.requestAnalysisCode(mQrCode);
        }
    }

    private void jumpCashierPay(QueryOrderBean outJumpBean) {
        Postcard postcard = ARouter.getInstance().build(ARouterPath.Cashier.CashierPayActivity)
                .withString(BaseConstants.Transmit.JSON, new Gson().toJson(outJumpBean));
        LogisticsCenter.completion(postcard);
        Intent intent = new Intent(getActivity(), postcard.getDestination());
        intent.putExtras(postcard.getExtras());
        startActivityForResult(intent, CommonConstants.ResultCode.Request);
    }

    public void setHomeCallBack(HomeCallBack homeCallBack) {
        mHomeCallBack = homeCallBack;
    }

    @Override
    public void balanceSuccess(CurrencyAccountListBean balanceBean) {
        Log.e("done", "balanceSuccess: " + balanceBean.toString());
    }

    @Override
    public void analysisCodeSuccess(QrcodeBean balanceBean) {
        //个人收款信息
        if (TextUtils.equals(UserConstants.QrType.PSK, balanceBean.getQrType())) {
            QrcodeBean.PayeeInfoBean payeeInfo = balanceBean.getPayeeInfo();
            String amt = payeeInfo.getAmt();
            ByMobileBean byMobileBean = new ByMobileBean();
            byMobileBean.setAmount(payeeInfo.getAmt());
            byMobileBean.setMobile(payeeInfo.getPayeeMobile());
            byMobileBean.setRealName(payeeInfo.getRealName());
            byMobileBean.setNikeName(payeeInfo.getRealName());
            byMobileBean.setUserAvatar(payeeInfo.getUserAvatar());
            byMobileBean.setRemark(payeeInfo.getRemark());
            byMobileBean.setCurrency(payeeInfo.getCurrency());
            byMobileBean.setMobileAreaCode(payeeInfo.getMobileAreaCode());
            if (TextUtils.isEmpty(amt)) {
                //转账详情
                ARouter.getInstance().build(ARouterPath.TransferPath.TransferDetailsActivity).withString(BaseConstants.Transmit.FROMTYPE, BaseConstants.JumpFlag.SEND_MONEY).withString(CommonConstants.Transmit.JSON, new Gson().toJson(byMobileBean)).navigation();
            } else {
                //固定转账页面
                //转账详情
                ARouter.getInstance().build(ARouterPath.TransferPath.TransferFixedActivity).withString(CommonConstants.Transmit.JSON, new Gson().toJson(byMobileBean)).withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP).navigation();
            }
        } else if (TextUtils.equals(UserConstants.QrType.MER, balanceBean.getQrType())) {
            //扫描商户的二维码
            QrcodeBean.MerRecInfoBean merRecInfo = balanceBean.getMerRecInfo();
            String amt = merRecInfo.getAmt();
            if (TextUtils.isEmpty(amt)) {
                QueryOrderBean queryOrderBean = new QueryOrderBean("", "", "", "",
                        merRecInfo.getMerNo(), merRecInfo.getMerName(),
                        merRecInfo.getCheckstandNo(), "",
                        mQrCode, merRecInfo.getCurrency(), CommonConstants.PaymentProduct.QRCODE);

                ARouter.getInstance().build(ARouterPath.Payment.BeSweptUnfixedPaymentActivity)
                        .withString(BaseConstants.Transmit.JSON, new Gson().toJson(queryOrderBean))
                        .withString(UserConstants.Transmit.QRCODE, mQrCode).navigation();
            } else {
                QueryOrderBean queryOrderBean = new QueryOrderBean("", "", "", "",
                        merRecInfo.getMerNo(), merRecInfo.getMerName(),
                        merRecInfo.getCheckstandNo(), merRecInfo.getAmt(),
                        mQrCode, merRecInfo.getCurrency(), CommonConstants.PaymentProduct.QRCODE);
                jumpCashierPay(queryOrderBean);

            }
        } else if (TextUtils.equals(UserConstants.QrType.PMP, balanceBean.getQrType())) {
            UserInfoBean userInfo = balanceBean.getUserInfo();
            String json = new Gson().toJson(userInfo);
            if (userInfo.getIsContact().equals("0")) {
                //添加联系人
                ARouter.getInstance().build(ARouterPath.TransferPath.AddContactsActivity).
                        withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP).
                        withString(CommonConstants.Transmit.JSON, json).
                        navigation();
            } else {
                //联系人详情
                ARouter.getInstance().build(ARouterPath.TransferPath.ContactDetailsActivity).
                        withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEJUMP).
                        withString(CommonConstants.Transmit.JSON, json).
                        navigation();
            }
        }
    }

    @Override
    public void requestFail(String sResMsg) {
        ToastUtil.show(mContext, sResMsg);
    }

    @Override
    public void hasNewsSuccess(HasNews sResData) {
        String hasNew = sResData.getHasNew();
        if (TextUtils.equals(hasNew, "0")) {
            //没有新消息
            mBbv.setBadgeShown(false);
        } else {
            //有新消息
            mBbv.setBadgeShown(true);
        }
    }

    @Override
    public void hasNewsFail(String sResMsg) {
        mBbv.setBadgeShown(false);
    }

    @Override
    public void bannerSuccess(BannerBean sResData) {
        if (sResData != null && sResData.getDetails() != null && sResData.getDetails().size() > 0) {
            mBannerBean = sResData;

            //设置图片集合
            mBannerIv.setImages(sResData.getDetails());
            //设置轮播时间
            mBannerIv.setDelayTime(5000);
            //banner设置方法全部调用完毕时最后调用
            mBannerIv.start();
        } else {
            mBannerIv.setBackgroundResource(R.drawable.home_banner_1);
        }
    }

    @Override
    public void bannerFail(String sResMsg) {
        mBannerIv.setBackgroundResource(R.drawable.home_banner_1);
    }

    @Override
    public void floatingSuccess(BannerBean.DetailsBean sResData) {
        if (sResData != null && !TextUtils.isEmpty(sResData.getImageUrl())) {
            mDialog = new PromotionPictureDialog(mContext, sResData.getImageUrl());
            mDialog.setPictureCallBack(new PromotionPictureDialog.PromotionPictureCallBack() {
                @Override
                public void clickShowPicture() {
                    JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.HomeAccountJump, () -> {
                        adAndBannerJump(sResData.getImageLinkType(), sResData.getImageLink());
                    });
                }
            });
            mDialog.show();
            BaseApp.setIsPromotion(false);
        }
    }

    @Override
    public void getBankNumSuccess(String num) {
        if (StringUtils.isValidString(num) && Integer.valueOf(num) > 0) {
            ARouter.getInstance().build(ARouterPath.TopUp.TopUpActivity)
                    .withString(BaseConstants.HomeJumpType.JUMP_HOME,BaseConstants.OrderSourceType.HOME_SOURCE)
                    .navigation();
        } else {
            ToastUtil.show(mContext, getString(R.string.no_Linked_Bank_Accounts));
        }
    }

    /**Banner点击事件**/
    @Override
    public void OnBannerClick(int position) {

        Log.e("TAG", "OnBannerClick: " + position);
        if (mBannerBean != null && mBannerBean.getDetails() != null && mBannerBean.getDetails().size() > 0) {
            JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.HomeAccountJump, () -> {
                BannerBean.DetailsBean detailsBean = mBannerBean.getDetails().get(position);
                adAndBannerJump(detailsBean.getImageLinkType(), detailsBean.getImageLink());
            });
        }
    }

    //banner和广告图的点击跳转
    private void adAndBannerJump(String imageLinkType,String imageLink){
        if (TextUtils.equals(imageLinkType, "0")) {
            //内部
            if (TextUtils.equals(imageLink, BaseConstants.BannerJumpState.TOPUP)) {
                //1 充值
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.TopUpJump, () -> {
                    ARouter.getInstance().build(ARouterPath.TopUp.TopUpActivity)
                            .withString(BaseConstants.HomeJumpType.JUMP_HOME,BaseConstants.OrderSourceType.HOME_SOURCE)
                            .navigation();
                });
            }else if (imageLink.contains(BaseConstants.BannerJumpState.SENDMONEY)) {
                //2 转账
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.TransferJump, () -> {
                    //转账页面
                    ARouter.getInstance().build(ARouterPath.TransferPath.TransferActivity)
                            .withString(BaseConstants.HomeJumpType.JUMP_HOME,BaseConstants.OrderSourceType.HOME_SOURCE)
                            .navigation();
                });
            }else if (imageLink.contains(BaseConstants.BannerJumpState.LR)) {
                //账户类型 0-常规账户 1-亲子账户
                String accountType= (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ACCOUNTTYPE,"0");
                if ("1".equals(accountType))
                    return;
                //3 奖励
                String userName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERNAME, "");
                String realName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.REALNAME, "");
                String userMobile = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERMOBILE, "");
                String userCardId = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.USERID, "");
                String currencyList = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ACTIVATE_CURRENCY, "");
                Log.e("sdk=","userName="+userName+"=realName="+realName+"=userMobile="+userMobile+"=userCardId="+userCardId+"=currencyList="+currencyList);
                ShowMarketingManager.getInstance().jumpCardWalletActivity(userCardId, realName, userName, userMobile, "",currencyList);
            }else if (imageLink.contains(BaseConstants.BannerJumpState.BUY_AB)) {
                //4 话费流量充值
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.HistoryJump, () -> {
                    ARouter.getInstance().build(ARouterPath.Payment.BuyAirtimeActivity).navigation();
                });
            }else if (imageLink.contains(BaseConstants.BannerJumpState.ZESA)) {
                //5 电力缴费
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.HistoryJump, () -> {
                    ARouter.getInstance().build(ARouterPath.Payment.BillPayTypeActivity).navigation();
                });
            }else if (imageLink.contains(BaseConstants.BannerJumpState.INVITE_FRIENDS)) {
                //6 邀请好友
                //账户类型 0-常规账户 1-亲子账户
                String accountType= (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ACCOUNTTYPE,"0");
                if ("1".equals(accountType))
                    return;
                ARouter.getInstance().build(ARouterPath.HomePath.ShowInviteActivity).navigation();
            }else if (imageLink.contains(BaseConstants.BannerJumpState.JUNIOR_ACCOUNT)) {
                //7 亲子账户
                //账户类型 0-常规账户 1-亲子账户
                String accountType= (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.ACCOUNTTYPE,"0");
                if ("1".equals(accountType))
                    return;
                ARouter.getInstance().build(ARouterPath.HomePath.JuniorAccountManagementActivity)
                        .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK|Intent.FLAG_ACTIVITY_CLEAR_TOP).navigation();
            }
        } else {
            if (!TextUtils.isEmpty(imageLink)) {
                if (!imageLink.startsWith("http://") || !imageLink.startsWith("https://")) {
                    imageLink = "http://" + imageLink;
                }
                //外部
                ARouter.getInstance().build(ARouterPath.Promotion.ShowWebActivity).withString(BaseConstants.Transmit.WEBVIEWURL, imageLink).navigation();
            }
        }
    }

    @Override
    public void itemClickListener(HomeTabBean homeTabBean, int position) {
        switch (homeTabBean.getType()) {
            case HomeConstants.FunctionType.TOP_UP:
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.TopUpJump, () -> {
                    mPresenter.getBankNum();
                });
                break;
            case HomeConstants.FunctionType.WITH_DRAW:
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.WithdrawalJump, () -> {
                    ARouter.getInstance().build(ARouterPath.TransferPath.WithdrawalActivity)
                            .withString(BaseConstants.HomeJumpType.JUMP_HOME,BaseConstants.OrderSourceType.HOME_SOURCE)
                            .navigation();
                });
                break;
            case HomeConstants.FunctionType.SEND_MONEY:
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.TransferJump, () -> {
                    //转账页面
                    ARouter.getInstance().build(ARouterPath.TransferPath.TransferActivity)
                            .withString(BaseConstants.HomeJumpType.JUMP_HOME,BaseConstants.OrderSourceType.HOME_SOURCE)
                            .navigation();
                });
                break;
            case HomeConstants.FunctionType.HISTORY:
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.HistoryJump, () -> {
                ARouter.getInstance().build(ARouterPath.TransferPath.HistoryActivity).navigation();
                });
                break;
            case HomeConstants.FunctionType.Bill_Pay:
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.HistoryJump, () -> {
                    ARouter.getInstance().build(ARouterPath.Payment.BillPayTypeActivity).navigation();
                });
                break;
            case HomeConstants.FunctionType.AIRTIME:
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.HistoryJump, () -> {
                    ARouter.getInstance().build(ARouterPath.Payment.BuyAirtimeActivity).navigation();
                });
                break;
            case HomeConstants.FunctionType.Nearby_Mer:
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.HistoryJump, () -> {
                    ARouter.getInstance().build(ARouterPath.Promotion.MerListActivity).navigation();
                });
                break;
            case HomeConstants.FunctionType.MORE:
                JumpJudgementUtils.lockJudgement(mContext, BaseConstants.HomeJumpType.HistoryJump, () -> {
                    ARouter.getInstance().build(ARouterPath.HomePath.MoreFunctionActivity).navigation();
                });
                break;
        }
    }

    /**
     * homeFragment的回调
     */
    public interface HomeCallBack {
        /**
         * 左上角页面被点击了
         */
        void homeCallBack();
    }

    public void logout() {
        mNameTv.setText(R.string.home_label_hi_pls_login);
        mDateTv.setVisibility(View.GONE);
    }

    public void login() {
        String fullName = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.REALNAME, "");
        if (!TextUtils.isEmpty(fullName)) {
            String helloStr = getString(R.string.home_label_hi) + "," + fullName;
            mNameTv.setText(helloStr);
        }

        String lastLoginTime = (String) SharedPreferencesUtils.getParam(mContext, BaseConstants.SaveParameter.LASTLOGINTIME, "");
        if (!TextUtils.isEmpty(lastLoginTime)) {
            String timeStr = getString(R.string.home_label_last_logintime) + " " +lastLoginTime;
            mDateTv.setText(timeStr);
        }
        mDateTv.setVisibility(View.VISIBLE);
    }
}
