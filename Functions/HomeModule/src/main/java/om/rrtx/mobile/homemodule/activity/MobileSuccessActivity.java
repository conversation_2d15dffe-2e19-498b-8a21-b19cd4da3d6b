package om.rrtx.mobile.homemodule.activity;

import android.content.Intent;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.gyf.immersionbar.ImmersionBar;

import butterknife.BindView;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.rrtxcommon1.UserConstants;
import om.rrtx.mobile.homemodule.R;
import om.rrtx.mobile.homemodule.R2;
import om.rrtx.mobile.rrtxcommon1.base.BasePresenter;
import om.rrtx.mobile.rrtxcommon1.base.BaseSuperActivity;
import om.rrtx.mobile.rrtxcommon1.utils.CustomClickListener;
import om.rrtx.mobile.rrtxcommon1.utils.StringUtils;

@Route(path = ARouterPath.HomePath.MobileSuccessActivity)
public class MobileSuccessActivity extends BaseSuperActivity {

    @BindView(R2.id.titleTv)
    TextView mTitleTv;
    @BindView(R2.id.leftBg)
    View mLeftBg;
    @BindView(R2.id.backIv)
    ImageView mBackIv;
    @BindView(R2.id.icon)
    ImageView mIcon;
    @BindView(R2.id.success)
    TextView mSuccess;
    @BindView(R2.id.hintTv)
    TextView mHintTv;
    @BindView(R2.id.completeTv)
    TextView mCompleteTv;
    private String mMobile;
    private String mMobileAreaCode;

    @Override
    public void doGetExtra() {
        super.doGetExtra();
        mMobile = getIntent().getStringExtra(UserConstants.Transmit.MOBILE);
        mMobileAreaCode = getIntent().getStringExtra(UserConstants.Transmit.MOBILEAREACODE);
    }

    @Override
    protected int createContentView() {
        return R.layout.home_activity_mobile_success;
    }

    @Override
    protected BasePresenter createPresenter() {
        return null;
    }

    @Override
    protected void initView() {
        ImmersionBar.with(this)
                .statusBarView(R.id.statusView)
                .statusBarDarkFont(true, 0.2f)
                .init();

        mBackIv.setVisibility(View.GONE);

        mTitleTv.setBackgroundColor(getResources().getColor(R.color.color_FFFFFF));
        mTitleTv.setTextColor(getResources().getColor(R.color.color_131313));
        mTitleTv.setText(R.string.SMS_title_mobile_verification);
    }

    @Override
    public void initDate() {
        super.initDate();
        String userMobile = StringUtils.stringMask(mMobileAreaCode, mMobile);
        mHintTv.setText(userMobile);
    }

    @Override
    public void initListener() {
        super.initListener();
        mCompleteTv.setOnClickListener(new CustomClickListener() {
            @Override
            public void onSingleClick(View view) {
                Intent intent = new Intent(mContext, PersonalInfoActivity.class);
                intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP);
                intent.putExtra(UserConstants.Transmit.MOBILE, mMobile);
                intent.putExtra(UserConstants.Transmit.MOBILEAREACODE, mMobileAreaCode);
                startActivity(intent);
            }
        });
    }

    @Override
    public void onBackPressed() {
        Intent intent = new Intent(mContext, PersonalInfoActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.putExtra(UserConstants.Transmit.MOBILE, mMobile);
        intent.putExtra(UserConstants.Transmit.MOBILEAREACODE, mMobileAreaCode);
        startActivity(intent);
    }
}
