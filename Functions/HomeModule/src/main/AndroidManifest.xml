<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="om.rrtx.mobile.homemodule">

    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />

    <application>
        <activity
            android:name=".activity.HomeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.MoreFunctionActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.PersonalInfoActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.JuniorPersonalInfoActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.MyQrCodeActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.UserInfoEditActivity"
            android:screenOrientation="portrait"
            android:theme="@style/editTextTheme"
            android:windowSoftInputMode="adjustPan|stateVisible" />
        <activity
            android:name=".activity.UserInfoEditMobileActivity"
            android:screenOrientation="portrait"
            android:theme="@style/editTextTheme"
            android:windowSoftInputMode="adjustPan|stateVisible" />
        <activity
            android:name=".activity.MobileSuccessActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.AboutActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.ShowWebViewActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.PortraitSettingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="om.rrtx.mobile.functioncommon.activity.CustomFlutterActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name="io.flutter.embedding.android.FlutterActivity"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustResize" />
        <activity
            android:name=".activity.ContactUsActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.ShowInviteActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.BroadcastSettingActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.JuniorAccountManagementActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        <activity
            android:name=".activity.JuniorAccountActivity"
            android:screenOrientation="portrait"
            android:theme="@style/APPThemeNoState"
            android:windowSoftInputMode="adjustPan|stateHidden" />
        </application>

</manifest>