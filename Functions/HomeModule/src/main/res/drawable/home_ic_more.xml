<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="82dp"
    android:height="82dp"
    android:viewportWidth="82"
    android:viewportHeight="82">
  <path
      android:pathData="M82,41Q82,42.007 81.951,43.012Q81.901,44.017 81.803,45.019Q81.704,46.02 81.556,47.016Q81.409,48.012 81.212,48.999Q81.016,49.986 80.771,50.962Q80.527,51.938 80.235,52.902Q79.942,53.865 79.603,54.813Q79.264,55.76 78.879,56.69Q78.494,57.62 78.064,58.53Q77.633,59.44 77.159,60.327Q76.684,61.215 76.167,62.078Q75.649,62.942 75.09,63.778Q74.531,64.615 73.932,65.424Q73.332,66.232 72.693,67.01Q72.055,67.788 71.379,68.534Q70.703,69.28 69.991,69.991Q69.28,70.703 68.534,71.379Q67.788,72.055 67.01,72.693Q66.232,73.332 65.424,73.932Q64.615,74.531 63.778,75.09Q62.942,75.649 62.078,76.167Q61.215,76.684 60.327,77.159Q59.44,77.633 58.53,78.064Q57.62,78.494 56.69,78.879Q55.76,79.264 54.813,79.603Q53.865,79.942 52.902,80.235Q51.938,80.527 50.962,80.771Q49.986,81.016 48.999,81.212Q48.012,81.409 47.016,81.556Q46.02,81.704 45.019,81.803Q44.017,81.901 43.012,81.951Q42.007,82 41,82Q39.993,82 38.988,81.951Q37.983,81.901 36.981,81.803Q35.98,81.704 34.984,81.556Q33.988,81.409 33.001,81.212Q32.014,81.016 31.038,80.771Q30.062,80.527 29.098,80.235Q28.135,79.942 27.188,79.603Q26.24,79.264 25.31,78.879Q24.38,78.494 23.47,78.064Q22.56,77.633 21.673,77.159Q20.785,76.684 19.922,76.167Q19.059,75.649 18.222,75.09Q17.385,74.531 16.576,73.932Q15.768,73.332 14.99,72.693Q14.212,72.055 13.466,71.379Q12.72,70.703 12.009,69.991Q11.297,69.28 10.621,68.534Q9.945,67.788 9.307,67.01Q8.668,66.232 8.068,65.424Q7.469,64.615 6.91,63.778Q6.351,62.942 5.833,62.078Q5.316,61.215 4.841,60.327Q4.367,59.44 3.936,58.53Q3.506,57.62 3.121,56.69Q2.736,55.76 2.397,54.813Q2.058,53.865 1.765,52.902Q1.473,51.938 1.229,50.962Q0.984,49.986 0.788,48.999Q0.591,48.012 0.444,47.016Q0.296,46.02 0.197,45.019Q0.099,44.017 0.049,43.012Q0,42.007 0,41Q0,39.993 0.049,38.988Q0.099,37.983 0.197,36.981Q0.296,35.98 0.444,34.984Q0.591,33.988 0.788,33.001Q0.984,32.014 1.229,31.038Q1.473,30.062 1.765,29.098Q2.058,28.135 2.397,27.188Q2.736,26.24 3.121,25.31Q3.506,24.38 3.936,23.47Q4.367,22.56 4.841,21.673Q5.316,20.785 5.833,19.922Q6.351,19.059 6.91,18.222Q7.469,17.385 8.068,16.576Q8.668,15.768 9.307,14.99Q9.945,14.212 10.621,13.466Q11.297,12.72 12.009,12.009Q12.72,11.297 13.466,10.621Q14.212,9.945 14.99,9.307Q15.768,8.668 16.576,8.068Q17.385,7.469 18.222,6.91Q19.059,6.351 19.922,5.833Q20.785,5.316 21.673,4.841Q22.56,4.367 23.47,3.936Q24.38,3.506 25.31,3.121Q26.24,2.736 27.188,2.397Q28.135,2.058 29.098,1.765Q30.062,1.473 31.038,1.229Q32.014,0.984 33.001,0.788Q33.988,0.591 34.984,0.444Q35.98,0.296 36.981,0.197Q37.983,0.099 38.988,0.049Q39.993,0 41,0Q42.007,0 43.012,0.049Q44.017,0.099 45.019,0.197Q46.02,0.296 47.016,0.444Q48.012,0.591 48.999,0.788Q49.986,0.984 50.962,1.229Q51.938,1.473 52.902,1.765Q53.865,2.058 54.813,2.397Q55.76,2.736 56.69,3.121Q57.62,3.506 58.53,3.936Q59.44,4.367 60.327,4.841Q61.215,5.316 62.078,5.833Q62.942,6.351 63.778,6.91Q64.615,7.469 65.424,8.068Q66.232,8.668 67.01,9.307Q67.788,9.945 68.534,10.621Q69.28,11.297 69.991,12.009Q70.703,12.72 71.379,13.466Q72.055,14.212 72.693,14.99Q73.332,15.768 73.932,16.576Q74.531,17.385 75.09,18.222Q75.649,19.059 76.167,19.922Q76.684,20.785 77.159,21.673Q77.633,22.56 78.064,23.47Q78.494,24.38 78.879,25.31Q79.264,26.24 79.603,27.188Q79.942,28.135 80.235,29.098Q80.527,30.062 80.771,31.038Q81.016,32.014 81.212,33.001Q81.409,33.988 81.556,34.984Q81.704,35.98 81.803,36.981Q81.901,37.983 81.951,38.988Q82,39.993 82,41Z"
      android:fillColor="#F3881E"/>
  <path
      android:pathData="M15.8,15.8h50.4v50.4h-50.4z"
      android:fillColor="#D8D8D8"
      android:fillAlpha="0"/>
  <group>
    <clip-path
        android:pathData="M17.24,17.24h47.52v47.52h-47.52z"/>
    <path
        android:pathData="M38.54,35.06L38.54,25.16Q38.54,23.718 37.52,22.699Q36.501,21.68 35.06,21.68L25.16,21.68Q23.718,21.68 22.699,22.699Q21.68,23.718 21.68,25.16L21.68,35.06Q21.68,36.501 22.699,37.521Q23.718,38.54 25.16,38.54L35.06,38.54Q36.501,38.54 37.52,37.521Q38.54,36.501 38.54,35.06ZM35.399,24.821Q35.54,24.961 35.54,25.16L35.54,35.06Q35.54,35.259 35.399,35.399Q35.259,35.54 35.06,35.54L25.16,35.54Q24.961,35.54 24.82,35.399Q24.68,35.259 24.68,35.06L24.68,25.16Q24.68,24.961 24.82,24.821Q24.961,24.68 25.16,24.68L35.06,24.68Q35.259,24.68 35.399,24.821Z"
        android:fillColor="#FFFFFF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M38.54,56.84L38.54,46.94Q38.54,45.499 37.52,44.479Q36.501,43.46 35.06,43.46L25.16,43.46Q23.718,43.46 22.699,44.479Q21.68,45.499 21.68,46.94L21.68,56.84Q21.68,58.282 22.699,59.301Q23.718,60.32 25.16,60.32L35.06,60.32Q36.501,60.32 37.52,59.301Q38.54,58.282 38.54,56.84ZM35.399,46.601Q35.54,46.741 35.54,46.94L35.54,56.84Q35.54,57.039 35.399,57.179Q35.259,57.32 35.06,57.32L25.16,57.32Q24.961,57.32 24.82,57.179Q24.68,57.039 24.68,56.84L24.68,46.94Q24.68,46.741 24.82,46.601Q24.961,46.46 25.16,46.46L35.06,46.46Q35.259,46.46 35.399,46.601Z"
        android:fillColor="#FFFFFF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M60.32,35.06L60.32,25.16Q60.32,23.718 59.3,22.699Q58.281,21.68 56.84,21.68L46.94,21.68Q45.498,21.68 44.479,22.699Q43.46,23.718 43.46,25.16L43.46,35.06Q43.46,36.501 44.479,37.521Q45.498,38.54 46.94,38.54L56.84,38.54Q58.281,38.54 59.3,37.521Q60.32,36.501 60.32,35.06ZM57.179,24.821Q57.32,24.961 57.32,25.16L57.32,35.06Q57.32,35.259 57.179,35.399Q57.038,35.54 56.84,35.54L46.94,35.54Q46.741,35.54 46.6,35.399Q46.46,35.259 46.46,35.06L46.46,25.16Q46.46,24.961 46.6,24.821Q46.741,24.68 46.94,24.68L56.84,24.68Q57.038,24.68 57.179,24.821Z"
        android:fillColor="#FFFFFF"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M54.35,61.351L61.351,54.351Q62.37,53.331 62.37,51.89Q62.37,50.449 61.351,49.429L54.35,42.429Q53.331,41.41 51.89,41.41Q50.448,41.41 49.429,42.429L42.429,49.429Q41.409,50.449 41.409,51.89Q41.409,53.331 42.429,54.351L49.429,61.351Q50.448,62.37 51.89,62.37Q53.331,62.37 54.35,61.351ZM59.37,51.89Q59.37,52.089 59.229,52.229L52.229,59.23Q52.089,59.37 51.89,59.37Q51.691,59.37 51.55,59.23L44.55,52.229Q44.409,52.089 44.409,51.89Q44.409,51.691 44.55,51.551L51.55,44.55Q51.691,44.41 51.89,44.41Q52.089,44.41 52.229,44.55L59.229,51.551Q59.37,51.691 59.37,51.89Z"
        android:fillColor="#FFFFFF"
        android:fillType="evenOdd"/>
  </group>
</vector>
