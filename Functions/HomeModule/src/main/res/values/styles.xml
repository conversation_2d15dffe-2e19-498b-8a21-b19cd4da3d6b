<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!--圆角对话框-->
    <style name="home_whiteRoundDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:background">@drawable/home_drawable_white_round</item>
        <item name="android:windowBackground">@drawable/home_drawable_white_round</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <!--圆角对话框-->
    <style name="home_whiteRoundSmallDialog" parent="@android:style/Theme.Dialog">
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:background">@drawable/home_drawable_white_round_smail</item>
        <item name="android:windowBackground">@drawable/home_drawable_white_round_smail</item>
        <item name="android:backgroundDimEnabled">true</item>
    </style>

    <!--tabLayout 显示文字的设置-->
    <style name="home_TabLayoutStyle">
        <item name="android:textSize">28pt</item>
        <!--这个是控制你输入的字母大小写的！-->
        <item name="android:textAllCaps">false</item>
    </style>


    <!--EditText的文字颜色-->
    <style name="home_hintTextAppearance" parent="TextAppearance.AppCompat">
        <item name="android:textSize">20pt</item>
    </style>

</resources>