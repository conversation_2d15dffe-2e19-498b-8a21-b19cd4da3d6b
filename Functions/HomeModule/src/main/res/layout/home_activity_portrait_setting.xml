<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.PortraitSettingActivity">

    <View
        android:id="@+id/statusView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/color_FFFFFF"
        app:layout_constraintBottom_toTopOf="@id/titleTv"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/titleTv"
        android:layout_width="match_parent"
        android:layout_height="90pt"
        android:gravity="center"
        android:textColor="@color/color_FFFFFF"
        android:textSize="36pt"
        app:layout_constraintTop_toBottomOf="@id/statusView"
        tools:text="Certification" />

    <View
        android:id="@+id/leftBg"
        android:layout_width="90pt"
        android:layout_height="90pt"
        app:layout_constraintBottom_toBottomOf="@id/titleTv"
        app:layout_constraintLeft_toLeftOf="@id/titleTv"
        app:layout_constraintTop_toTopOf="@id/titleTv" />

    <ImageView
        android:id="@+id/backIv"
        android:layout_width="48pt"
        android:layout_height="48pt"
        android:contentDescription="@string/base_app_name"
        app:layout_constraintBottom_toBottomOf="@id/leftBg"
        app:layout_constraintLeft_toLeftOf="@id/leftBg"
        app:layout_constraintRight_toRightOf="@id/leftBg"
        app:layout_constraintTop_toTopOf="@id/leftBg" />

    <View
        android:id="@+id/rightBg"
        android:layout_width="90pt"
        android:layout_height="90pt"
        app:layout_constraintBottom_toBottomOf="@id/titleTv"
        app:layout_constraintRight_toRightOf="@id/titleTv"
        app:layout_constraintTop_toTopOf="@id/titleTv" />

    <ImageView
        android:id="@+id/rightIv"
        android:layout_width="48pt"
        android:layout_height="48pt"
        android:background="@drawable/home_ic_black_set"
        android:contentDescription="@string/base_app_name"
        app:layout_constraintBottom_toBottomOf="@id/rightBg"
        app:layout_constraintLeft_toLeftOf="@id/rightBg"
        app:layout_constraintRight_toRightOf="@id/rightBg"
        app:layout_constraintTop_toTopOf="@id/rightBg" />


    <com.github.chrisbanes.photoview.PhotoView
        android:id="@+id/photoView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/common_ye_F3881E"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleTv" />

</androidx.constraintlayout.widget.ConstraintLayout>