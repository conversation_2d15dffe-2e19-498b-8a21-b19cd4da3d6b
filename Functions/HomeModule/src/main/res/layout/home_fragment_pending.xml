<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/pendingView"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/statusView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/color_FFFFFF"
        app:layout_constraintBottom_toTopOf="@id/title"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="90pt"
        android:background="@color/color_FFFFFF"
        android:gravity="center"
        android:text="@string/aa_title_aa_records"
        android:textColor="@color/color_131313"
        android:textSize="36pt"
        app:layout_constraintTop_toBottomOf="@id/statusView" />

    <com.flyco.tablayout.SlidingTabLayout
        android:id="@+id/stl"
        android:layout_width="match_parent"
        android:layout_height="80pt"
        android:background="@color/color_FFFFFF"
        app:layout_constraintTop_toBottomOf="@id/title"
        app:tl_divider_padding="13pt"
        app:tl_indicator_color="@color/common_ye_F3881E"
        app:tl_indicator_height="6pt"
        app:tl_indicator_width_equal_title="true"
        app:tl_tab_space_equal="true"
        app:tl_textBold="BOTH"
        app:tl_textUnselectColor="@color/color_131313"
        app:tl_textSelectColor="@color/common_ye_F3881E"
        app:tl_textsize="28pt" />

    <androidx.viewpager.widget.ViewPager
        android:id="@+id/contentVp2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/stl" />
</androidx.constraintlayout.widget.ConstraintLayout>