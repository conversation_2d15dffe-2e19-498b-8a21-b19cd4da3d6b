<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    tools:context=".activity.ContactUsActivity">

    <include
        android:id="@+id/include_title"
        layout="@layout/home_base_title" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/topIcon"
                android:layout_width="126pt"
                android:layout_height="126pt"
                android:layout_marginTop="108pt"
                android:background="@drawable/home_contact_us"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/hint"
                android:layout_width="650pt"
                android:layout_height="wrap_content"
                android:layout_marginLeft="30pt"
                android:layout_marginTop="40pt"
                android:layout_marginRight="30pt"
                android:gravity="center_horizontal"
                android:text="@string/contact_label_call_tip"
                android:textColor="@color/common_text_1d2129"
                android:textSize="28pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/topIcon" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/showPhoneTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="317pt"
                android:textColor="@color/common_text_4E5969"
                android:textSize="24pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/hint" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/callUsTv"
                android:layout_width="690pt"
                android:layout_height="80pt"
                android:layout_marginTop="22pt"
                android:background="@drawable/home_drawable_ye_btn"
                android:gravity="center"
                android:text="@string/contact_btn_call_us"
                android:textColor="@color/color_FFFFFF"
                android:textSize="32pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/showPhoneTv" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/feedBackTv"
                android:layout_width="690pt"
                android:layout_height="80pt"
                android:layout_marginTop="32pt"
                android:background="@drawable/home_drawable_ye"
                android:gravity="center"
                android:text="@string/contact_btn_feedback"
                android:textColor="@color/common_ye_F3881E"
                android:textSize="32pt"
                app:layout_constraintHorizontal_bias="0.506"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/callUsTv" />

            <RelativeLayout
                android:id="@+id/rel_line"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="269pt"
                app:layout_constraintTop_toBottomOf="@id/feedBackTv"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent">

                <ImageView
                    android:id="@+id/leftIv"
                    android:layout_width="150pt"
                    android:layout_height="1pt"
                    android:layout_centerVertical="true"
                    android:background="@drawable/left_icon" />

                <TextView
                    android:id="@+id/tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="16pt"
                    android:layout_toRightOf="@id/leftIv"
                    android:text="@string/other_social"
                    android:textColor="@color/common_text_86909C"
                    android:textSize="26pt" />

                <ImageView
                    android:id="@+id/rightIv"
                    android:layout_width="150pt"
                    android:layout_height="1pt"
                    android:layout_centerVertical="true"
                    android:layout_marginLeft="16pt"
                    android:layout_toRightOf="@id/tv"
                    android:background="@drawable/right_line" />
            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/rel_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:paddingBottom="104pt"
                android:layout_marginTop="34pt"
                app:layout_constraintTop_toBottomOf="@id/rel_line"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent">

                <ImageView
                    android:id="@+id/faceIv"
                    android:layout_width="80pt"
                    android:layout_height="80pt"
                    android:background="@drawable/face_icon" />

                <ImageView
                    android:id="@+id/twiterIv"
                    android:layout_width="80pt"
                    android:layout_height="80pt"
                    android:layout_marginLeft="80pt"
                    android:layout_toRightOf="@id/faceIv"
                    android:background="@drawable/twiter_icon" />
            </RelativeLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>

</LinearLayout>