<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.PersonalInfoActivity">

    <include
        android:id="@+id/include_title"
        layout="@layout/home_base_title" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1pt"
        android:background="@color/color_E5E5E5"
        app:layout_constraintTop_toBottomOf="@id/include_title" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/color_FFFFFF"
        app:layout_constraintTop_toBottomOf="@id/line">

        <View
            android:id="@+id/heardBg"
            android:layout_width="match_parent"
            android:layout_height="182pt"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:text="@string/personal_label_profile_photo"
            android:textColor="@color/color_9B9B9B"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/heardBg"
            app:layout_constraintLeft_toLeftOf="@id/heardBg"
            app:layout_constraintTop_toTopOf="@id/heardBg" />

        <ImageView
            android:id="@+id/moreIv"
            android:layout_width="48pt"
            android:layout_height="48pt"
            android:layout_marginRight="30pt"
            android:contentDescription="@string/common_app_name"
            android:src="@drawable/home_ic_more_arr"
            app:layout_constraintBottom_toBottomOf="@id/heardBg"
            app:layout_constraintRight_toRightOf="@id/heardBg"
            app:layout_constraintTop_toTopOf="@id/heardBg" />

        <de.hdodenhof.circleimageview.CircleImageView
            android:id="@+id/headIv"
            android:layout_width="120pt"
            android:layout_height="120pt"
            android:layout_marginRight="10pt"
            android:contentDescription="@string/common_app_name"
            app:layout_constraintBottom_toBottomOf="@id/heardBg"
            app:layout_constraintRight_toLeftOf="@id/moreIv"
            app:layout_constraintTop_toTopOf="@id/heardBg"
            tools:src="@drawable/ic_contact_head" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1pt"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:background="@color/color_E5E5E5"
            app:layout_constraintBottom_toBottomOf="@id/heardBg" />

        <View
            android:id="@+id/userNameBg"
            android:layout_width="match_parent"
            android:layout_height="107pt"
            app:layout_constraintTop_toBottomOf="@id/heardBg" />

        <TextView
            android:id="@+id/userNameHint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:text="@string/name"
            android:textColor="@color/color_9B9B9B"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/userNameBg"
            app:layout_constraintLeft_toLeftOf="@id/userNameBg"
            app:layout_constraintTop_toTopOf="@id/userNameBg" />

        <TextView
            android:id="@+id/userNameTv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:contentDescription="@string/common_app_name"
            android:gravity="right"
            android:textColor="@color/color_404040"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/userNameBg"
            app:layout_constraintLeft_toRightOf="@id/userNameHint"
            app:layout_constraintRight_toRightOf="@id/userNameBg"
            app:layout_constraintTop_toTopOf="@id/userNameBg"
            tools:text="dahaksdksadhasjkdhkjasdhkjas" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1pt"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:background="@color/color_E5E5E5"
            app:layout_constraintBottom_toBottomOf="@id/userNameBg" />

        <View
            android:visibility="gone"
            android:id="@+id/middleNameBg"
            android:layout_width="match_parent"
            android:layout_height="107pt"
            app:layout_constraintTop_toBottomOf="@id/userNameBg" />

        <TextView
            android:visibility="gone"
            android:id="@+id/middleNameHint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:text="@string/middle_name"
            android:textColor="@color/color_9B9B9B"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/middleNameBg"
            app:layout_constraintLeft_toLeftOf="@id/middleNameBg"
            app:layout_constraintTop_toTopOf="@id/middleNameBg" />

        <TextView
            android:visibility="gone"
            android:id="@+id/middleNameTv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:contentDescription="@string/common_app_name"
            android:gravity="right"
            android:textColor="@color/color_404040"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/middleNameBg"
            app:layout_constraintLeft_toRightOf="@id/middleNameHint"
            app:layout_constraintRight_toRightOf="@id/middleNameBg"
            app:layout_constraintTop_toTopOf="@id/middleNameBg"
            tools:text="dahaksdksadhasjkdhkjasdhkjas" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1pt"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:background="@color/color_E5E5E5"
            app:layout_constraintBottom_toBottomOf="@id/userNameBg" />

        <View
            android:id="@+id/mobileBg"
            android:layout_width="match_parent"
            android:layout_height="107pt"
            app:layout_constraintTop_toBottomOf="@id/middleNameBg" />

        <TextView
            android:id="@+id/mobileHint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:text="@string/personal_title_mobile"
            android:textColor="@color/color_9B9B9B"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/mobileBg"
            app:layout_constraintLeft_toLeftOf="@id/mobileBg"
            app:layout_constraintTop_toTopOf="@id/mobileBg" />

        <ImageView
            android:id="@+id/mobileMore"
            android:layout_width="48pt"
            android:layout_height="48pt"
            android:visibility="gone"
            android:layout_marginRight="30pt"
            android:contentDescription="@string/common_app_name"
            android:src="@drawable/home_ic_more_arr"
            app:layout_constraintBottom_toBottomOf="@id/mobileBg"
            app:layout_constraintRight_toRightOf="@id/mobileBg"
            app:layout_constraintTop_toTopOf="@id/mobileBg" />

        <TextView
            android:id="@+id/mobileTv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:gravity="right"
            android:textColor="@color/color_404040"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/mobileBg"
            app:layout_constraintLeft_toRightOf="@id/mobileHint"
            app:layout_constraintRight_toRightOf="@id/mobileBg"
            app:layout_constraintTop_toTopOf="@id/mobileBg"
            tools:text="dahaksdksadhasjkdhkjasdhkjas" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1pt"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:background="@color/color_E5E5E5"
            app:layout_constraintBottom_toBottomOf="@id/mobileBg" />


        <View
            android:id="@+id/nricBg"
            android:layout_width="match_parent"
            android:layout_height="107pt"
            app:layout_constraintTop_toBottomOf="@id/mobileBg" />

        <TextView
            android:id="@+id/nricHint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:text="@string/nationality"
            android:textColor="@color/color_9B9B9B"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/nricBg"
            app:layout_constraintLeft_toLeftOf="@id/nricBg"
            app:layout_constraintTop_toTopOf="@id/nricBg" />

        <TextView
            android:id="@+id/nricTv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:gravity="right"
            android:textColor="@color/color_404040"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/nricBg"
            app:layout_constraintLeft_toRightOf="@id/nricHint"
            app:layout_constraintRight_toRightOf="@id/nricBg"
            app:layout_constraintTop_toTopOf="@id/nricBg"
            tools:text="dahaksdksadhasjkdhkjasdhkjas" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1pt"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:background="@color/color_E5E5E5"
            app:layout_constraintBottom_toBottomOf="@id/nricBg" />

        <View
            android:id="@+id/expiringBg"
            android:layout_width="match_parent"
            android:layout_height="107pt"
            app:layout_constraintTop_toBottomOf="@id/nricBg" />

        <TextView
            android:id="@+id/expiringHint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:text="@string/iD_Expiring_Date"
            android:textColor="@color/color_9B9B9B"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/expiringBg"
            app:layout_constraintLeft_toLeftOf="@id/expiringBg"
            app:layout_constraintTop_toTopOf="@id/expiringBg" />

        <TextView
            android:id="@+id/expiringTv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:gravity="right"
            android:textColor="@color/color_404040"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/expiringBg"
            app:layout_constraintLeft_toRightOf="@id/expiringHint"
            app:layout_constraintRight_toRightOf="@id/expiringBg"
            app:layout_constraintTop_toTopOf="@id/expiringBg"
            tools:text="dahaksdksadhasjkdhkjasdhkjas" />

        <View
            android:layout_width="match_parent"
            android:layout_height="1pt"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:background="@color/color_E5E5E5"
            app:layout_constraintBottom_toBottomOf="@id/expiringBg" />

        <View
            android:id="@+id/emailBg"
            android:layout_width="match_parent"
            android:layout_height="107pt"
            app:layout_constraintTop_toBottomOf="@id/expiringBg" />

        <TextView
            android:id="@+id/emailHint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:text="@string/personal_title_email"
            android:textColor="@color/color_9B9B9B"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/emailBg"
            app:layout_constraintLeft_toLeftOf="@id/emailBg"
            app:layout_constraintTop_toTopOf="@id/emailBg" />

        <ImageView
            android:id="@+id/emailMore"
            android:layout_width="48pt"
            android:layout_height="48pt"
            android:layout_marginRight="30pt"
            android:contentDescription="@string/common_app_name"
            android:src="@drawable/home_ic_more_arr"
            app:layout_constraintBottom_toBottomOf="@id/emailBg"
            app:layout_constraintRight_toRightOf="@id/emailBg"
            app:layout_constraintTop_toTopOf="@id/emailBg" />

        <TextView
            android:id="@+id/emailTv"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="10pt"
            android:contentDescription="@string/common_app_name"
            android:gravity="right"
            android:singleLine="true"
            android:textColor="@color/color_404040"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/emailBg"
            app:layout_constraintLeft_toRightOf="@id/emailHint"
            app:layout_constraintRight_toLeftOf="@id/emailMore"
            app:layout_constraintTop_toTopOf="@id/emailBg"
            tools:text="dahaksdksadhasjkdhkjasdhkjas" />


        <View
            android:layout_width="match_parent"
            android:layout_height="1pt"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:background="@color/color_E5E5E5"
            app:layout_constraintBottom_toBottomOf="@id/emailBg" />

        <View
            android:id="@+id/qrCodeBg"
            android:layout_width="match_parent"
            android:layout_height="107pt"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/emailBg" />

        <TextView
            android:id="@+id/qrCodeHint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:text="@string/my_QR_Code"
            android:textColor="@color/color_9B9B9B"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="@id/qrCodeBg"
            app:layout_constraintLeft_toLeftOf="@id/qrCodeBg"
            app:layout_constraintTop_toTopOf="@id/qrCodeBg" />

        <ImageView
            android:id="@+id/qrCodeMore"
            android:layout_width="48pt"
            android:layout_height="48pt"
            android:layout_marginRight="30pt"
            android:contentDescription="@string/common_app_name"
            android:src="@drawable/home_ic_more_arr"
            app:layout_constraintBottom_toBottomOf="@id/qrCodeBg"
            app:layout_constraintRight_toRightOf="@id/qrCodeBg"
            app:layout_constraintTop_toTopOf="@id/qrCodeBg" />

        <ImageView
            android:id="@+id/qrCode_Iv"
            android:layout_width="54pt"
            android:layout_height="54pt"
            android:layout_marginLeft="30pt"
            app:layout_constraintBottom_toBottomOf="@id/qrCodeBg"
            app:layout_constraintRight_toLeftOf="@id/qrCodeMore"
            app:layout_constraintTop_toTopOf="@id/qrCodeBg" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>