<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <include
        android:id="@+id/include_title"
        layout="@layout/home_base_title" />

    <TextView
        android:id="@+id/tv_hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30pt"
        android:layout_marginTop="24pt"
        android:layout_marginRight="30pt"
        android:textColor="@color/common_text_1d2129"
        android:textSize="28pt"
        tools:text="@string/email" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="100pt"
        android:layout_marginLeft="30pt"
        android:layout_marginTop="12pt"
        android:layout_marginRight="30pt">

        <EditText
            android:id="@+id/editTie"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/drawable_white_round"
            android:inputType="text"
            android:textSize="32pt"
            android:textColor="@color/common_text_1d2129"
            android:hint="<EMAIL>"
            android:textCursorDrawable="@drawable/common_cursor"
            android:textColorHint="@color/common_text_86909C"
            android:paddingLeft="26pt"
            android:paddingRight="80pt" />

        <ImageView
            android:id="@+id/textClear_iv"
            android:layout_width="56pt"
            android:layout_height="56pt"
            android:layout_centerVertical="true"
            android:layout_alignParentRight="true"
            android:layout_marginRight="24pt"
            android:background="@drawable/ic_text_clear" />
    </RelativeLayout>

    <TextView
        android:id="@+id/loginTv"
        android:layout_width="690pt"
        android:layout_height="80pt"
        android:layout_gravity="center_horizontal"
        android:layout_marginTop="64pt"
        android:background="@drawable/common_btn_bg"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/personal_btn_save"
        android:textColor="@color/color_FFFFFF"
        android:textSize="34pt" />

</LinearLayout>