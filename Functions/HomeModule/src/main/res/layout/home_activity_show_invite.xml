<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="invitationOpt"
            type="om.rrtx.mobile.homemodule.activity.ShowInviteActivity.InvitationOpt" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".activity.ShowInviteActivity">


        <include
            android:id="@+id/include_title"
            layout="@layout/home_jetpack_base_title"
            app:titleClick="@{invitationOpt}" />

        <WebView
            android:id="@+id/showWeb"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/include_title" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
