<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="500pt"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <TextView
        android:id="@+id/titleTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30pt"
        android:textColor="@color/color_131313"
        android:textSize="32pt"
        android:textStyle="bold"
        android:text="@string/common_alert_prompt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/contentTv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="30pt"
        android:layout_marginTop="10pt"
        android:layout_marginRight="30pt"
        android:gravity="center"
        android:lineSpacingExtra="10pt"
        android:minHeight="172pt"
        android:textColor="@color/color_131313"
        android:textSize="28pt"
        app:layout_constraintTop_toBottomOf="@id/titleTv" />

    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1pt"
        android:layout_marginTop="10pt"
        android:background="@color/color_dfdfdf"
        app:layout_constraintTop_toBottomOf="@id/contentTv" />

    <TextView
        android:id="@+id/bottomTv"
        android:layout_width="match_parent"
        android:layout_height="88pt"
        android:gravity="center"
        android:textColor="@color/color_2D88FE"
        android:textSize="30pt"
        android:textStyle="bold"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/line"
        tools:text="Set Now" />
</androidx.constraintlayout.widget.ConstraintLayout>