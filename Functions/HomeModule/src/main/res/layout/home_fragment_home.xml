<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/homeView"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/statusView"
        android:layout_width="match_parent"
        android:layout_height="0pt"
        android:background="@color/common_ye_F3881E"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/titleBg_cl"
        />

   <androidx.constraintlayout.widget.ConstraintLayout
       android:id="@+id/titleBg_cl"
       android:layout_width="match_parent"
       android:layout_height="90pt"
       android:background="@color/common_ye_F3881E"
       app:layout_constraintTop_toBottomOf="@id/statusView"
       app:layout_constraintBottom_toBottomOf="@id/statusView"
       >
       <ImageView
           android:id="@+id/homeIv"
           android:layout_width="48pt"
           android:layout_height="48pt"
           android:layout_marginLeft="30pt"
           android:layout_marginTop="21pt"
           android:contentDescription="@string/base_app_name"
           android:src="@drawable/home_ic_hamburger"
           app:layout_constraintLeft_toLeftOf="parent"
           app:layout_constraintTop_toTopOf="parent"
           app:layout_constraintBottom_toBottomOf="parent"
           app:layout_goneMarginLeft="10pt" />

       <ImageView
           android:layout_width="218pt"
           android:layout_height="40pt"
           app:layout_constraintBottom_toBottomOf="@id/homeIv"
           app:layout_constraintLeft_toRightOf="@id/homeIv"
           android:layout_marginLeft="32pt"
           android:background="@drawable/one_money"
           />

       <ImageView
           android:id="@+id/newsIv"
           android:layout_width="48pt"
           android:layout_height="48pt"
           android:layout_marginTop="21pt"
           android:layout_marginRight="30pt"
           android:contentDescription="@string/base_app_name"
           android:src="@drawable/home_ic_information"
           app:layout_constraintRight_toRightOf="parent"
           app:layout_constraintTop_toTopOf="parent"
           app:layout_constraintBottom_toBottomOf="parent"
           app:layout_goneMarginLeft="10pt" />
       <com.itingchunyu.badgeview.BaseBadgeView
           android:id="@+id/bbv"
           android:layout_width="wrap_content"
           android:layout_height="wrap_content"
           android:gravity="center"
           app:badge_color="@color/color_ED273E"
           app:badge_none_show="false"
           app:layout_constraintRight_toRightOf="@id/newsIv"
           app:layout_constraintTop_toTopOf="@id/newsIv" />
   </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleBg_cl">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <View
                android:id="@+id/topBg"
                android:layout_width="match_parent"
                android:layout_height="350pt"
                android:background="@drawable/home_top_bg"
                app:layout_constraintTop_toTopOf="parent" />

            <TextView
                android:id="@+id/nameTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="30pt"
                android:layout_marginEnd="30pt"
                android:layout_marginTop="18pt"
                android:ellipsize="end"
                android:maxLines="1"
                android:singleLine="true"
                android:text="@string/home_label_hi"
                android:textColor="@color/color_FFFFFF"
                android:textSize="40pt"
                android:textStyle="bold"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintLeft_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="Hi,Please Login" />

            <TextView
                android:id="@+id/dateTv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="30pt"
                android:layout_marginTop="9pt"
                android:textColor="#FFFFFF"
                android:alpha="0.8"
                android:textSize="24pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@id/nameTv" />


                <ImageView
                    android:id="@+id/receiveIv"
                    android:layout_width="120pt"
                    android:layout_height="120pt"
                    android:layout_marginLeft="85pt"
                    android:layout_marginTop="43pt"
                    android:scaleType="fitXY"
                    android:contentDescription="@string/base_app_name"
                    android:src="@drawable/home_ic_receive"
                    app:layout_constraintHorizontal_chainStyle="spread_inside"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toLeftOf="@id/paymentIv"
                    app:layout_constraintTop_toBottomOf="@id/dateTv"
                    app:tint="@color/color_FFFFFF" />

                <TextView
                    android:id="@+id/receiveTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/home_btn_receive_payment"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="22pt"
                    app:layout_constraintLeft_toLeftOf="@id/receiveIv"
                    app:layout_constraintRight_toRightOf="@id/receiveIv"
                    app:layout_constraintTop_toBottomOf="@id/receiveIv" />

                <ImageView
                    android:id="@+id/paymentIv"
                    android:layout_width="120pt"
                    android:layout_height="120pt"
                    android:scaleType="fitXY"
                    android:contentDescription="@string/base_app_name"
                    android:src="@drawable/home_ic_payment"
                    app:layout_constraintLeft_toRightOf="@id/receiveIv"
                    app:layout_constraintRight_toLeftOf="@id/scanIv"
                    app:layout_constraintTop_toTopOf="@id/receiveIv"
                    app:tint="@color/color_FFFFFF" />

                <TextView
                    android:id="@+id/paymentTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/home_btn_make_payment"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="22pt"
                    app:layout_constraintLeft_toLeftOf="@id/paymentIv"
                    app:layout_constraintRight_toRightOf="@id/paymentIv"
                    app:layout_constraintTop_toBottomOf="@id/paymentIv"
                    />

                <ImageView
                    android:id="@+id/scanIv"
                    android:layout_width="120pt"
                    android:layout_height="120pt"
                    android:layout_marginRight="63pt"
                    android:contentDescription="@string/base_app_name"
                    android:src="@drawable/home_ic_scan"
                    app:layout_constraintLeft_toRightOf="@id/paymentIv"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@id/receiveIv"
                    app:tint="@color/color_FFFFFF" />

                <TextView
                    android:id="@+id/scanTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/home_btn_scan"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="22pt"
                    app:layout_constraintLeft_toLeftOf="@id/scanIv"
                    app:layout_constraintRight_toRightOf="@id/scanIv"
                    app:layout_constraintTop_toBottomOf="@id/scanIv" />

            <RelativeLayout
                android:id="@+id/rel"
                android:layout_marginTop="338pt"
                android:paddingTop="40pt"
                android:paddingLeft="20pt"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/home_drawable_grey_round"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/functionRv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

            </RelativeLayout>

            <com.youth.banner.Banner
                android:id="@+id/bannerIv"
                android:layout_width="690pt"
                android:layout_height="260pt"
                android:layout_marginTop="11pt"
                android:contentDescription="@string/app_name"
                app:indicator_drawable_selected="@drawable/cycle_selected"
                app:indicator_drawable_unselected="@drawable/cycle_unselect"
                app:indicator_height="12pt"
                app:indicator_width="12pt"
                app:indicator_margin="16pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/rel" />

            <View
                android:layout_width="match_parent"
                android:layout_height="30pt"
                app:layout_constraintTop_toBottomOf="@id/bannerIv" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>