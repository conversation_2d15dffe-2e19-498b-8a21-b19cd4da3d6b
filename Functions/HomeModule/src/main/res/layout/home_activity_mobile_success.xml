<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.MobileSuccessActivity">

    <include
        android:id="@+id/include_title"
        layout="@layout/home_base_title" />

    <ImageView
        android:id="@+id/icon"
        android:layout_width="120pt"
        android:layout_height="120pt"
        android:layout_marginTop="120pt"
        android:contentDescription="@string/common_app_name"
        android:src="@drawable/home_ic_success"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_title" />


    <TextView
        android:id="@+id/success"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="60pt"
        android:text="@string/personal_label_reset_mobile_tip"
        android:textColor="@color/color_00AB4D"
        android:textSize="36pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/icon" />

    <TextView
        android:id="@+id/hintTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20pt"
        android:textColor="@color/color_212121"
        android:textSize="32pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/success"
        tools:text="+602312****23123" />

    <TextView
        android:id="@+id/completeTv"
        android:layout_width="690pt"
        android:layout_height="80pt"
        android:layout_marginTop="100pt"
        android:background="@drawable/home_drawable_btn_select"
        android:gravity="center"
        android:text="@string/done"
        android:textColor="@color/color_FFFFFF"
        android:textSize="32pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/hintTv" />
</androidx.constraintlayout.widget.ConstraintLayout>