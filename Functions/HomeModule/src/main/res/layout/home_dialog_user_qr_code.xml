<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/topBg"
        android:layout_width="610pt"
        android:layout_height="wrap_content"
        android:background="@drawable/drawable_white_round"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/hint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="44pt"
            android:text="@string/account_label_myqrcode"
            android:textColor="@color/color_212121"
            android:textSize="32pt"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/qrCodeIv"
            android:layout_width="402pt"
            android:layout_height="402pt"
            android:layout_marginTop="39pt"
            android:contentDescription="@string/common_app_name"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/hint" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="39pt"
            android:layout_marginBottom="30pt"
            android:text="@string/account_label_qrcode_add_friends_tip"
            android:textColor="@color/color_999999"
            android:textSize="28pt"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/qrCodeIv" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <ImageView
        android:id="@+id/closeIv"
        android:layout_width="56pt"
        android:layout_height="56pt"
        android:layout_marginTop="40pt"
        android:contentDescription="@string/common_app_name"
        android:src="@drawable/home_ic_close_dialog"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/topBg" />
</androidx.constraintlayout.widget.ConstraintLayout>