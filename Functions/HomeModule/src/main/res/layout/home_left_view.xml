<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="466pt"
    android:layout_height="match_parent"
    android:layout_gravity="start"
    android:background="@color/common_ye_F3881E"
    android:clickable="true"
    android:focusable="true"
    android:orientation="vertical">

    <View
        android:id="@+id/languageBg"
        android:layout_width="match_parent"
        android:layout_height="100pt"
        android:layout_marginTop="150pt"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/languageIv"
        android:layout_width="48pt"
        android:layout_height="48pt"
        android:layout_marginLeft="40pt"
        android:contentDescription="@string/base_app_name"
        android:src="@drawable/home_ic_language"
        app:layout_constraintBottom_toBottomOf="@id/languageBg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/languageBg" />

    <TextView
        android:id="@+id/languageTv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20pt"
        android:layout_marginRight="30pt"
        android:ellipsize="end"
        android:text="@string/slider_btn_language"
        android:textColor="@color/color_FFFFFF"
        android:textSize="26pt"
        app:layout_constraintBottom_toBottomOf="@id/languageIv"
        app:layout_constraintLeft_toRightOf="@id/languageIv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/languageIv" />

    <View
        android:id="@+id/broadcastBg"
        android:layout_width="match_parent"
        android:layout_height="100pt"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/languageBg" />

    <ImageView
        android:id="@+id/broadcastIv"
        android:layout_width="48pt"
        android:layout_height="48pt"
        android:layout_marginLeft="40pt"
        android:visibility="gone"
        android:contentDescription="@string/base_app_name"
        android:src="@drawable/home_ic_left_broadcast"
        app:layout_constraintBottom_toBottomOf="@id/broadcastBg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/broadcastBg" />


    <TextView
        android:id="@+id/broadcastTv"
        android:layout_width="0dp"
        android:visibility="gone"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20pt"
        android:layout_marginRight="30pt"
        android:ellipsize="end"
        android:text="@string/slider_btn_broadcast"
        android:textColor="@color/color_FFFFFF"
        android:textSize="26pt"
        app:layout_constraintBottom_toBottomOf="@id/broadcastIv"
        app:layout_constraintLeft_toRightOf="@id/broadcastIv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/broadcastIv" />

    <View
        android:id="@+id/contactsBg"
        android:layout_width="match_parent"
        android:layout_height="100pt"
        app:layout_constraintTop_toBottomOf="@id/broadcastBg" />


    <ImageView
        android:id="@+id/contactsIv"
        android:layout_width="48pt"
        android:layout_height="48pt"
        android:layout_marginLeft="40pt"
        android:contentDescription="@string/base_app_name"
        android:src="@drawable/home_ic_left_contacts"
        app:layout_constraintBottom_toBottomOf="@id/contactsBg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/contactsBg" />

    <TextView
        android:id="@+id/contactsTv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20pt"
        android:layout_marginRight="30pt"
        android:ellipsize="end"
        android:text="@string/my_contacts"
        android:textColor="@color/color_FFFFFF"
        android:textSize="26pt"
        app:layout_constraintBottom_toBottomOf="@id/contactsIv"
        app:layout_constraintLeft_toRightOf="@id/contactsIv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/contactsIv" />

    <View
        android:id="@+id/contactBg"
        android:layout_width="match_parent"
        android:layout_height="100pt"
        app:layout_constraintTop_toBottomOf="@id/contactsBg" />

    <ImageView
        android:id="@+id/contactIv"
        android:layout_width="48pt"
        android:layout_height="48pt"
        android:layout_marginLeft="40pt"
        android:contentDescription="@string/base_app_name"
        android:src="@drawable/home_ic_left_feedback"
        app:layout_constraintBottom_toBottomOf="@id/contactBg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/contactBg" />


    <TextView
        android:id="@+id/contactTv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20pt"
        android:layout_marginRight="30pt"
        android:ellipsize="end"
        android:text="@string/slider_btn_contact_us"
        android:textColor="@color/color_FFFFFF"
        android:textSize="26pt"
        app:layout_constraintBottom_toBottomOf="@id/contactIv"
        app:layout_constraintLeft_toRightOf="@id/contactIv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/contactIv" />

    <View
        android:id="@+id/aboutBg"
        android:layout_width="match_parent"
        android:layout_height="100pt"
        app:layout_constraintTop_toBottomOf="@id/contactBg" />

    <ImageView
        android:id="@+id/aboutIv"
        android:layout_width="48pt"
        android:layout_height="48pt"
        android:layout_marginLeft="40pt"
        android:contentDescription="@string/base_app_name"
        android:src="@drawable/home_ic_left_about"
        app:layout_constraintBottom_toBottomOf="@id/aboutBg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/aboutBg" />


    <TextView
        android:id="@+id/aboutTv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20pt"
        android:layout_marginRight="30pt"
        android:ellipsize="end"
        android:text="@string/slider_btn_about"
        android:textColor="@color/color_FFFFFF"
        android:textSize="26pt"
        app:layout_constraintBottom_toBottomOf="@id/aboutIv"
        app:layout_constraintLeft_toRightOf="@id/aboutIv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/aboutIv" />

    <View
        android:id="@+id/upDateBg"
        android:layout_width="match_parent"
        android:layout_height="100pt"
        app:layout_constraintTop_toBottomOf="@id/aboutBg" />

    <ImageView
        android:id="@+id/upDateIv"
        android:layout_width="48pt"
        android:layout_height="48pt"
        android:layout_marginLeft="40pt"
        android:contentDescription="@string/base_app_name"
        android:src="@drawable/home_ic_left_update"
        app:layout_constraintBottom_toBottomOf="@id/upDateBg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/upDateBg" />


    <TextView
        android:id="@+id/upDateTv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20pt"
        android:layout_marginRight="30pt"
        android:ellipsize="end"
        android:text="@string/slider_btn_update"
        android:textColor="@color/color_FFFFFF"
        android:textSize="26pt"
        app:layout_constraintBottom_toBottomOf="@id/upDateIv"
        app:layout_constraintLeft_toRightOf="@id/upDateIv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/upDateIv" />


    <View
        android:id="@+id/logoutBg"
        android:layout_width="match_parent"
        android:layout_height="100pt"
        android:layout_marginBottom="50pt"
        app:layout_constraintBottom_toBottomOf="parent"
        tools:layout_editor_absoluteX="0dp" />


    <ImageView
        android:id="@+id/logoutIv"
        android:layout_width="48pt"
        android:layout_height="48pt"
        android:layout_marginLeft="40pt"
        android:contentDescription="@string/base_app_name"
        android:src="@drawable/home_ic_left_log_out"
        app:layout_constraintBottom_toBottomOf="@id/logoutBg"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="@id/logoutBg" />

    <TextView
        android:id="@+id/logoutTv"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginLeft="20pt"
        android:layout_marginRight="30pt"
        android:ellipsize="end"
        android:text="@string/slider_btn_logout"
        android:textColor="@color/color_FFFFFF"
        android:textSize="32pt"
        app:layout_constraintBottom_toBottomOf="@id/logoutIv"
        app:layout_constraintLeft_toRightOf="@id/logoutIv"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/logoutIv" />

</androidx.constraintlayout.widget.ConstraintLayout>