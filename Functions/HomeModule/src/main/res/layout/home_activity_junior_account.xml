<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">


        <include
            android:id="@+id/include_title"
            layout="@layout/home_base_title" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="0pt"
            android:layout_weight="1"
            android:background="@color/common_bg_f7f8fa"
            android:paddingLeft="30pt"
            android:paddingRight="30pt"
            >

            <ImageView
                android:id="@+id/infoBg_view"
                android:layout_width="match_parent"
                android:layout_height="280pt"
                android:layout_marginTop="24pt"
                android:background="@drawable/bg_junior_info"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/icon_iv"
                android:layout_width="100pt"
                android:layout_height="100pt"
                android:layout_marginLeft="24pt"
                android:background="@drawable/ic_junior"
                android:layout_marginTop="50pt"
                app:layout_constraintLeft_toLeftOf="@id/infoBg_view"
                app:layout_constraintTop_toTopOf="@id/infoBg_view" />

            <TextView
                android:id="@+id/name_tv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginLeft="148pt"
                android:layout_marginRight="24pt"
                android:fontFamily="sans-serif-condensed-medium"
                android:textColor="@color/color_FFFFFF"
                android:textSize="32pt"
                android:lines="1"
                android:ellipsize="end"
                app:layout_constraintLeft_toRightOf="@id/icon_iv"
                app:layout_constraintTop_toTopOf="@id/icon_iv"
                />

            <TextView
                android:id="@+id/mobile_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textColor="@color/color_FFFFFF"
                android:textSize="28pt"
                android:alpha="0.8"
                app:layout_constraintBottom_toBottomOf="@id/icon_iv"
                app:layout_constraintLeft_toLeftOf="@id/name_tv"
                app:layout_goneMarginBottom="8pt" />

            <View
                android:id="@+id/line"
                android:layout_width="0pt"
                android:layout_height="1pt"
                android:layout_marginTop="50pt"
                android:background="@color/color_FFFFFF"
                android:alpha="0.2"
                app:layout_constraintLeft_toLeftOf="@id/infoBg_view"
                app:layout_constraintRight_toRightOf="@id/infoBg_view"
                app:layout_constraintTop_toBottomOf="@id/icon_iv" />

            <ImageView
                android:id="@+id/dot_iv"
                android:layout_width="10pt"
                android:layout_height="10pt"
                android:layout_marginLeft="40pt"
                android:src="@drawable/ic_dot"
                app:layout_constraintBottom_toBottomOf="@id/infoBg_view"
                app:layout_constraintLeft_toLeftOf="@id/infoBg_view"
                app:layout_constraintTop_toBottomOf="@id/line"
                app:tint="@color/color_FFFFFF" />

            <TextView
                android:id="@+id/status_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10pt"
                android:textColor="@color/color_FFFFFF"
                android:textSize="22pt"
                app:layout_constraintBottom_toBottomOf="@id/infoBg_view"
                app:layout_constraintLeft_toRightOf="@id/dot_iv"
                app:layout_constraintTop_toBottomOf="@id/line" />


            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/function_rv"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintTop_toBottomOf="@id/infoBg_view" />

            <TextView
                android:id="@+id/cancel_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="100pt"
                android:text="@string/cancel_Junior_Account"
                android:textColor="@color/common_ye_F3881E"
                android:textSize="30pt"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent" />
            <View
                app:layout_constraintTop_toBottomOf="@id/cancel_tv"
                app:layout_constraintLeft_toLeftOf="@id/cancel_tv"
                app:layout_constraintRight_toRightOf="@id/cancel_tv"
                android:background="@color/common_ye_F3881E"
                android:layout_width="320pt"
                android:layout_height="2pt"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

    </LinearLayout>
</layout>