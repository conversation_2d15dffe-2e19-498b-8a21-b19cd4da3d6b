<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/common_bg_f7f8fa">

        <include
            android:id="@+id/include_title"
            layout="@layout/home_base_title"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/iv_adv"
            android:layout_width="match_parent"
            android:layout_height="300pt"
            android:background="@drawable/bg_junior_management"
            app:layout_constraintTop_toBottomOf="@id/include_title" />


        <TextView
            android:id="@+id/s1_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="30pt"
            android:layout_marginTop="32pt"
            android:text="@string/linked_Junior_Accounts"
            android:textColor="@color/common_text_1d2129"
            android:textSize="28pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_adv" />

        <om.rrtx.mobile.rrtxcommon1.widget.EmptyRecycleView
            android:id="@+id/rv_account"
            android:layout_width="match_parent"
            android:layout_height="0pt"
            android:layout_marginBottom="20pt"
            app:layout_constraintBottom_toTopOf="@id/next_tv"
            app:layout_constraintTop_toBottomOf="@id/s1_tv" />

        <TextView
            android:id="@+id/hint_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/unlinked_Junior_Accounts"
            android:textColor="@color/common_text_1d2129"
            android:textSize="32pt"
            android:visibility="gone"
            app:layout_constraintBottom_toTopOf="@id/next_tv"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_adv" />


        <TextView
            android:id="@+id/next_tv"
            android:layout_width="match_parent"
            android:layout_height="80pt"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:layout_marginBottom="60pt"
            android:background="@drawable/common_usable_btn"
            android:gravity="center"
            android:text="@string/register_Junior_Account"
            android:textColor="@color/color_FFFFFF"
            android:textSize="32pt"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>