<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="om.rrtx.mobile.homemodule.R" />
    </data>

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginLeft="12pt"
        app:cardMaxElevation="0pt"
        app:cardElevation="0pt"
        android:layout_marginRight="12pt"
        app:cardCornerRadius="16pt">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:background="@android:color/transparent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/topBg"
                android:layout_width="match_parent"
                android:layout_height="255pt"
                android:background="@drawable/home_ic_account_bg"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/curTitleTv"
                    android:layout_width="240pt"
                    android:layout_height="52pt"
                    android:background="@drawable/bg_account_name"
                    android:fontFamily="sans-serif-condensed-medium"
                    android:gravity="center"
                    android:textColor="@color/common_ye_F3881E"
                    android:textSize="28pt"
                    app:layout_constraintLeft_toLeftOf="@id/topBg"
                    app:layout_constraintTop_toTopOf="@id/topBg"
                    tools:text="USD Account Asset" />

                <LinearLayout
                    android:id="@+id/lin"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="25pt"
                    android:layout_marginTop="50pt"
                    android:layout_marginRight="25pt"
                    android:layout_marginBottom="36pt"
                    android:weightSum="3"
                    app:layout_constraintTop_toBottomOf="@id/curTitleTv">

                    <LinearLayout
                        android:id="@+id/topUpLl"
                        android:layout_width="0pt"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="52pt"
                            android:layout_height="52pt"
                            android:src="@drawable/home_ic_balance_top" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:text="@string/bank_to_OneMoney"
                            android:textColor="@color/color_212121"
                            android:textSize="26pt" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/withLl"
                        android:layout_width="0pt"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="52pt"
                            android:layout_height="52pt"
                            android:src="@drawable/home_ic_balance_with" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/home_oneMoney_to_Bank"
                            android:textColor="@color/color_212121"
                            android:textSize="26pt" />
                    </LinearLayout>

                    <LinearLayout
                        android:id="@+id/recordLl"
                        android:layout_width="0pt"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:gravity="center"
                        android:orientation="vertical">

                        <ImageView
                            android:layout_width="52pt"
                            android:layout_height="52pt"
                            android:src="@drawable/ic_send_money"
                            app:tint="@color/common_text_1d2129" />

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/send_money"
                            android:textColor="@color/color_212121"
                            android:textSize="26pt" />
                    </LinearLayout>
                </LinearLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/view_junior"
                    android:visibility="gone"
                    android:layout_width="match_parent"
                    android:layout_height="120pt"
                    android:layout_marginTop="60pt"
                    android:paddingLeft="32pt"
                    android:paddingRight="32pt"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:id="@+id/tv_Enquiry1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="20pt"
                        android:text="@string/balance_Enquiry"
                        android:textColor="@color/common_text_1d2129"
                        android:textSize="28pt"
                        android:fontFamily="sans-serif-condensed-medium"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <TextView
                        android:id="@+id/tv_lastQueryTime1"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="9pt"
                        android:textColor="@color/common_text_4E5969"
                        android:textSize="20pt"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tv_Enquiry1" />

                    <LinearLayout
                        android:id="@+id/bg_getBalance1"
                        cornerBackgroundColor="@{R.color.common_ye_F3881E}"
                        cornerBackgroundRadius="@{47}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:gravity="center"
                        android:orientation="vertical"
                        android:paddingTop="12pt"
                        android:paddingBottom="12pt"
                        android:paddingLeft="36pt"
                        android:paddingRight="36pt"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/get_Balance"
                            android:fontFamily="sans-serif-condensed-medium"
                            android:textColor="@color/common_text_FFFFFF"
                            android:textSize="20pt" />

                        <TextView
                            android:visibility="gone"
                            android:id="@+id/tv_fees1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:textColor="@color/common_text_FFFFFF"
                            android:textSize="14pt" />

                    </LinearLayout>


                </androidx.constraintlayout.widget.ConstraintLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <RelativeLayout
                android:id="@+id/consview"
                android:layout_width="match_parent"
                android:layout_height="120pt"
                android:layout_marginTop="250pt"
                android:background="@color/color_fceedb"
                android:paddingLeft="32pt"
                android:paddingRight="32pt"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tv_Enquiry"
                    android:gravity="left"
                    android:layout_alignParentLeft="true"
                    android:layout_toLeftOf="@id/bg_getBalance"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20pt"
                    android:text="@string/balance_Enquiry"
                    android:textColor="@color/common_text_1d2129"
                    android:textSize="28pt"
                    android:fontFamily="sans-serif-condensed-medium" />

                <TextView
                    android:id="@+id/tv_lastQueryTime"
                    android:gravity="left"
                    android:layout_alignParentLeft="true"
                    android:layout_toLeftOf="@id/bg_getBalance"
                    android:layout_below="@id/tv_Enquiry"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="9pt"
                    android:textColor="@color/common_text_4E5969"
                    android:textSize="20pt" />

                <LinearLayout
                    android:id="@+id/bg_getBalance"
                    cornerBackgroundColor="@{R.color.common_ye_F3881E}"
                    cornerBackgroundRadius="@{47}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="vertical"
                    android:paddingTop="12pt"
                    android:paddingBottom="12pt"
                    android:paddingLeft="36pt"
                    android:paddingRight="36pt"
                    android:layout_alignParentRight="true"
                    android:layout_centerVertical="true">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/get_Balance"
                        android:fontFamily="sans-serif-condensed-medium"
                        android:textColor="@color/common_text_FFFFFF"
                        android:textSize="20pt" />

                    <TextView
                        android:visibility="gone"
                        android:id="@+id/tv_fees"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="@color/common_text_FFFFFF"
                        android:textSize="14pt" />

                </LinearLayout>

            </RelativeLayout>

            <LinearLayout
                android:id="@+id/activateNowLl"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/home_ic_account_bg"
                android:gravity="center"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/accountTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/color_212121"
                    android:textSize="32pt"
                    tools:text="AOA Account" />

                <TextView
                    android:visibility="gone"
                    android:id="@+id/activateNowTv"
                    android:layout_width="220pt"
                    android:layout_height="64pt"
                    android:layout_marginTop="20pt"
                    android:background="@drawable/home_drawable_black_border_bg"
                    android:gravity="center"
                    android:text="@string/home_label_activate_now"
                    android:textColor="@color/color_212121"
                    android:textSize="26pt" />
            </LinearLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>

</layout>