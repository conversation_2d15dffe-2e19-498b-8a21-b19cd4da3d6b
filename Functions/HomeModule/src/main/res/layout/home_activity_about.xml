<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_F5F7F8"
    tools:context=".activity.AboutActivity">

    <include
        android:id="@+id/include_title"
        layout="@layout/home_base_title" />

    <ImageView
        android:id="@+id/icon"
        android:layout_width="260pt"
        android:layout_height="186pt"
        android:layout_marginTop="84pt"
        android:contentDescription="@string/common_app_name"
        android:src="@drawable/home_icon"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_title" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="11pt"
        android:text="@string/common_app_name"
        android:textColor="@color/common_ye_F3881E"
        android:textSize="40pt"
        android:textStyle="bold"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/icon" />

    <TextView
        android:id="@+id/versionTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8pt"
        android:textColor="@color/common_text_86909C"
        android:textSize="26pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/title"
        tools:text="v1.0.2" />

    <androidx.cardview.widget.CardView
        android:id="@+id/termsCv"
        android:layout_width="690pt"
        android:layout_height="98pt"
        android:layout_marginTop="89pt"
        android:background="@color/color_FFFFFF"
        app:cardCornerRadius="10pt"
        app:cardElevation="0pt"
        app:cardMaxElevation="0pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/versionTv">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20pt"
            android:text="@string/about_btn_term_conditions"
            android:textColor="@color/common_text_1d2129"
            android:textSize="28pt" />

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="48pt"
            android:layout_height="48pt"
            android:layout_gravity="right|center_vertical"
            android:layout_marginRight="20pt"
            android:src="@drawable/home_icon_jiantou" />

    </androidx.cardview.widget.CardView>

    <androidx.cardview.widget.CardView
        android:id="@+id/policyCv"
        android:layout_width="690pt"
        android:layout_height="98pt"
        android:layout_marginTop="20pt"
        android:background="@color/color_FFFFFF"
        app:cardCornerRadius="10pt"
        app:cardElevation="0pt"
        app:cardMaxElevation="0pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/termsCv">

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginLeft="20pt"
            android:text="@string/about_btn_privacy_policy"
            android:textColor="@color/common_text_1d2129"
            android:textSize="28pt" />

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="48pt"
            android:layout_height="48pt"
            android:layout_gravity="right|center_vertical"
            android:layout_marginRight="20pt"
            android:src="@drawable/home_icon_jiantou" />
    </androidx.cardview.widget.CardView>

    <TextView
        android:id="@+id/hint"
        android:visibility="gone"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginBottom="40pt"
        android:text="@string/about_label_copyright_tip"
        android:textColor="@color/color_999999"
        android:textSize="24pt"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <androidx.appcompat.widget.AppCompatImageView
        android:visibility="gone"
        android:layout_width="190pt"
        android:layout_height="32pt"
        android:layout_marginBottom="10pt"
        android:src="@drawable/home_bamboo_logo"
        app:layout_constraintBottom_toTopOf="@id/hint"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>