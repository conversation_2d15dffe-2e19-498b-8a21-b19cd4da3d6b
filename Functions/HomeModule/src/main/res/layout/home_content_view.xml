<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_F5F7F8">

    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/contentVp2"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@id/bnv"
        app:layout_constraintTop_toTopOf="parent" />

    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bnv"
        android:layout_width="match_parent"
        android:layout_height="130pt"
        android:layout_alignParentBottom="true"
        android:background="@color/color_FFFFFF"
        app:elevation="3pt"
        app:itemIconSize="48pt"
        app:itemBackground="@null"
        app:itemIconTint="@color/home_color_tab"
        app:itemTextAppearanceActive="@style/home_hintTextAppearance"
        app:itemTextAppearanceInactive="@style/home_hintTextAppearance"
        app:itemTextColor="@color/home_color_tab"
        app:layout_constraintBottom_toBottomOf="parent"
        app:menu="@menu/home_menu_bottom_navigation_view" />
</androidx.constraintlayout.widget.ConstraintLayout>