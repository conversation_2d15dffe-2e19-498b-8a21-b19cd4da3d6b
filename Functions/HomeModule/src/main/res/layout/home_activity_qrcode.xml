<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="om.rrtx.mobile.homemodule.R" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:background="@color/common_bg_f7f8fa"
        android:layout_height="match_parent">

        <include
            android:id="@+id/include_title"
            layout="@layout/home_base_title" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/qrCode_bg"
            android:layout_width="610pt"
            android:layout_height="700pt"
            android:layout_marginTop="100pt"
            android:background="@drawable/bg_qrcode"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/include_title">

            <de.hdodenhof.circleimageview.CircleImageView
                android:id="@+id/head_iv"
                android:layout_width="110pt"
                android:layout_height="110pt"
                android:layout_marginTop="70pt"
                app:layout_constraintLeft_toLeftOf="@id/qrCode_iv"
                app:layout_constraintTop_toTopOf="parent" />

            <LinearLayout
                android:layout_marginLeft="223pt"
                android:layout_marginRight="113pt"
                app:layout_constraintTop_toTopOf="@id/head_iv"
                app:layout_constraintBottom_toBottomOf="@id/head_iv"
                android:orientation="vertical"
                android:gravity="center_vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">
                <TextView
                    android:id="@+id/name_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="sans-serif-condensed-medium"
                    android:textColor="@color/common_text_1d2129"
                    android:textSize="30pt" />

                <TextView
                    android:id="@+id/mobile_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="12pt"
                    android:textSize="24pt"
                    android:textColor="@color/common_text_86909C"/>
            </LinearLayout>

            <ImageView
                android:id="@+id/qrCode_iv"
                android:layout_width="402pt"
                android:layout_height="402pt"
                android:layout_marginTop="32pt"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@id/head_iv" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/save_qrCode_bg"
            android:layout_width="750pt"
            android:layout_height="988pt"
            android:layout_marginTop="100pt"
            android:background="@drawable/bg_qrcode_save"
            android:visibility="invisible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/include_title">

            <ImageView
                android:id="@+id/appName_iv"
                android:layout_width="match_parent"
                android:layout_height="142pt"
                android:background="@drawable/bg_app_name"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="0pt"
                android:background="@drawable/bg_qrcode_bottom"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@id/appName_iv">

                <View
                    android:id="@+id/bg_view"
                    cornerBackgroundRadius="@{30}"
                    android:layout_width="530pt"
                    android:layout_height="540pt"
                    android:layout_marginTop="86.88pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <ImageView
                    android:id="@+id/save_QrCode_Iv"
                    android:layout_width="434pt"
                    android:layout_height="434pt"
                    app:layout_constraintBottom_toBottomOf="@id/bg_view"
                    app:layout_constraintLeft_toLeftOf="@id/bg_view"
                    app:layout_constraintRight_toRightOf="@id/bg_view"
                    app:layout_constraintTop_toTopOf="@id/bg_view" />

                <TextView
                    android:id="@+id/save_name_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="51pt"
                    android:fontFamily="sans-serif-condensed-medium"
                    android:textColor="@color/common_text_FFFFFF"
                    android:textSize="42pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/bg_view" />

                <TextView
                    android:id="@+id/save_mobile_tv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16pt"
                    android:textColor="@color/common_text_FFFFFF"
                    android:textSize="30pt"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/save_name_tv" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <TextView
            android:id="@+id/close_tv"
            android:layout_width="0pt"
            android:layout_height="88pt"
            android:layout_marginLeft="30pt"
            android:layout_marginBottom="104pt"
            android:background="@drawable/common_btn_stroke"
            android:gravity="center"
            android:text="@string/close"
            android:textColor="@color/common_ye_F3881E"
            android:textSize="32pt"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/save_tv"
            app:layout_goneMarginBottom="104pt" />

        <TextView
            android:id="@+id/save_tv"
            android:layout_width="0pt"
            android:layout_height="88pt"
            android:layout_marginLeft="30pt"
            android:layout_marginRight="30pt"
            android:background="@drawable/common_usable_btn"
            android:gravity="center"
            android:text="@string/save_as_Image"
            android:textColor="@color/common_text_FFFFFF"
            android:textSize="32pt"
            app:layout_constraintLeft_toRightOf="@id/close_tv"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/close_tv" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>