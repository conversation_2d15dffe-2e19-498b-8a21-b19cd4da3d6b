<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <include
            android:id="@+id/include_title"
            layout="@layout/home_base_title" />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:fillViewport="true"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/include_title">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/color_F5F7F8">

                <TextView
                    android:id="@+id/title1"
                    android:layout_width="match_parent"
                    android:layout_height="82pt"
                    android:background="@color/color_F5F7F8"
                    android:gravity="center_vertical"
                    android:paddingLeft="30pt"
                    android:text="@string/personal_title1"
                    android:textColor="@color/common_text_1d2129"
                    android:textSize="28pt"
                    app:layout_constraintTop_toTopOf="parent" />

                <View
                    android:id="@+id/heardBg"
                    android:layout_width="match_parent"
                    android:layout_height="182pt"
                    android:background="@color/color_FFFFFF"
                    app:layout_constraintTop_toBottomOf="@id/title1" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:text="Portrait"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/heardBg"
                    app:layout_constraintLeft_toLeftOf="@id/heardBg"
                    app:layout_constraintTop_toTopOf="@id/heardBg" />

                <ImageView
                    android:id="@+id/moreIv"
                    android:layout_width="48pt"
                    android:layout_height="48pt"
                    android:layout_marginRight="30pt"
                    android:contentDescription="@string/common_app_name"
                    android:src="@drawable/home_ic_more_arr"
                    app:layout_constraintBottom_toBottomOf="@id/heardBg"
                    app:layout_constraintRight_toRightOf="@id/heardBg"
                    app:layout_constraintTop_toTopOf="@id/heardBg" />

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/headIv"
                    android:layout_width="120pt"
                    android:layout_height="120pt"
                    android:layout_marginRight="10pt"
                    android:contentDescription="@string/common_app_name"
                    app:layout_constraintBottom_toBottomOf="@id/heardBg"
                    app:layout_constraintRight_toLeftOf="@id/moreIv"
                    app:layout_constraintTop_toTopOf="@id/heardBg"
                    tools:src="@drawable/ic_contact_head" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1pt"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:background="@color/color_E5E5E5"
                    app:layout_constraintBottom_toBottomOf="@id/heardBg" />

                <View
                    android:id="@+id/userNameBg"
                    android:layout_width="match_parent"
                    android:layout_height="107pt"
                    android:background="@color/color_FFFFFF"
                    app:layout_constraintTop_toBottomOf="@id/heardBg" />

                <TextView
                    android:id="@+id/userNameHint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:text="@string/name"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/userNameBg"
                    app:layout_constraintLeft_toLeftOf="@id/userNameBg"
                    app:layout_constraintTop_toTopOf="@id/userNameBg" />

                <TextView
                    android:id="@+id/userNameTv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:contentDescription="@string/common_app_name"
                    android:gravity="right"
                    android:textColor="@color/color_404040"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/userNameBg"
                    app:layout_constraintLeft_toRightOf="@id/userNameHint"
                    app:layout_constraintRight_toRightOf="@id/userNameBg"
                    app:layout_constraintTop_toTopOf="@id/userNameBg"
                    tools:text="dahaksdksadhasjkdhkjasdhkjas" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1pt"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:background="@color/color_E5E5E5"
                    app:layout_constraintBottom_toBottomOf="@id/userNameBg" />

                <View
                    android:visibility="gone"
                    android:id="@+id/middleNameBg"
                    android:layout_width="match_parent"
                    android:layout_height="107pt"
                    android:background="@color/color_FFFFFF"
                    app:layout_constraintTop_toBottomOf="@id/userNameBg" />

                <TextView
                    android:visibility="gone"
                    android:id="@+id/middleNameHint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:text="@string/middle_name"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/middleNameBg"
                    app:layout_constraintLeft_toLeftOf="@id/middleNameBg"
                    app:layout_constraintTop_toTopOf="@id/middleNameBg" />

                <TextView
                    android:visibility="gone"
                    android:id="@+id/middleNameTv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:contentDescription="@string/common_app_name"
                    android:gravity="right"
                    android:textColor="@color/color_404040"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/middleNameBg"
                    app:layout_constraintLeft_toRightOf="@id/middleNameHint"
                    app:layout_constraintRight_toRightOf="@id/middleNameBg"
                    app:layout_constraintTop_toTopOf="@id/middleNameBg"
                    tools:text="dahaksdksadhasjkdhkjasdhkjas" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1pt"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:background="@color/color_E5E5E5"
                    app:layout_constraintBottom_toBottomOf="@id/userNameBg" />

                <View
                    android:id="@+id/mobileBg"
                    android:layout_width="match_parent"
                    android:layout_height="107pt"
                    android:background="@color/color_FFFFFF"
                    app:layout_constraintTop_toBottomOf="@id/middleNameBg" />

                <TextView
                    android:id="@+id/mobileHint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:text="@string/personal_title_mobile"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/mobileBg"
                    app:layout_constraintLeft_toLeftOf="@id/mobileBg"
                    app:layout_constraintTop_toTopOf="@id/mobileBg" />

                <ImageView
                    android:id="@+id/mobileMore"
                    android:layout_width="48pt"
                    android:layout_height="48pt"
                    android:layout_marginRight="30pt"
                    android:contentDescription="@string/common_app_name"
                    android:src="@drawable/home_ic_more_arr"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/mobileBg"
                    app:layout_constraintRight_toRightOf="@id/mobileBg"
                    app:layout_constraintTop_toTopOf="@id/mobileBg" />

                <TextView
                    android:id="@+id/mobileTv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:gravity="right"
                    android:textColor="@color/color_404040"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/mobileBg"
                    app:layout_constraintLeft_toRightOf="@id/mobileHint"
                    app:layout_constraintRight_toLeftOf="@id/mobileMore"
                    app:layout_constraintTop_toTopOf="@id/mobileBg"
                    tools:text="dahaksdksadhasjkdhkjasdhkjas" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1pt"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:background="@color/color_E5E5E5"
                    app:layout_constraintBottom_toBottomOf="@id/mobileBg" />


                <View
                    android:id="@+id/nricBg"
                    android:layout_width="match_parent"
                    android:layout_height="107pt"
                    android:background="@color/color_FFFFFF"
                    app:layout_constraintTop_toBottomOf="@id/mobileBg" />

                <TextView
                    android:id="@+id/nricHint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:text="@string/nationality"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/nricBg"
                    app:layout_constraintLeft_toLeftOf="@id/nricBg"
                    app:layout_constraintTop_toTopOf="@id/nricBg" />

                <TextView
                    android:id="@+id/nricTv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:gravity="right"
                    android:textColor="@color/color_404040"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/nricBg"
                    app:layout_constraintLeft_toRightOf="@id/nricHint"
                    app:layout_constraintRight_toRightOf="@id/nricBg"
                    app:layout_constraintTop_toTopOf="@id/nricBg"
                    tools:text="dahaksdksadhasjkdhkjasdhkjas" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1pt"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:background="@color/color_E5E5E5"
                    app:layout_constraintBottom_toBottomOf="@id/nricBg" />

                <View
                    android:id="@+id/expiringBg"
                    android:layout_width="match_parent"
                    android:layout_height="107pt"
                    android:background="@color/color_FFFFFF"
                    app:layout_constraintTop_toBottomOf="@id/nricBg" />

                <TextView
                    android:id="@+id/expiringHint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:text="@string/iD_Expiring_Date"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/expiringBg"
                    app:layout_constraintLeft_toLeftOf="@id/expiringBg"
                    app:layout_constraintTop_toTopOf="@id/expiringBg" />

                <TextView
                    android:id="@+id/expiringTv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:gravity="right"
                    android:textColor="@color/color_404040"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/expiringBg"
                    app:layout_constraintLeft_toRightOf="@id/expiringHint"
                    app:layout_constraintRight_toRightOf="@id/expiringBg"
                    app:layout_constraintTop_toTopOf="@id/expiringBg"
                    tools:text="dahaksdksadhasjkdhkjasdhkjas" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1pt"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:background="@color/color_E5E5E5"
                    app:layout_constraintBottom_toBottomOf="@id/expiringBg" />

                <View
                    android:id="@+id/emailBg"
                    android:layout_width="match_parent"
                    android:layout_height="107pt"
                    android:visibility="gone"
                    android:background="@color/color_FFFFFF"
                    app:layout_constraintTop_toBottomOf="@id/expiringBg" />

                <TextView
                    android:id="@+id/emailHint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:text="@string/personal_title_email"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="28pt"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/emailBg"
                    app:layout_constraintLeft_toLeftOf="@id/emailBg"
                    app:layout_constraintTop_toTopOf="@id/emailBg" />

                <ImageView
                    android:id="@+id/emailMore"
                    android:layout_width="48pt"
                    android:layout_height="48pt"
                    android:layout_marginRight="30pt"
                    android:contentDescription="@string/common_app_name"
                    android:src="@drawable/home_ic_more_arr"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/emailBg"
                    app:layout_constraintRight_toRightOf="@id/emailBg"
                    app:layout_constraintTop_toTopOf="@id/emailBg" />

                <TextView
                    android:id="@+id/emailTv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="10pt"
                    android:contentDescription="@string/common_app_name"
                    android:gravity="right"
                    android:singleLine="true"
                    android:textColor="@color/color_404040"
                    android:textSize="28pt"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/emailBg"
                    app:layout_constraintLeft_toRightOf="@id/emailHint"
                    app:layout_constraintRight_toLeftOf="@id/emailMore"
                    app:layout_constraintTop_toTopOf="@id/emailBg"
                    tools:text="dahaksdksadhasjkdhkjasdhkjas" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1pt"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:background="@color/color_E5E5E5"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="@id/emailBg" />

                <View
                    android:id="@+id/qrCodeBg"
                    android:layout_width="match_parent"
                    android:layout_height="107pt"
                    android:background="@color/color_FFFFFF"
                    app:layout_constraintTop_toBottomOf="@id/emailBg" />

                <TextView
                    android:id="@+id/qrCodeHint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:text="@string/my_QR_Code"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/qrCodeBg"
                    app:layout_constraintLeft_toLeftOf="@id/qrCodeBg"
                    app:layout_constraintTop_toTopOf="@id/qrCodeBg" />

                <ImageView
                    android:id="@+id/qrCodeMore"
                    android:layout_width="48pt"
                    android:layout_height="48pt"
                    android:layout_marginRight="30pt"
                    android:contentDescription="@string/common_app_name"
                    android:src="@drawable/home_ic_more_arr"
                    app:layout_constraintBottom_toBottomOf="@id/qrCodeBg"
                    app:layout_constraintRight_toRightOf="@id/qrCodeBg"
                    app:layout_constraintTop_toTopOf="@id/qrCodeBg" />

                <ImageView
                    android:id="@+id/qrCode_Iv"
                    android:layout_width="54pt"
                    android:layout_height="54pt"
                    android:layout_marginLeft="30pt"
                    app:layout_constraintBottom_toBottomOf="@id/qrCodeBg"
                    app:layout_constraintRight_toLeftOf="@id/qrCodeMore"
                    app:layout_constraintTop_toTopOf="@id/qrCodeBg" />

                <TextView
                    android:id="@+id/title2"
                    android:layout_width="match_parent"
                    android:layout_height="82pt"
                    android:background="@color/color_F5F7F8"
                    android:gravity="center_vertical"
                    android:paddingLeft="30pt"
                    android:text="@string/personal_title2"
                    android:textColor="@color/common_text_1d2129"
                    android:textSize="28pt"
                    app:layout_constraintTop_toBottomOf="@id/qrCodeBg" />

                <View
                    android:id="@+id/RelationshipBg"
                    android:layout_width="match_parent"
                    android:layout_height="107pt"
                    android:background="@color/color_FFFFFF"
                    app:layout_constraintTop_toBottomOf="@id/title2" />

                <TextView
                    android:id="@+id/RelationshipHint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:text="@string/personal_relationship"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/RelationshipBg"
                    app:layout_constraintLeft_toLeftOf="@id/RelationshipBg"
                    app:layout_constraintTop_toTopOf="@id/RelationshipBg" />

                <TextView
                    android:id="@+id/RelationshipTv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:contentDescription="@string/common_app_name"
                    android:gravity="right"
                    android:textColor="@color/color_404040"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/RelationshipBg"
                    app:layout_constraintLeft_toRightOf="@id/RelationshipHint"
                    app:layout_constraintRight_toRightOf="@id/RelationshipBg"
                    app:layout_constraintTop_toTopOf="@id/RelationshipBg"
                    tools:text="dahaksdksadhasjkdhkjasdhkjas" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1pt"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:background="@color/color_E5E5E5"
                    app:layout_constraintBottom_toBottomOf="@id/RelationshipBg" />

                <View
                    android:id="@+id/NameBg"
                    android:layout_width="match_parent"
                    android:layout_height="107pt"
                    android:background="@color/color_FFFFFF"
                    app:layout_constraintTop_toBottomOf="@id/RelationshipBg" />

                <TextView
                    android:id="@+id/NameHint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:text="@string/name"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/NameBg"
                    app:layout_constraintLeft_toLeftOf="@id/NameBg"
                    app:layout_constraintTop_toTopOf="@id/NameBg" />

                <TextView
                    android:id="@+id/NameTv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:contentDescription="@string/common_app_name"
                    android:gravity="right"
                    android:textColor="@color/color_404040"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/NameBg"
                    app:layout_constraintLeft_toRightOf="@id/NameHint"
                    app:layout_constraintRight_toRightOf="@id/NameBg"
                    app:layout_constraintTop_toTopOf="@id/NameBg"
                    tools:text="dahaksdksadhasjkdhkjasdhkjas" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1pt"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:background="@color/color_E5E5E5"
                    app:layout_constraintBottom_toBottomOf="@id/NameBg" />

                <View
                    android:id="@+id/MobileBg"
                    android:layout_width="match_parent"
                    android:layout_height="107pt"
                    android:background="@color/color_FFFFFF"
                    app:layout_constraintTop_toBottomOf="@id/NameBg" />

                <TextView
                    android:id="@+id/MobileHint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:text="@string/personal_title_mobile"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/MobileBg"
                    app:layout_constraintLeft_toLeftOf="@id/MobileBg"
                    app:layout_constraintTop_toTopOf="@id/MobileBg" />

                <TextView
                    android:id="@+id/MobileTv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:contentDescription="@string/common_app_name"
                    android:gravity="right"
                    android:textColor="@color/color_404040"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/MobileBg"
                    app:layout_constraintLeft_toRightOf="@id/MobileHint"
                    app:layout_constraintRight_toRightOf="@id/MobileBg"
                    app:layout_constraintTop_toTopOf="@id/MobileBg"
                    tools:text="dahaksdksadhasjkdhkjasdhkjas" />

                <View
                    android:layout_width="match_parent"
                    android:layout_height="1pt"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:background="@color/color_E5E5E5"
                    app:layout_constraintBottom_toBottomOf="@id/MobileBg" />

                <View
                    android:id="@+id/LinkBg"
                    android:layout_width="match_parent"
                    android:layout_height="107pt"
                    android:background="@color/color_FFFFFF"
                    app:layout_constraintTop_toBottomOf="@id/MobileBg" />

                <TextView
                    android:id="@+id/LinkHint"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:text="@string/personal_link_date"
                    android:textColor="@color/color_9B9B9B"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/LinkBg"
                    app:layout_constraintLeft_toLeftOf="@id/LinkBg"
                    app:layout_constraintTop_toTopOf="@id/LinkBg" />

                <TextView
                    android:id="@+id/LinkTv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="30pt"
                    android:layout_marginRight="30pt"
                    android:contentDescription="@string/common_app_name"
                    android:gravity="right"
                    android:textColor="@color/color_404040"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/LinkBg"
                    app:layout_constraintLeft_toRightOf="@id/LinkHint"
                    app:layout_constraintRight_toRightOf="@id/LinkBg"
                    app:layout_constraintTop_toTopOf="@id/LinkBg"
                    tools:text="dahaksdksadhasjkdhkjasdhkjas" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>