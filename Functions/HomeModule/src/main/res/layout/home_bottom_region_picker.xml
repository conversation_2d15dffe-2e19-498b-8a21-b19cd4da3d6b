<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/color_FFFFFF"
    android:orientation="vertical">

    <com.cncoderx.wheelview.WheelView
        android:id="@+id/regionWv"
        android:layout_width="300pt"
        android:layout_height="wrap_content"
        android:layout_marginTop="50pt"
        app:layout_constraintHorizontal_chainStyle="packed"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:lineSpace="104pt"
        app:selectedColor="@color/color_131313"
        app:textSize="32pt"
        app:toward="right"
        app:unselectedColor="@color/color_707070"
        app:visibleItems="3" />

    <View
        android:id="@+id/linear_pass"
        android:layout_width="match_parent"
        android:layout_height="1pt"
        android:layout_marginTop="50pt"
        android:background="@color/color_B7B7B7"
        app:layout_constraintTop_toBottomOf="@id/regionWv" />

    <TextView
        android:id="@+id/cancelTv"
        android:layout_width="0dp"
        android:layout_height="100pt"
        android:gravity="center"
        android:text="@string/common_btn_cancel"
        android:textColor="@color/color_B7B7B7"
        android:textSize="36pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/line"
        app:layout_constraintTop_toBottomOf="@id/linear_pass" />

    <View
        android:id="@+id/line"
        android:layout_width="1pt"
        android:layout_height="100pt"
        android:background="@color/color_B7B7B7"
        app:layout_constraintLeft_toRightOf="@id/cancelTv"
        app:layout_constraintRight_toLeftOf="@id/okTv"
        app:layout_constraintTop_toBottomOf="@id/linear_pass" />

    <TextView
        android:id="@+id/okTv"
        android:layout_width="0dp"
        android:layout_height="100pt"
        android:gravity="center"
        android:text="@string/common_alert_ok"
        android:textColor="@color/common_ye_F3881E"
        android:textSize="36pt"
        app:layout_constraintLeft_toRightOf="@id/line"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/linear_pass" />
</androidx.constraintlayout.widget.ConstraintLayout>