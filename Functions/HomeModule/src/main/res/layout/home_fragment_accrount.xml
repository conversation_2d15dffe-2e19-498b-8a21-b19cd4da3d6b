<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/accountView"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:id="@+id/statusView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:background="@color/common_ye_F3881E"
        app:layout_constraintBottom_toTopOf="@id/titleTv"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/titleTv"
        android:layout_width="match_parent"
        android:layout_height="90pt"
        android:background="@color/common_ye_F3881E"
        android:gravity="center"
        android:textColor="@color/color_FFFFFF"
        android:textSize="36pt"
        android:textStyle="bold"
        app:layout_constraintTop_toBottomOf="@id/statusView" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toBottomOf="@id/titleTv">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/topBg"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/common_ye_F3881E"
                android:paddingBottom="60pt"
                app:layout_constraintTop_toTopOf="parent">

                <de.hdodenhof.circleimageview.CircleImageView
                    android:id="@+id/headIv"
                    android:layout_width="120pt"
                    android:layout_height="120pt"
                    android:layout_marginLeft="30pt"
                    android:layout_marginTop="10pt"
                    android:src="@drawable/ic_contact_head"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:layout_width="120pt"
                    android:layout_height="36pt"
                    android:background="@drawable/home_ic_head_bottom_edit_bg"
                    android:gravity="center"
                    android:text="@string/account_btn_edit"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="22pt"
                    app:layout_constraintBottom_toBottomOf="@id/headIv"
                    app:layout_constraintLeft_toLeftOf="@id/headIv"
                    app:layout_constraintRight_toRightOf="@id/headIv" />

                <ImageView
                    android:id="@+id/codeIv"
                    android:layout_width="48pt"
                    android:layout_height="43pt"
                    android:layout_marginRight="30pt"
                    android:contentDescription="@string/common_app_name"
                    android:src="@drawable/home_ic_account_code"
                    app:layout_constraintBottom_toBottomOf="@id/headIv"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@id/headIv" />

                <TextView
                    android:id="@+id/userNameTv"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20pt"
                    android:layout_marginRight="30pt"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:singleLine="true"
                    android:textColor="@color/color_FFFFFF"
                    android:textSize="34pt"
                    android:textStyle="bold"
                    app:layout_constraintBottom_toTopOf="@id/cardIdTv"
                    app:layout_constraintLeft_toRightOf="@id/headIv"
                    app:layout_constraintRight_toLeftOf="@id/codeIv"
                    app:layout_constraintTop_toTopOf="@id/headIv"
                    tools:text="David GarciaDavid GarciaDavid Garcia" />

                <TextView
                    android:id="@+id/cardIdTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20pt"
                    android:textColor="@color/color_FFFFFF"
                    android:paddingLeft="12pt"
                    android:paddingRight="12pt"
                    android:paddingTop="6pt"
                    android:paddingBottom="6pt"
                    android:textSize="22pt"
                    android:background="@drawable/home_drawable_background"
                    app:layout_constraintBottom_toBottomOf="@id/headIv"
                    app:layout_constraintLeft_toRightOf="@id/headIv"
                    app:layout_constraintTop_toBottomOf="@id/userNameTv"
                    tools:text="99****118" />

                <TextView
                    android:id="@+id/tv_junior"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20pt"
                    android:textColor="@color/color_FFFFFF"
                    android:paddingLeft="12pt"
                    android:paddingRight="12pt"
                    android:paddingTop="6pt"
                    android:paddingBottom="6pt"
                    android:textSize="22pt"
                    android:background="@drawable/home_drawable_db7007"
                    app:layout_constraintBottom_toBottomOf="@id/cardIdTv"
                    app:layout_constraintLeft_toRightOf="@id/cardIdTv"
                    android:text="@string/account_type_junior" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/accountVp2"
                android:layout_width="match_parent"
                android:layout_height="370pt"
                android:layout_marginTop="40pt"
                app:layout_constraintTop_toBottomOf="@+id/topBg"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                />

<!--            <View-->
<!--                android:id="@+id/topUpBg"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="100pt"-->
<!--                android:layout_marginTop="40pt"-->
<!--                app:layout_constraintTop_toBottomOf="@id/accountVp2" />-->

<!--            <ImageView-->
<!--                android:id="@+id/topUpIv"-->
<!--                android:layout_width="56pt"-->
<!--                android:layout_height="56pt"-->
<!--                android:layout_marginLeft="40pt"-->
<!--                android:contentDescription="@string/base_app_name"-->
<!--                android:src="@drawable/home_ic_account_topup"-->
<!--                app:layout_constraintBottom_toBottomOf="@id/topUpBg"-->
<!--                app:layout_constraintLeft_toLeftOf="parent"-->
<!--                app:layout_constraintTop_toTopOf="@id/topUpBg" />-->

<!--            <TextView-->
<!--                android:id="@+id/topUpTv"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginLeft="20pt"-->
<!--                android:layout_marginRight="30pt"-->
<!--                android:ellipsize="end"-->
<!--                android:text="@string/account_btn_top_up"-->
<!--                android:textColor="@color/color_404040"-->
<!--                android:textSize="32pt"-->
<!--                app:layout_constraintBottom_toBottomOf="@id/topUpIv"-->
<!--                app:layout_constraintLeft_toRightOf="@id/topUpIv"-->
<!--                app:layout_constraintRight_toRightOf="parent"-->
<!--                app:layout_constraintTop_toTopOf="@id/topUpIv" />-->


<!--            <View-->
<!--                android:id="@+id/withdrawalBg"-->
<!--                android:layout_width="match_parent"-->
<!--                android:layout_height="100pt"-->
<!--                android:layout_marginTop="15pt"-->
<!--                app:layout_constraintTop_toBottomOf="@id/topUpBg" />-->

<!--            <ImageView-->
<!--                android:id="@+id/withdrawalIv"-->
<!--                android:layout_width="56pt"-->
<!--                android:layout_height="56pt"-->
<!--                android:layout_marginLeft="40pt"-->
<!--                android:contentDescription="@string/base_app_name"-->
<!--                android:src="@drawable/home_ic_account_withdrawal"-->
<!--                app:layout_constraintBottom_toBottomOf="@id/withdrawalBg"-->
<!--                app:layout_constraintLeft_toLeftOf="parent"-->
<!--                app:layout_constraintTop_toTopOf="@id/withdrawalBg" />-->

<!--            <TextView-->
<!--                android:id="@+id/withdrawalBgTv"-->
<!--                android:layout_width="0dp"-->
<!--                android:layout_height="wrap_content"-->
<!--                android:layout_marginLeft="20pt"-->
<!--                android:layout_marginRight="30pt"-->
<!--                android:ellipsize="end"-->
<!--                android:text="@string/account_btn_withdrawal"-->
<!--                android:textColor="@color/color_404040"-->
<!--                android:textSize="32pt"-->
<!--                app:layout_constraintBottom_toBottomOf="@id/withdrawalIv"-->
<!--                app:layout_constraintLeft_toRightOf="@id/withdrawalIv"-->
<!--                app:layout_constraintRight_toRightOf="parent"-->
<!--                app:layout_constraintTop_toTopOf="@id/withdrawalIv" />-->


            <View
                android:id="@+id/bankCardBg"
                android:layout_width="match_parent"
                android:layout_height="56pt"
                android:layout_marginTop="32pt"
                app:layout_constraintTop_toBottomOf="@id/accountVp2" />

            <ImageView
                android:id="@+id/bankCardIv"
                android:layout_width="48pt"
                android:layout_height="48pt"
                android:layout_marginLeft="40pt"
                android:contentDescription="@string/base_app_name"
                android:src="@drawable/home_ic_bank_card"
                app:tint="@color/common_ye_F3881E"
                app:layout_constraintBottom_toBottomOf="@id/bankCardBg"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="@id/bankCardBg" />

            <TextView
                android:id="@+id/bankCardTv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20pt"
                android:layout_marginRight="30pt"
                android:ellipsize="end"
                android:text="@string/bank_Account_Management"
                android:textColor="@color/color_404040"
                android:textSize="32pt"
                app:layout_constraintBottom_toBottomOf="@id/bankCardIv"
                app:layout_constraintLeft_toRightOf="@id/bankCardIv"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/bankCardIv" />

            <View
                android:id="@+id/historyBg"
                android:layout_width="match_parent"
                android:layout_height="56pt"
                android:layout_marginTop="49pt"
                app:layout_constraintTop_toBottomOf="@id/bankCardBg" />

            <ImageView
                android:id="@+id/historyIv"
                android:layout_width="48pt"
                android:layout_height="48pt"
                android:layout_marginLeft="40pt"
                android:contentDescription="@string/base_app_name"
                android:src="@drawable/home_ic_account_history"
                app:tint="@color/common_ye_F3881E"
                app:layout_constraintBottom_toBottomOf="@id/historyBg"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="@id/historyBg" />

            <TextView
                android:id="@+id/historyTv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20pt"
                android:layout_marginRight="30pt"
                android:ellipsize="end"
                android:text="@string/statement_Enquiry"
                android:textColor="@color/color_404040"
                android:textSize="32pt"
                app:layout_constraintBottom_toBottomOf="@id/historyIv"
                app:layout_constraintLeft_toRightOf="@id/historyIv"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/historyIv" />

            <View
                android:id="@+id/loyalty_card_Bg"
                android:layout_width="match_parent"
                android:layout_height="56pt"
                android:layout_marginTop="49pt"
                app:layout_constraintTop_toBottomOf="@id/historyBg" />

            <ImageView
                android:id="@+id/loyalty_card_Iv"
                android:layout_width="48pt"
                android:layout_height="48pt"
                android:layout_marginLeft="40pt"
                android:contentDescription="@string/base_app_name"
                android:src="@drawable/icon_loyaltyrewards"
                app:tint="@color/common_ye_F3881E"
                app:layout_constraintBottom_toBottomOf="@id/loyalty_card_Bg"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="@id/loyalty_card_Bg" />

            <TextView
                android:id="@+id/loyalty_card_Tv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20pt"
                android:layout_marginRight="30pt"
                android:ellipsize="end"
                android:text="@string/account_btn_loyalty_rewards"
                android:textColor="@color/color_404040"
                android:textSize="32pt"
                app:layout_constraintBottom_toBottomOf="@id/loyalty_card_Iv"
                app:layout_constraintLeft_toRightOf="@id/loyalty_card_Iv"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/loyalty_card_Iv" />


            <View
                android:id="@+id/securityBg"
                android:layout_width="match_parent"
                android:layout_height="56pt"
                android:layout_marginTop="49pt"
                app:layout_constraintTop_toBottomOf="@id/loyalty_card_Bg" />

            <ImageView
                android:id="@+id/securityIv"
                android:layout_width="48pt"
                android:layout_height="48pt"
                android:layout_marginLeft="40pt"
                android:contentDescription="@string/base_app_name"
                android:src="@drawable/ic_security"
                app:tint="@color/common_ye_F3881E"
                app:layout_constraintBottom_toBottomOf="@id/securityBg"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="@id/securityBg" />

            <TextView
                android:id="@+id/securityTv"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20pt"
                android:layout_marginRight="30pt"
                android:ellipsize="end"
                android:text="@string/security_center"
                android:textColor="@color/color_404040"
                android:textSize="32pt"
                app:layout_constraintBottom_toBottomOf="@id/securityIv"
                app:layout_constraintLeft_toRightOf="@id/securityIv"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/securityIv" />

            <View
                android:id="@+id/inviteBg"
                android:layout_width="match_parent"
                android:layout_height="56pt"
                android:layout_marginTop="49pt"
                app:layout_constraintTop_toBottomOf="@id/securityBg" />

            <ImageView
                android:id="@+id/inviteIv"
                android:layout_width="48pt"
                android:layout_height="48pt"
                android:layout_marginLeft="40pt"
                android:contentDescription="@string/base_app_name"
                android:src="@drawable/home_ic_account_invite"
                app:tint="@color/common_ye_F3881E"
                app:layout_constraintBottom_toBottomOf="@id/inviteBg"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="@id/inviteBg" />

            <TextView
                android:id="@+id/inviteTv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20pt"
                android:layout_marginRight="30pt"
                android:ellipsize="end"
                android:text="@string/account_btn_invite_friends"
                android:textColor="@color/color_404040"
                android:textSize="32pt"
                app:layout_constraintBottom_toBottomOf="@id/inviteIv"
                app:layout_constraintLeft_toRightOf="@id/inviteIv"
                app:layout_constraintTop_toTopOf="@id/inviteIv" />

            <TextView
                android:id="@+id/rewardTv"
                android:visibility="gone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="10pt"
                android:background="@drawable/home_drawable_red_bg"
                android:gravity="center"
                android:minWidth="74pt"
                android:paddingLeft="5pt"
                android:paddingTop="2pt"
                android:paddingRight="5pt"
                android:paddingBottom="2pt"
                android:text="@string/account_label_reward"
                android:textColor="@color/color_FFFFFF"
                android:textSize="16pt"
                app:layout_constraintBottom_toBottomOf="@id/inviteIv"
                app:layout_constraintLeft_toRightOf="@id/inviteTv"
                app:layout_constraintTop_toTopOf="@id/inviteIv" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.core.widget.NestedScrollView>
</androidx.constraintlayout.widget.ConstraintLayout>