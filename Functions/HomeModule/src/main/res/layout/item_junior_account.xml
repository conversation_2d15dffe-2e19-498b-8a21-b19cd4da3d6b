<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

    </data>

    <RelativeLayout
        cornerBackgroundRadius="@{10}"
        android:layout_width="match_parent"
        android:layout_height="120pt"
        android:layout_marginLeft="30pt"
        android:layout_marginRight="30pt"
        android:paddingLeft="24pt"
        tools:ignore="ResourceName">

        <ImageView
            android:layout_centerVertical="true"
            android:layout_alignParentLeft="true"
            android:id="@+id/icon_iv"
            android:layout_width="78pt"
            android:layout_height="78pt" />

        <TextView
            android:layout_toRightOf="@id/icon_iv"
            android:id="@+id/title_tv"
            android:ellipsize="end"
            android:maxLines="1"
            android:singleLine="true"
            android:layout_centerVertical="true"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft="24pt"
            android:layout_marginRight="24pt"
            android:textColor="@color/common_text_1d2129"
            android:textSize="32pt" />

        <TextView
            android:layout_alignParentRight="true"
            android:id="@+id/text_tv"
            android:gravity="center"
            android:layout_width="122pt"
            android:layout_height="58pt"
            android:textSize="20pt" />
    </RelativeLayout>
</layout>