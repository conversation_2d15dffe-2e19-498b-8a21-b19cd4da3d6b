<?xml version="1.0" encoding="utf-8"?>
<layout>

    <data>

        <variable
            name="broadcastOpt"
            type="om.rrtx.mobile.homemodule.activity.BroadcastSettingActivity.BroadcastOpt" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".activity.BroadcastSettingActivity">

        <include
            android:id="@+id/include_title"
            layout="@layout/home_jetpack_base_title"
            app:titleClick="@{broadcastOpt}" />

        <androidx.cardview.widget.CardView
            android:id="@+id/touchIdCv"
            android:layout_width="690pt"
            android:layout_height="100pt"
            android:layout_marginTop="40pt"
            app:cardCornerRadius="10pt"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/include_title">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent">

                <ImageView
                    android:id="@+id/touchIdIv"
                    android:layout_width="48pt"
                    android:layout_height="48pt"
                    android:src="@drawable/home_ic_transfer"
                    android:layout_marginLeft="30pt"
                    android:contentDescription="@string/base_app_name"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <TextView
                    android:id="@+id/touchIdTv"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginLeft="20pt"
                    android:text="@string/common_btn_fund_transfer"
                    android:textColor="@color/color_404040"
                    android:textSize="28pt"
                    app:layout_constraintBottom_toBottomOf="@id/touchIdIv"
                    app:layout_constraintLeft_toRightOf="@id/touchIdIv"
                    app:layout_constraintTop_toTopOf="@id/touchIdIv" />

                <Switch
                    android:id="@+id/touchIdSwitch"
                    android:layout_width="wrap_content"
                    android:layout_height="46pt"
                    android:layout_marginRight="20pt"
                    android:thumb="@drawable/home_switch_thumb"
                    android:track="@drawable/home_switch_track"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.cardview.widget.CardView>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
