<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.HomeActivity">
    <androidx.drawerlayout.widget.DrawerLayout
        android:id="@+id/homeDl"
        android:layout_width="match_parent"
        android:layout_height="match_parent">
        <!--整体布局-->
        <include layout="@layout/home_content_view" />

        <!--左侧布局-->
        <include layout="@layout/home_left_view"
            android:id="@+id/drawerLeft"/>
    </androidx.drawerlayout.widget.DrawerLayout>
    <View
        android:id="@+id/logoutV"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone"/>

</androidx.constraintlayout.widget.ConstraintLayout>