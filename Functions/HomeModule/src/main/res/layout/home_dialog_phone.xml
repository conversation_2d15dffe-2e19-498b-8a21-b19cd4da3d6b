<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/common_white_corner_16_bg"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">

    <TextView
        android:id="@+id/phoneTv"
        android:layout_width="690pt"
        android:layout_height="80pt"
        android:layout_marginTop="40pt"
        android:gravity="center"
        tools:text="@string/contact_label_service_phone"
        android:textColor="@color/common_ye_F3881E"
        android:textSize="32pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
    <View
        android:id="@+id/line"
        android:layout_width="match_parent"
        android:layout_height="1pt"
        android:background="@color/color_E5E6EB"
        app:layout_constraintTop_toBottomOf="@id/phoneTv" />

    <TextView
        android:id="@+id/cancelTv"
        android:layout_width="690pt"
        android:layout_height="80pt"
        android:layout_marginTop="40pt"
        android:layout_marginBottom="40pt"
        android:gravity="center"
        android:text="@string/common_btn_cancel"
        android:textColor="@color/common_text_1d2129"
        android:textSize="32pt"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line" />
</androidx.constraintlayout.widget.ConstraintLayout>