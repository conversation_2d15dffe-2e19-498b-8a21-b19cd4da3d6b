<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".activity.UserInfoEditMobileActivity">

    <include
        android:id="@+id/include_title"
        layout="@layout/home_base_title" />

    <TextView
        android:id="@+id/hint"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="40pt"
        android:text="@string/personal_label_origin_mobile"
        android:textColor="@color/color_9B9B9B"
        android:textSize="26pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/include_title" />

    <TextView
        android:id="@+id/mobileTv"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="10pt"
        android:textColor="@color/color_000000"
        android:textSize="36pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/hint"
        tools:text="+6012****9999" />

    <TextView
        android:id="@+id/mobileTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="35pt"
        android:layout_marginTop="60pt"
        android:text="@string/personal_label_new_mobile"
        android:textColor="@color/color_AEB3C0"
        android:textSize="24pt"
        android:visibility="invisible"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mobileTv" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/leftCl"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginLeft="35pt"
        android:layout_marginTop="5pt"
        android:visibility="gone"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@id/mobileTitle">

        <TextView
            android:id="@+id/codeTv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:gravity="bottom"
            android:text="@string/common_label_60"
            android:textColor="@color/common_text_1d2129"
            android:textSize="32pt"
            android:textStyle="bold"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/icon"
            android:layout_width="16pt"
            android:layout_height="16pt"
            android:layout_marginLeft="5pt"
            android:contentDescription="@string/common_app_name"
            android:src="@drawable/home_ic_sanjiao_down"
            app:layout_constraintBottom_toBottomOf="@id/codeTv"
            app:layout_constraintLeft_toRightOf="@id/codeTv"
            app:layout_constraintTop_toTopOf="@id/codeTv"
            app:tint="@color/common_text_1d2129" />

        <View
            android:layout_width="1pt"
            android:layout_height="36pt"
            android:layout_marginLeft="11pt"
            android:background="@color/color_131313"
            app:layout_constraintBottom_toBottomOf="@id/codeTv"
            app:layout_constraintLeft_toRightOf="@id/icon"
            app:layout_constraintTop_toTopOf="@id/codeTv" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.google.android.material.textfield.TextInputEditText
        android:id="@+id/mobileEt"
        android:layout_width="0dp"
        android:layout_height="55pt"
        android:layout_marginLeft="35pt"
        android:layout_marginTop="5pt"
        android:layout_marginRight="30pt"
        android:background="@null"
        android:gravity="center_vertical"
        android:hint="@string/personal_label_new_mobile"
        android:inputType="number"
        android:maxLength="12"
        android:paddingRight="84pt"
        android:textColor="@color/color_131313"
        android:textSize="32pt"
        app:layout_constraintBottom_toBottomOf="@id/leftCl"
        app:layout_constraintLeft_toRightOf="@id/leftCl"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/leftCl" />

    <ImageView
        android:id="@+id/phoneIv"
        android:layout_width="84pt"
        android:layout_height="68pt"
        android:contentDescription="@string/base_app_name"
        android:paddingLeft="21pt"
        android:paddingTop="21pt"
        android:paddingRight="21pt"
        android:paddingBottom="5pt"
        android:src="@drawable/home_input_icon_close"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/mobileEt"
        app:layout_constraintRight_toRightOf="@+id/mobileEt" />

    <View
        android:id="@+id/line3"
        android:layout_width="match_parent"
        android:layout_height="3pt"
        android:layout_marginLeft="35pt"
        android:layout_marginRight="30pt"
        android:background="@color/color_AEB3C0"
        app:layout_constraintTop_toBottomOf="@id/mobileEt" />

    <TextView
        android:id="@+id/confirmTv"
        android:layout_width="690pt"
        android:layout_height="80pt"
        android:layout_marginTop="120pt"
        android:background="@drawable/common_unusable_btn"
        android:enabled="false"
        android:gravity="center"
        android:text="@string/common_btn_confirm"
        android:textColor="@color/color_FFFFFF"
        android:textSize="34pt"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/line3" />

</androidx.constraintlayout.widget.ConstraintLayout>