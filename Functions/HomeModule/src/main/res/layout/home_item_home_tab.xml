<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="210pt"
    android:layout_marginLeft="10pt"
    android:layout_marginRight="20pt"
    android:layout_height="210pt"
    android:layout_marginBottom="20pt"
    android:background="@drawable/home_drawable_tab_bg">

    <ImageView
        android:id="@+id/icon"
        android:layout_width="82pt"
        android:layout_height="82pt"
        android:scaleType="fitXY"
        android:layout_marginTop="32pt"
        android:layout_marginLeft="64pt"
        android:layout_marginRight="64pt"
        android:layout_centerHorizontal="true"
        android:contentDescription="@string/common_app_name" />

    <TextView
        android:id="@+id/titleTv"
        android:layout_centerHorizontal="true"
        android:layout_below="@id/icon"
        android:layout_marginTop="12pt"
        android:paddingLeft="10pt"
        android:paddingRight="10pt"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="@color/color_000000"
        android:textSize="22pt"
        android:gravity="center"
        android:fontFamily="sans-serif-condensed-medium"
        tools:text="fsdfsdfsdf" />

</RelativeLayout>