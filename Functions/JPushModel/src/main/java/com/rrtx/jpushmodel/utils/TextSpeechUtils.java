package com.rrtx.jpushmodel.utils;

import android.content.Context;
import android.os.Build;
import android.speech.tts.TextToSpeech;
import android.speech.tts.UtteranceProgressListener;
import android.util.Log;

import java.util.Locale;

/**
 * <AUTHOR> Angle
 * 创建时间 : 2019/1/25 15:09
 * 描述 : 原生语音播报类
 */
public class TextSpeechUtils extends UtteranceProgressListener {
    private static final String TAG = TextSpeechUtils.class.getSimpleName();
    private Context mContext;
    private static TextSpeechUtils singleton;
    /**
     * 系统语音播报类
     */
    private TextToSpeech mTextToSpeech;

    private TextSpeechUtils() {

    }

    public static TextSpeechUtils getInstance() {
        if (singleton == null) {
            synchronized (TextSpeechUtils.class) {
                if (singleton == null) {
                    singleton = new TextSpeechUtils();
                }
            }
        }
        return singleton;
    }


    public void init(Context context) {
        this.mContext = context.getApplicationContext();
        mTextToSpeech = new TextToSpeech(mContext, new TextToSpeech.OnInitListener() {
            @Override
            public void onInit(int i) {
                LogUtil.e(TAG, "onInit: " + i);
                //系统语音初始化成功
                if (i == TextToSpeech.SUCCESS) {
                    // 设置音调，值越大声音越尖（女生），值越小则变成男声,1.0是常规
                    mTextToSpeech.setPitch(1.0f);
                    mTextToSpeech.setSpeechRate(1.0f);
                    //语音播报得回调
                    mTextToSpeech.setOnUtteranceProgressListener(TextSpeechUtils.this);
                }
            }
        });
    }

    public void playText(String playText, Locale locale) {
        if (mTextToSpeech != null) {
            int result = mTextToSpeech.setLanguage(locale);
            if (result == TextToSpeech.LANG_MISSING_DATA || result == TextToSpeech.LANG_NOT_SUPPORTED) {
                //系统不支持中文播报
                LogUtil.e(TAG, "不支持");
                return;
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                mTextToSpeech.speak(playText, TextToSpeech.QUEUE_ADD, null, null);
            } else {
                mTextToSpeech.speak(playText, TextToSpeech.QUEUE_ADD, null);
            }
        } else {
            LogUtil.e(TAG, "playText: 请先进行初始化操作");
        }
    }

    public void stopSpeak() {
        if (mTextToSpeech != null) {
            mTextToSpeech.stop();
        }
    }

    @Override
    public void onStart(String utteranceId) {
        LogUtil.e(TAG, "onStart: 语音播报开始");
    }

    @Override
    public void onDone(String utteranceId) {
        LogUtil.e(TAG, "onDone: 语音播报正在执行");
    }

    @Override
    public void onError(String utteranceId) {
        LogUtil.e(TAG, "onError: 语音播报错误");
    }
}
