package com.rrtx.jpushmodel.receiver;

import android.content.Context;
import android.content.Intent;
import android.text.TextUtils;
import android.util.Log;


import com.alibaba.android.arouter.launcher.ARouter;
import com.kapp.xmarketing.ShowMarketingManager;
import com.rrtx.jpushmodel.utils.LogUtil;
import com.rrtx.jpushmodel.utils.TagAliasOperatorHelper;
import com.rrtx.jpushmodel.utils.TextSpeechUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Locale;

import cn.jpush.android.api.CmdMessage;
import cn.jpush.android.api.CustomMessage;
import cn.jpush.android.api.JPushInterface;
import cn.jpush.android.api.JPushMessage;
import cn.jpush.android.api.NotificationMessage;
import cn.jpush.android.service.JPushMessageReceiver;
import om.rrtx.mobile.functionapi.ARouterPath;
import om.rrtx.mobile.rrtxcommon1.BaseApp;
import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.utils.ActivityController;
import om.rrtx.mobile.rrtxcommon1.utils.LocaleManager;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR>
 * 极光推送的设置
 */
public class JPushReceiver extends JPushMessageReceiver {

    private static final String TAG = "PushMessageReceiver";

    @Override
    public void onMessage(Context context, CustomMessage customMessage) {
        LogUtil.e(TAG, "[onMessage] " + customMessage);
        processCustomMessage(context, customMessage);
    }

    @Override
    public void onNotifyMessageOpened(Context context, NotificationMessage message) {
        LogUtil.e(TAG, "[onNotifyMessageOpened] " + message);
        if (ActivityController.getInstance().getStackActivitySize() > 0) {
            String userName = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.USERNAME, "");
            String authorization = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.AUTHORIZATION, "");
            Log.e("JPushReceiver>>>", "zfw onNotifyMessageOpened>>> 收到的消息内容:"+ message.notificationExtras);
//            //打开的时候保存相应的信息,这里只会用在推送中见异常的时候
            SharedPreferencesUtils.setParam(context, BaseConstants.SaveParameter.JPUSHDATA, message.notificationExtras);

            if (TextUtils.isEmpty(userName)) {
                Log.e("JPushReceiver>>>", "zfw onNotifyMessageOpened>>> 跳转页面:"+ARouterPath.LoginPath.LoginActivity );
                ARouter.getInstance().build(ARouterPath.LoginPath.LoginActivity)
                        .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.JPUSHJUMP)
                        .withString(BaseConstants.Transmit.JPUSHJSON, message.notificationExtras)
                        .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                        .navigation();
            } else {
                //用户名不为空
                if (!TextUtils.isEmpty(authorization)) {
                    jumpHandler(message, context);
                } else {
                    boolean isFinger = (boolean) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.ISFINGER, false);
                    if (isFinger) {
                        Log.e("JPushReceiver>>>", "zfw onNotifyMessageOpened>>> 跳转页面:"+ARouterPath.SecurityPath.LoginFingerLockActivity );
                        ARouter.getInstance().build(ARouterPath.SecurityPath.LoginFingerLockActivity)
                                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.JPUSHJUMP)
                                .withString(BaseConstants.Transmit.JPUSHJSON, message.notificationExtras)
                                .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withBoolean(BaseConstants.Transmit.ISPSDBACK, false)
                                .navigation();
                    } else {
                        Log.e("JPushReceiver>>>", "zfw onNotifyMessageOpened>>> 跳转页面:"+ARouterPath.SecurityPath.LoginPasswordLockActivity );
                        ARouter.getInstance().build(ARouterPath.SecurityPath.LoginPasswordLockActivity)
                                .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.JPUSHJUMP)
                                .withString(BaseConstants.Transmit.JPUSHJSON, message.notificationExtras)
                                .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withBoolean(BaseConstants.Transmit.ISPSDBACK, false)
                                .navigation();
                    }
                }
            }
        } else {
            Log.e("JPushReceiver>>>", "zfw onNotifyMessageOpened>>> 跳转页面:"+ARouterPath.LoginPath.SplashActivity );
            //应用没有启动
            ARouter.getInstance().build(ARouterPath.LoginPath.SplashActivity)
                    .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.JPUSHJUMP)
                    .withString(BaseConstants.Transmit.JPUSHJSON, message.notificationExtras)
                    .withFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
                    .navigation();
        }
    }

    private void jumpHandler(NotificationMessage message, Context context) {
        String extras = message.notificationExtras;
        try {
            JSONObject jsonObject = new JSONObject(extras);
            //目标页面
            String target = jsonObject.optString("target");
            String[] targetSplit = target.split("/");
            if (targetSplit.length < 2) {
                return;
            }
            if (TextUtils.equals(targetSplit[1], BaseConstants.MessageType.HOME)) {
                //跳转到首页
                ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                        .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                        .navigation();
                SharedPreferencesUtils.setParam(context, BaseConstants.SaveParameter.JPUSHDATA, "");
            } else if (TextUtils.equals(targetSplit[1], BaseConstants.MessageType.TRANSACTION)) {
                //转账类型
                if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONLIST)) {
                    //历史账单列表
                    ARouter.getInstance().build(ARouterPath.TransferPath.HistoryActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .navigation();
                    SharedPreferencesUtils.setParam(context, BaseConstants.SaveParameter.JPUSHDATA, "");
                } else if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONDETAIL)) {
                    String orderId = jsonObject.optString(BaseConstants.Transmit.ORDERID);
                    String transType = jsonObject.optString(BaseConstants.Transmit.ORDERTYPE);

                    if (TextUtils.equals(transType, BaseConstants.HistoryType.PAYTYPE)) {
                        ARouter.getInstance().build(ARouterPath.TransferPath.HistoryPaymentActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    } else if (TextUtils.equals(transType, BaseConstants.HistoryType.WITHDRAWALTYPE)) {
                        ARouter.getInstance().build(ARouterPath.TransferPath.WithdrawalTypeActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    } else if (TextUtils.equals(transType, BaseConstants.HistoryType.AGENTTOPUP)||TextUtils.equals(transType, BaseConstants.HistoryType.AGENTWITH)) {
                        ARouter.getInstance().build(ARouterPath.TransferPath.AgentTopUpActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    } else if (TextUtils.equals(transType, BaseConstants.HistoryType.AGENTTRAN)) {
                        ARouter.getInstance().build(ARouterPath.TransferPath.HistoryAgentTranActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    } else if (TextUtils.equals(transType, BaseConstants.HistoryType.AGENTMOBILE)) {
                        ARouter.getInstance().build(ARouterPath.TransferPath.HistoryAgentMobileActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    } else {
                        ARouter.getInstance().build(ARouterPath.TransferPath.HistoryDetailsActivity)
                                .withFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                                .withString(BaseConstants.Transmit.ORDERID, orderId)
                                .withString(BaseConstants.Transmit.ORDERTYPE, transType)
                                .navigation();
                    }
                } else if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONSPILTRECLIST)) {
                    //AA我发起的列表
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEAALAUNCH)
                            .navigation();
                    SharedPreferencesUtils.setParam(context, BaseConstants.SaveParameter.JPUSHDATA, "");
                } else if (TextUtils.equals(targetSplit[2], BaseConstants.MessageType.TRANSACTIONSPILTPAYLIST)) {
                    //AA我支付的列表
                    ARouter.getInstance().build(ARouterPath.HomePath.HomeActivity)
                            .withFlags(Intent.FLAG_ACTIVITY_SINGLE_TOP | Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK)
                            .withString(BaseConstants.Transmit.JUMPFLAG, BaseConstants.JumpFlag.HOMEAARECEIVED)
                            .navigation();
                    SharedPreferencesUtils.setParam(context, BaseConstants.SaveParameter.JPUSHDATA, "");
                }else if (targetSplit[2].contains(BaseConstants.MessageType.SENDMONEY)) {
                    //2 转账
                    ARouter.getInstance().build(ARouterPath.TransferPath.TransferActivity).navigation();
                }else if (targetSplit[2].contains(BaseConstants.MessageType.LR)) {
                    //3 奖励
                    String userName = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.USERNAME, "");
                    String realName = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.REALNAME, "");
                    String userMobile = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.USERMOBILE, "");
                    String userCardId = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.USERID, "");
                    String currencyList = (String) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.ACTIVATE_CURRENCY, "");
                    Log.e("sdk=","userName="+userName+"=realName="+realName+"=userMobile="+userMobile+"=userCardId="+userCardId+"=currencyList="+currencyList);
                    ShowMarketingManager.getInstance().jumpCardWalletActivity(userCardId, realName, userName, userMobile, "",currencyList);
                }else if (targetSplit[2].contains(BaseConstants.MessageType.BUY_AB)) {
                    //4 话费流量充值
                    ARouter.getInstance().build(ARouterPath.Payment.BuyAirtimeActivity).navigation();
                }else if (targetSplit[2].contains(BaseConstants.MessageType.ZESA)) {
                    //5 电力缴费
                    ARouter.getInstance().build(ARouterPath.Payment.BillPayTypeActivity).navigation();
                }else if (targetSplit[2].contains(BaseConstants.MessageType.INVITE_FRIENDS)) {
                    //6 邀请好友
                    ARouter.getInstance().build(ARouterPath.HomePath.ShowInviteActivity).navigation();
                }else if (targetSplit[2].contains(BaseConstants.MessageType.JUNIOR_ACCOUNT)) {
                    //7 亲子账户
                    ARouter.getInstance().build(ARouterPath.HomePath.JuniorAccountManagementActivity).navigation();
                }
            }
        } catch (JSONException e) {
            e.printStackTrace();
        }
    }

    @Override
    public void onMultiActionClicked(Context context, Intent intent) {
        LogUtil.e(TAG, "[onMultiActionClicked] 用户点击了通知栏按钮");
        String nActionExtra = intent.getExtras().getString(JPushInterface.EXTRA_NOTIFICATION_ACTION_EXTRA);

        //开发者根据不同 Action 携带的 extra 字段来分配不同的动作。
        if (nActionExtra == null) {
            LogUtil.e(TAG, "ACTION_NOTIFICATION_CLICK_ACTION nActionExtra is null");
            return;
        }
        if (nActionExtra.equals("my_extra1")) {
            LogUtil.e(TAG, "[onMultiActionClicked] 用户点击通知栏按钮一");
        } else if (nActionExtra.equals("my_extra2")) {
            LogUtil.e(TAG, "[onMultiActionClicked] 用户点击通知栏按钮二");
        } else if (nActionExtra.equals("my_extra3")) {
            LogUtil.e(TAG, "[onMultiActionClicked] 用户点击通知栏按钮三");
        } else {
            LogUtil.e(TAG, "[onMultiActionClicked] 用户点击通知栏按钮未定义");
        }
    }

    @Override
    public void onNotifyMessageArrived(Context context, NotificationMessage message) {
        //收到推送
        //{notificationId=537002892, msgId='67554076974774382', appkey='9c1a277f1244f07233947c11',
        // notificationContent='10.00元', notificationAlertType=7, notificationTitle='转账', notificationSmallIcon='',
        // notificationLargeIcon='', notificationExtras='{}', notificationStyle=0, notificationBuilderId=0,
        // notificationBigText='', notificationBigPicPath='', notificationInbox='', notificationPriority=0,
        // notificationCategory='', developerArg0='', platform=0, notificationChannelId='', displayForeground='',
        // notificationType=0', inAppIntent=', inAppMsgContent=', inAppMsgType=1', inAppMsgShowType=2', inAppMsgShowPos=0',
        // inAppMsgShowAniAction=0', inAppMsgDismissAniAction=0', inAppMsgShowBackground=0', inAppMsgAllowIntercept= 1',
        // inAppMsgFilterMsg=0', inAppMsgPicPath=', inAppMsgCountLmt=5', inAppMsgGap=1800', inAppMsgToUser=0',
        // inAppMsgDelayDisplayTime=10', inAppMsgBackgroundColor=#FFFFFFFF', inAppMsgTitleColor=#FF000000',
        // inAppMsgContentColor=#FF000000', inAppMsgTitleSize=14', inAppMsgContentSize=14', inAppMsgCircularSize=9',
        // inAppMsgShowTime=5', inAppMsgShowElapseTime=0.5',
        // inAppMsgDismissElapseTime=0.5, inAppMsgTitle=, inAppMsgContentBody=}
        LogUtil.e(TAG, "[onNotifyMessageArrived] " + message);

        boolean isBroadcast = (boolean) SharedPreferencesUtils.getParam(context, BaseConstants.SaveParameter.ISBROADCAST, false);
        if (!isBroadcast) {
            return;
        }
        String extras = message.notificationExtras;
        try {
            JSONObject jsonObject = new JSONObject(extras);
            //目标页面
            String textToSpeechFlag = jsonObject.optString("textToSpeechFlag");

            if (TextUtils.equals(textToSpeechFlag, "0")) {
                //如果是0的话,不播报.如果是1的情况下播报
                return;
            }

            //获取推送消息
            String content = jsonObject.optString("speechContent");
            LogUtil.e(TAG, "onNotifyMessageArrived: " + content);
            if (TextUtils.isEmpty(content)) {
                return;
            }

            String currentLocale = LocaleManager.getInstance().getLanguage(BaseApp.getAPPContext());
            if (TextUtils.equals(currentLocale, LocaleManager.LANGUAGE_CHINA)) {
                //中文
                LogUtil.e(TAG, "onNotifyMessageArrived: 中文" );
                TextSpeechUtils.getInstance().playText(content, Locale.CHINA);
            } else {
                //英语
                LogUtil.e(TAG, "onNotifyMessageArrived: 英文" );
                TextSpeechUtils.getInstance().playText(content, Locale.ENGLISH);
            }
        } catch (Exception e) {
            LogUtil.e(TAG, "onNotifyMessageArrived: 解析json异常");
        }
    }

    @Override
    public void onNotifyMessageDismiss(Context context, NotificationMessage message) {
        LogUtil.e(TAG, "[onNotifyMessageDismiss] " + message);
    }

    @Override
    public void onRegister(Context context, String registrationId) {
        LogUtil.e(TAG, "[onRegister] " + registrationId);
    }

    @Override
    public void onConnected(Context context, boolean isConnected) {
        LogUtil.e(TAG, "[onConnected] " + isConnected);
    }

    @Override
    public void onCommandResult(Context context, CmdMessage cmdMessage) {
        LogUtil.e(TAG, "[onCommandResult] " + cmdMessage);
    }

    @Override
    public void onTagOperatorResult(Context context, JPushMessage jPushMessage) {
        TagAliasOperatorHelper.getInstance().onTagOperatorResult(context, jPushMessage);
        super.onTagOperatorResult(context, jPushMessage);
    }

    @Override
    public void onCheckTagOperatorResult(Context context, JPushMessage jPushMessage) {
        TagAliasOperatorHelper.getInstance().onCheckTagOperatorResult(context, jPushMessage);
        super.onCheckTagOperatorResult(context, jPushMessage);
    }

    @Override
    public void onAliasOperatorResult(Context context, JPushMessage jPushMessage) {
        TagAliasOperatorHelper.getInstance().onAliasOperatorResult(context, jPushMessage);
        super.onAliasOperatorResult(context, jPushMessage);
    }

    @Override
    public void onMobileNumberOperatorResult(Context context, JPushMessage jPushMessage) {
        TagAliasOperatorHelper.getInstance().onMobileNumberOperatorResult(context, jPushMessage);
        super.onMobileNumberOperatorResult(context, jPushMessage);
    }

    //send msg to MainActivity
    private void processCustomMessage(Context context, CustomMessage customMessage) {
//        if (MainActivity.isForeground) {
//            String message = customMessage.message;
//            String extras = customMessage.extra;
//            Intent msgIntent = new Intent(MainActivity.MESSAGE_RECEIVED_ACTION);
//            msgIntent.putExtra(MainActivity.KEY_MESSAGE, message);
//            if (!ExampleUtil.isEmpty(extras)) {
//                try {
//                    JSONObject extraJson = new JSONObject(extras);
//                    if (extraJson.length() > 0) {
//                        msgIntent.putExtra(MainActivity.KEY_EXTRAS, extras);
//                    }
//                } catch (JSONException e) {
//
//                }
//
//            }
//            LocalBroadcastManager.getInstance(context).sendBroadcast(msgIntent);
//        }
    }

    @Override
    public void onNotificationSettingsCheck(Context context, boolean isOn, int source) {
        super.onNotificationSettingsCheck(context, isOn, source);
        LogUtil.e(TAG, "[onNotificationSettingsCheck] isOn:" + isOn + ",source:" + source);
    }
}
