apply plugin: 'com.android.library'
apply plugin: 'com.jakewharton.butterknife'

//配置相应的引入参数
def cfg = rootProject.ext.configuration // 配置
def libs = rootProject.ext.libraries // 库

android {
    compileSdkVersion cfg.compileVersion
    buildToolsVersion cfg.buildToolsVersion
    defaultConfig {
        minSdkVersion cfg.minSdk
        targetSdkVersion cfg.targetSdk
        versionCode cfg.version_code
        versionName cfg.version_name

        // 控制日志Log 输出打印
        buildConfigField("boolean", "enableLog", "true")
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [AROUTER_MODULE_NAME: project.getName()]
            }
        }
    }

    buildTypes {
        release {
            // 控制日志Log 输出打印
            buildConfigField("boolean", "enableLog", "false")
            consumerProguardFiles 'proguard-rules.pro'
        }
        debug {
            // 控制日志Log 输出打印
            buildConfigField("boolean", "enableLog", "false")
            consumerProguardFiles 'proguard-rules.pro'
        }
    }

    dataBinding {
        enabled true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation "androidx.appcompat:appcompat:${libs.androidx_appcompat}"
    implementation "androidx.constraintlayout:constraintlayout:${libs.androidx_constraintlayout}"

    //基础类库
    api project(path: ':BaseModule:RrtxCommon')
    //统一收银台接口
    api project(path: ':BaseModule:FunctionCommon')

    //arouter
    annotationProcessor "com.alibaba:arouter-compiler:${libs.arouter_compiler}"

    // 此处以JPush 3.7.0 版本为例。
    api 'cn.jiguang.sdk:jcore:2.4.2'
    // 此处以JCore 2.4.2 版本为例。
    api 'cn.jiguang.sdk:jpush:3.7.0'
}
