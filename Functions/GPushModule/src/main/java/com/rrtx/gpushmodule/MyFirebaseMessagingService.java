package com.rrtx.gpushmodule;

import android.util.Log;

import androidx.annotation.NonNull;

import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;

import om.rrtx.mobile.rrtxcommon1.BaseConstants;
import om.rrtx.mobile.rrtxcommon1.utils.SharedPreferencesUtils;

/**
 * <AUTHOR> zfw
 * @date : 2023/7/25 14:31
 * @week : 星期二
 * @desc :
 */
public class MyFirebaseMessagingService  extends FirebaseMessagingService {
    @Override
    public void onCreate() {
        super.onCreate();
        Log.e("MyFirebaseMessagingService>>>", "zfw onCreate>>> 消息服务已开启:" );
    }

    //获取到谷歌到token
    @Override
    public void onNewToken(@NonNull String token) {
        super.onNewToken(token);
        Log.e("Google token", "zfw Refreshed token: "+token);
        sendRegistrationToServer(token);

    }

    //  回传给服务器操作
    private void sendRegistrationToServer(String token) {
//
//        mToken = token;
//
//        //这里我做了本地保存，你可以在你需要到地方获取
//        ShareUtils.putString(getApplicationContext(), "GoogleToken", mToken);
        SharedPreferencesUtils.setParam(getApplicationContext(), BaseConstants.SaveParameter.DEVICETOKEN, token);
    }

    @Override
    public void onMessageReceived(@NonNull RemoteMessage message) {
        Log.e("MyFirebaseMessagingService>>>", "zfw onMessageReceived>>> message:"+message );
        super.onMessageReceived(message);



    }


}
